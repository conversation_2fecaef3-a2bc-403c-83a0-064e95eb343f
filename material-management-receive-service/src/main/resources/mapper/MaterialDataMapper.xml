<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress ALL -->
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper">
    <select id="wagonReceiveOverviewCard"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardDTO">
        select t1.category_id categoryId, t1.material_id materialId, t1.weight_unit unit,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive
        from d_material_data t1 where material_id is not null and is_deleted = 0 and weight_unit is not null and
        weight_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        <if test="receiveModes != null and receiveModes == 1">
            and contract_detail_id is not null
        </if>
        <if test="receiveModes != null and receiveModes == 2">
            and contract_detail_id is null
        </if>
    </select>

    <select id="selectWeightDetail"
            resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialDataDetailDTO">
        select a.project_id                                as receiverProject
             , a.weight_unit                               as unit
             , a.supplier_id
             , a.supplier_name
             , a.receive_id
             , a.create_id
             , a.weight_gross
             , a.weight_tare
             , a.weight_net
             , a.weight_deduct                             as weightDeduction
             , a.weight_actual
             , a.moisture_content
             , a.purchase_order_id                         as purchaseId
             , a.is_device
             , a.enter_time
             , a.enter_pic
             , a.leave_time
             , a.leave_pic
             , b.receive_no
             , b.receive_unit
             , b.receiver
             , a.receive_time
             , b.type_detail
             , a.wbs_id
             , a.position
             , e.discharge_point
             , b.is_addition
             , b.ext_no
             , b.truck_no
             , a.document_pic
             , a.receive_mode
             , a.material_validity
             , if(a.purchase_order_id is null, '否', '是') as IsUsed
             , d.deviation_calculate
        from d_material_data a
                 left join d_material_send_receive b on a.receive_id = b.id
                 left join d_purchase_order c on c.id = a.purchase_order_id and c.is_deleted = 0
                 left join d_purchase_contract_detail d on d.id = a.contract_detail_id
                 left join d_material_data_expand e on e.data_id = a.id and e.is_deleted = 0
        where a.id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="selectWeighDetailsWithPurchase"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialDatasVO">

        select a.ratio            as conversionRate
             , a.weight_send
             , a.actual_count
             , a.weight_unit      as unit
             , a.material_name
             , a.material_id
             , a.category_id
             , a.category_name    as materialCategoryName
             , a.deviation_rate   as deviation
             , a.deviation_status as deviationStatusByte
             , a.id
             , a.actual_receive
             , a.moisture_content
        from d_material_data a
        where a.id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="queryWeightReceiveInfo"
            resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialWeighInfoDTO">
        SELECT
        a.id,
        a.receive_id,
        a.weight_gross as gWeight,
        a.weight_tare as tWeight,
        a.weight_deduct as bWeight,
        a.weight_net as nWeight,
        a.weight_actual,
        a.moisture_content,
        a.purchase_order_id,
        a.contract_detail_id contractDetailId,
        a.actual_count,
        a.weight_send,
        a.material_id,
        a.position,
        a.project_id as receiveProject,
        a.weight_unit AS unit,
        a.deviation_rate,
        a.ratio,
        (a.actual_count - a.weight_send) as deviationCount,
        a.deviation_status,
        a.material_name,
        a.supplier_id,
        a.actual_receive,
        a.supplier_name,
        a.create_id,
        a.reconciliation_id,
        a.enter_time,
        a.leave_time,
        a.receive_time,
        b.receiver,
        a.push_state as isPushed,
        a.confirm_type,
        a.contract_detail_id,
        a.weigh_id,
        b.receive_no,
        b.type,
        b.is_addition,
        b.truck_no,
        b.ext_no,
        b.type_detail,
        if(b.type = 1,if(a.purchase_order_id is not null,'按采购单',if(a.contract_detail_id is not
        null,'按合同','仅关联供应商')),'') as receiveType
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id and b.company_id = a.company_id and b.project_id =
        a.project_id and b.type = #{query.type}
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and (mixing_type is null or mixing_type != 1)
        <if test="query.projectId != null and query.projectId != ''">
            and a.project_id = #{query.projectId}
        </if>
        <if test="query.projectIds != null and query.projectIds.size != 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.type != null">
            and b.type = #{query.type}
        </if>
        <if test="query.confirmType != null">
            and a.confirm_type = #{query.confirmType}
        </if>
        <if test="query.receiveNo != null and query.receiveNo != ''">
            and b.receive_no like concat ('%', #{query.receiveNo},'%')
        </if>

        <if test="query.supplierId != null and query.supplierId != ''">
            and a.supplier_id = #{query.supplierId}
        </if>
        <if test="query.truckNo != null and query.truckNo !=''">
            and b.truck_no like concat ('%', #{query.truckNo},'%')
        </if>
        <if test="query.receiveType != null and query.receiveType != '' and query.type == 1">
            <if test="query.receiveType == 1">
                and a.purchase_order_id is not null
            </if>
            <if test="query.receiveType == 2">
                and a.purchase_order_id is null
                and a.contract_detail_id is not null
            </if>
            <if test="query.receiveType == 3">
                and a.purchase_order_id is null
                and a.contract_detail_id is null
            </if>
        </if>
        <if test="query.receiveType != null and query.receiveType != '' and query.type == 2">
            and b.type_detail = #{query.receiveType}
        </if>
        <if test="query.receiveProject != null and query.receiveProject != ''">
            and a.supplier_name like concat('%', #{query.receiveProject},'%')
        </if>
        <if test="query.startTime != null">
            and a.receive_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.receive_time &lt; #{query.endTime}
        </if>
        <if test="query.reconciliationId != null and query.reconciliationId != ''">
            and a.reconciliation_id = #{query.reconciliationId}
        </if>
        <if test="query.isRevise != null and query.isRevise !=''">
            and a.is_revise = #{query.isRevise}
        </if>
        <if test="query.isVerify != null and query.isVerify != ''">
            <if test="query.isVerify == 1">
                and a.reconciliation_id is not null
            </if>
            <if test="query.isVerify == 2">
                and a.reconciliation_id is null
            </if>
        </if>
        <if test="query.isAddition != null and query.isAddition != ''">
            and b.is_addition = #{query.isAddition}
        </if>
        <if test="query.deviationStatus != null and query.deviationStatus != 99">
            and a.deviation_status = #{query.deviationStatus}
        </if>
        <if test="query.deviationStatus == 99">
            and a.deviation_status is null
        </if>
        <if test="query.weighId != null and query.weighId != ''">
            and a.weigh_id like concat('%',#{query.weighId},'%')
        </if>
        <if test="query.extNo != null and query.extNo != ''">
            and b.ext_no like concat('%',#{query.extNo},'%')
        </if>
        order by a.receive_time desc,a.create_time desc
    </select>

    <select id="sdkHistory" resultType="cn.pinming.microservice.material.management.resposity.vo.SDKHistoryVO">
        select b.supplier_id
             , b.material_id
             , b.material_name
             , b.ratio
             , b.weight_unit
             , b.contract_detail_id
             , b.supplier_name
             , b.purchase_order_id
             , b.category_id
             , a.type_detail
             , a.ext_no
             , b.remark
             , b.wbs_id
             , b.position
             , a.receive_unit
             , c.discharge_point
        from d_material_send_receive a
                 left join d_material_data b on b.receive_id = a.id and b.is_deleted = 0
                 left join d_material_data_expand c on c.data_id = b.id and c.is_deleted = 0
        where a.project_id = #{projectId}
          and a.is_deleted = 0
          and a.type = #{type}
          and b.create_id = #{mid}
        order by b.create_time desc limit 1
    </select>

    <select id="getDataByExtNo" resultType="cn.pinming.microservice.material.management.vo.MaterialDataStandardVO">
        SELECT
        a.id,
        a.weight_gross ,
        a.weight_tare ,
        a.weight_deduct ,
        a.category_id ,
        a.weight_net ,
        a.weight_actual,
        a.moisture_content,
        a.purchase_order_id ,
        a.contract_detail_id ,
        a.enter_time ,
        a.leave_time ,
        a.actual_count,
        a.weight_send,
        a.material_id,
        a.weight_unit AS unit,
        a.deviation_rate,
        a.deviation_status,
        a.material_name,
        a.ratio,
        a.supplier_id,
        a.actual_receive,
        a.receive_time,
        a.position,
        b.type,
        b.ext_no,
        b.truck_no,
        b.type_detail,
        b.remark
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        where a.is_deleted = 0
        and b.confirm_ext_no in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>

    <select id="getDataByIds" resultType="cn.pinming.microservice.material.management.vo.MaterialDataStandardVO">
        SELECT
        a.id,
        a.weight_gross ,
        a.weight_tare ,
        a.weight_deduct ,
        a.category_id ,
        a.weight_net ,
        a.weight_actual,
        a.moisture_content,
        a.purchase_order_id ,
        a.contract_detail_id ,
        a.enter_time ,
        a.leave_time ,
        a.actual_count,
        a.weight_send,
        a.material_id,
        a.weight_unit AS unit,
        a.deviation_rate,
        a.deviation_status,
        a.material_name,
        a.ratio,
        a.supplier_id,
        a.actual_receive,
        a.receive_time,
        a.position,
        b.type,
        b.ext_no,
        b.truck_no,
        b.type_detail,
        b.remark
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        where a.is_deleted = 0
        and a.id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>

    <select id="getDataByPurchaseId" resultType="cn.pinming.microservice.material.management.vo.MaterialDataStandardVO">
        SELECT a.id,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.category_id,
               a.weight_net,
               a.weight_actual,
               a.moisture_content,
               a.purchase_order_id,
               a.contract_detail_id,
               a.enter_time,
               a.leave_time,
               a.actual_count,
               a.weight_send,
               a.material_id,
               a.weight_unit AS unit,
               a.deviation_rate,
               a.deviation_status,
               a.material_name,
               a.ratio,
               a.supplier_id,
               a.actual_receive,
               a.receive_time,
               a.position,
               b.type,
               b.ext_no,
               b.truck_no,
               b.type_detail,
               b.remark,
               c.id          as purchaseOrderDetailId
        FROM d_material_data a
                 LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
                 left join d_purchase_order_detail c
                           on c.order_id = a.purchase_order_id and c.contract_detail_id = a.contract_detail_id
        where a.is_deleted = 0
          and a.purchase_order_id = #{purchaseOrderId}
    </select>

    <select id="getCargo" resultType="cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO">
        select b.material_id,
               b.category_id,
               c.supplier_id,
               b.deviation_floor,
               b.deviation_ceiling,
               b.deviation_calculate
        from d_purchase_order_detail a
                 left join d_purchase_contract_detail b on b.id = a.contract_detail_id
                 left join d_purchase_contract c on c.id = b.contract_id
        where a.id = #{cargoId}

        union all

        select k.material_id,
               k.category_id,
               g.supplier_id,
               j.deviation_floor,
               j.deviation_ceiling,
               j.deviation_calculate
        from d_mixing_plant_order_detail k
                 left join d_purchase_order_detail f on f.id = k.purchase_order_detail_id
                 left join d_purchase_contract_detail j on j.id = f.contract_detail_id
                 left join d_purchase_contract g on g.id = j.contract_id
        where k.id = #{cargoId}
    </select>

    <select id="selectVerifyByDataId" resultType="int">
        select count(*)
        from d_material_data a
                 left join d_material_verify_relation b on a.id = b.receive_data_id and a.company_id = b.company_id
                 left join d_material_verify c on c.id = b.verify_id and a.company_id = b.company_id
        where a.id = #{id}
          and a.company_id = #{companyId}
          and a.is_deleted = 0
          and c.status = 0
    </select>

    <select id="mobileHistoryByType"
            resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialDataExpandDTO">
        select a.*,c.*
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        left join d_material_data_expand c on c.data_id = a.id and c.is_deleted = 0
        where a.is_deleted = 0
        and a.company_id = #{companyId}
        and a.project_id = #{projectId}
        and b.type = #{type}
        <if test="type == 1">
            and a.receive_mode in (1,2)
        </if>
        <if test="receiveType != null and receiveType != ''">
            <if test="receiveType == 2">
                and a.purchase_order_id is not null
            </if>
            <if test="receiveType == 1">
                and a.contract_detail_id is not null
                and a.purchase_order_id is null
            </if>
        </if>
        order by a.update_time desc
        limit 1
    </select>

    <select id="getCargoFromMixing"
            resultType="cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO">
        select k.material_id,
               k.category_id,
               g.supplier_id,
               j.deviation_floor,
               j.deviation_ceiling,
               j.deviation_calculate
        from d_mixing_plant_order_detail k
                 left join d_purchase_order_detail f on f.id = k.purchase_order_detail_id
                 left join d_purchase_contract_detail j on j.id = f.contract_detail_id
                 left join d_purchase_contract g on g.id = j.contract_id
        where k.id = #{cargoId}
    </select>

    <select id="getCargoFromPurchase"
            resultType="cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO">
        select b.material_id,
               b.category_id,
               c.supplier_id,
               b.deviation_floor,
               b.deviation_ceiling,
               b.deviation_calculate
        from d_purchase_order_detail a
                 left join d_purchase_contract_detail b on b.id = a.contract_detail_id
                 left join d_purchase_contract c on c.id = b.contract_id
        where a.id = #{cargoId}
    </select>

    <select id="getCargoFromContract"
            resultType="cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO">
        select a.material_id,
               a.category_id,
               b.supplier_id,
               a.deviation_floor,
               a.deviation_ceiling,
               a.deviation_calculate
        from d_purchase_contract_detail a
                 left join d_purchase_contract b on b.id = a.contract_id
        where a.id = #{cargoId}
    </select>

    <select id="innerSync" resultType="cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO">
        select a.id, a.receive_id,b.receive_no,b.ext_no,b.confirm_ext_no,1 as kind,b.type,b.type_detail,null as
        distribution,b.truck_no,
        a.supplier_id,if(b.type = 2,a.supplier_name,null) as supplierName,a.material_id,c.discharge_point,
        null as length,null as root,null as materialTheoreticalWeight,null as theoreticalWeight,
        a.weight_gross,a.weight_tare,a.weight_deduct,a.weight_net,a.moisture_content,a.weight_actual,a.weight_send,
        a.ratio,a.actual_count,a.actual_receive,null as actualDeviation,null as actualDeviationRate,
        a.deviation_rate,a.deviation_status,a.weight_unit,a.weigh_id,a.enter_time,a.leave_time,a.receive_time,a.enter_pic,
        a.leave_pic,a.signature_pic,a.document_pic,a.signer_pic,a.wbs_id,a.position,null as
        brand,a.record_id_1,a.record_id_2,
        a.remark,
        b.receiver,b.driver,b.driver_number,b.receive_unit,b.is_addition,a.push_state,a.company_id,a.project_id,a.department_id,a.create_id,a.update_id,a.is_deleted
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        left join d_material_data_expand c on c.data_id = a.id and c.is_deleted = 0
        where 1 = 1
        <if test="companyIdList != null and companyIdList != ''">
            and a.company_id in
            <foreach collection="companyIdList" item="companyId" open="(" close=")" separator="," index="">
                #{companyId}
            </foreach>
        </if>
        <if test="projectIdList != null and projectIdList != ''">
            and a.project_id in
            <foreach collection="projectIdList" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>

        union all

        select m.id,m.receive_id,n.receive_no,n.ext_no,n.confirm_ext_no,2 as
        kind,n.type,n.type_detail,n.distribution,n.truck_no,
        m.supplier_id,null as supplierName,m.material_id,m.length,m.root,
        m.material_theoretical_weight,m.theoretical_weight,n.weight_gross,n.weight_tare,n.weight_deduct,n.weight_net,
        n.moisture_content,m.weight_actual,m.weight_send,m.ratio,m.actual_count,m.actual_receive,m.actual_deviation,m.actual_deviation_rate,
        m.send_deviation_rate as deviationRate,m.deviation_status,m.weight_unit,null as
        weighId,n.enter_time,n.leave_time,n.receive_time,n.enter_pic,n.leave_pic,
        n.signature_pic,n.document_pic,n.signer_pic,m.wbs_id,m.position,m.brand,n.record_id_1,n.record_id_2,m.remark,n.receiver,n.driver,n.driver_number,
        n.receive_unit,1 as
        isAddition,m.push_state,m.company_id,m.project_id,m.department_id,m.create_id,m.update_id,m.is_deleted
        from d_material_data_multi m
        left join d_material_send_receive_multi n on n.id = m.receive_id
        where 1 = 1
        <if test="companyIdList != null and companyIdList != ''">
            and m.company_id in
            <foreach collection="companyIdList" item="companyId" open="(" close=")" separator="," index="">
                #{companyId}
            </foreach>
        </if>
        <if test="projectIdList != null and projectIdList != ''">
            and m.project_id in
            <foreach collection="projectIdList" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="innerSyncById" resultType="cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO">
        select a.id,a.receive_id, b.receive_no,b.ext_no,b.confirm_ext_no,1 as kind,b.type,b.type_detail,null as
        distribution,b.truck_no,
        a.supplier_id,if(b.type = 2,a.supplier_name,null) as supplierName,a.material_id,
        null as length,null as root,null as materialTheoreticalWeight,null as theoreticalWeight,
        a.weight_gross,a.weight_tare,a.weight_deduct,a.weight_net,a.moisture_content,a.weight_actual,a.weight_send,
        a.ratio,a.actual_count,a.actual_receive,null as actualDeviation,null as actualDeviationRate,
        a.deviation_rate,a.deviation_status,a.weight_unit,a.weigh_id,a.enter_time,a.leave_time,a.receive_time,a.enter_pic,
        a.leave_pic,a.signature_pic,a.document_pic,a.signer_pic,a.wbs_id,a.position,null as
        brand,a.record_id_1,a.record_id_2,
        a.remark,
        b.receiver,b.driver,b.driver_number,b.receive_unit,b.is_addition,a.push_state,a.company_id,a.project_id,a.department_id,a.create_id,a.update_id,a.is_deleted
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where 1 = 1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="receiveId != null and receiveId != ''">
            and b.id = #{receiveId}
        </if>
        and (
        <foreach collection="list" item="item" separator="or">
            (a.company_id = #{item.companyId}
            <if test="item.projectIds != null and item.projectIds != ''">
                and find_in_set(a.project_id,#{item.projectIds})
            </if>)
        </foreach>
        )
    </select>
    <select id="warning" resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialDataExpandDTO">
        select a.*,b.receive_no,b.type as receiveType
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where a.is_deleted = 0
        and a.id in
        <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>
    <select id="selectReceiveListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO">
        select e.category_name, sum(a.weight_send) as weightSend, sum(a.actual_count) as actualCount
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        left join d_purchase_contract_detail e on e.id = a.contract_detail_id
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        and e.category_name is not null
        <if test="type != null and type == 11">
            and a.receive_mode = 2
        </if>
        <if test="type != null and type == 12">
            and a.receive_mode = 1
        </if>
        group by category_name
        order by actualCount desc
    </select>
    <select id="selectSupplierAnalysisByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO">
        select sum(weight_send) as weightSendAmount ,sum(actual_count) as weightActualAmount,sum(actual_receive) as
        actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        and a.weight_unit = #{unit}
    </select>
    <select id="selectSupplierAnalysisUnionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO">
        select sum(weightSendAmount) as weightSendAmount ,sum(weightActualAmount) as weightActualAmount
        ,sum(actualReceive) as actualReceive
        from
        (select sum(weight_send) as weightSendAmount ,sum(actual_count) as weightActualAmount,sum(actual_receive) as
        actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.is_deleted = 0 and b.type = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        and a.weight_unit = #{unit}
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        union all
        select sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as
        weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        ) x
    </select>
    <select id="listUnitByCategoryIds"
            resultType="cn.pinming.microservice.material.management.resposity.dto.StatisticsUnitDTO">
        select distinct categoryId, unit from (
        select distinct category_id categoryId, weight_unit unit from d_material_data
        where 1 = 1
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        union all
        select distinct category_id categoryId, settlement_unit unit from d_mobile_receive_total
        where 1 = 1
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        ) t
    </select>
    <select id="selectSupplierAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO">
        select a.supplier_id, sum(a.weight_send) as weightSendAmount, sum(a.actual_count) as
        weightActualAmount,sum(a.actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        <if test="supplierIds !=null and supplierIds.size > 0">
            and a.supplier_id in
            <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id

    </select>
    <select id="selectSupplierUnionAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO">
        select supplier_id,sum(weightSendAmount) as weightSendAmount,sum(weightActualAmount) as
        weightActualAmount,sum(actualReceive) as actualReceive
        from
        (select a.supplier_id, sum(a.weight_send) as weightSendAmount, sum(a.actual_count) as
        weightActualAmount,sum(a.actual_receive) as actualReceive
        from d_material_data a left join d_material_send_receive b on a.receive_id = b.id
        where a.is_deleted = 0 and b.type = 1 and a.supplier_id is not null
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        <if test="supplierIds !=null and supplierIds.size > 0">
            and a.supplier_id in
            <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id

        union all

        select a.supplier_id,sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as
        weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0 and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id
        ) x
        group by supplier_id having supplier_id is not null
    </select>
    <select id="mobileReceiveOverviewCard"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardDTO">
        select t4.category_id categoryId, t4.material_id, t4.settlement_unit unit,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where material_id is not null and is_deleted = 0 and settlement_unit is
        not null and settlement_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>
    <select id="wagonReceiveOverviewOther"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardDTO">
        select t1.category_id categoryId, t1.material_id materialId, t1.weight_unit unit,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1=1 and is_deleted = 0 and weight_unit is not null and weight_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id not in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewOther"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardDTO">
        select t4.category_id categoryId, t4.material_id, t4.settlement_unit unit,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1=1 and is_deleted = 0 and settlement_unit is not null and
        settlement_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id not in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>
    <select id="mobileReceiveOverviewCardSecond"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t3.create_time,'%Y/%m/%d') time, date_format(t3.create_time,'%Y/%m') month,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1 = 1 and is_deleted = 0
        <if test="materialIds != null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator="," index="">
                #{materialId}
            </foreach>
        </if>
        <if test="unit != null and unit != ''">
            and settlement_unit = #{unit}
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>
    <select id="wagonReceiveOverviewOtherSecond"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t.receive_time,'%Y/%m/%d') time, date_format(t.receive_time,'%Y/%m') month,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1=1 and is_deleted = 0
        <if test="categoryId != null and categoryId != -1">
            and category_id = #{categoryId}
        </if>
        <if test="categoryId != null and categoryId == -1">
            and (material_id is null or material_id = '')
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewOtherSecond"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t3.create_time,'%Y/%m/%d') time, date_format(t3.create_time,'%Y/%m') month,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1=1 and is_deleted = 0
        <if test="categoryId != null and categoryId != -1">
            and category_id = #{categoryId}
        </if>
        <if test="categoryId != null and categoryId == -1">
            and (material_id is null or material_id = '')
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>
    <select id="wagonReceiveOverviewCardSecond"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t.receive_time,'%Y/%m/%d') time, date_format(t.receive_time,'%Y/%m') month,
        t1.weight_unit unit, if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1 = 1 and is_deleted = 0
        <if test="materialIds != null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator="," index="">
                #{materialId}
            </foreach>
        </if>
        <if test="unit != null and unit != ''">
            and weight_unit = #{unit}
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="selectDeviationByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO">
        select count(IF(b.deviation_status = 0, 1, null)) as normal,
        count(IF(b.deviation_status = 2, 1, null)) as positive,
        count(IF(b.deviation_status = 1, 1, null)) as negative,
        count(IF(b.deviation_status is null, 1, null)) as unidentified,
        date_format(b.receive_time,'%Y-%m') as `date`
        from d_material_send_receive a left join d_material_data b on a.id = b.receive_id and b.receive_time is not null
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        and b.material_validity = 1
        where a.type = 1 and b.material_exist = 1
        <if test="receiveModes != null and receiveModes.size > 0">
            and a.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        and a.material_validity = 1
        and a.company_id = #{query.companyId}
        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(b.receive_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(b.receive_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        group by `date` order by `date`
    </select>
    <select id="selectDeviationSummaryByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO">
        select b.deviation_status , count(1) as count
        from d_material_send_receive a
        left join d_material_data b on a.id = b.receive_id
        <if test="receiveModes != null and receiveModes.size > 0">
            and a.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        and b.material_validity = 1
        where a.type = 1 and b.material_exist = 1
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        and a.material_validity = 1
        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>

        </if>
        <if test="query.startDate != null">
            and date_format(b.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(b.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.deviation_status
    </select>
    <select id="listSummaryDeliveryByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SummaryDeliveryVO">
        select a.category_id,a.category_name,sum(if(a.weight_actual is null, 0, a.weight_actual)) as accumulationCount,
        sum(if(date_format(a.receive_time, '%Y-%m') = date_format(now(), '%Y-%m'), if(a.weight_actual is null, 0,
        a.weight_actual), 0)) AS monthlyCount,
        a.weight_unit as unit
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where b.material_validity = 1 and a.material_id is not null
        and b.type = 2
        and a.category_id is not null
        and a.weight_gross is not null and a.is_deleted = 0 and b.is_deleted = 0
        and a.company_id = #{companyId}
        <if test="startDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.category_id
    </select>
    <select id="selectOverviewMonthNegativeDiffAmount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select time as 'key', ifnull(sum(num),0) as val
        from (select date_format(a.receive_time, '%Y-%m') as time,
        (actual_count - weight_send)/weight_send as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and a.company_id = b.company_id and a.project_id = b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and type = 1
        and deviation_status = 1
        and a.receive_time is not null
        and a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        and a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -1 YEAR), '%Y-%m-01 00:00:00')) x
        group by x.time

    </select>
    <select id="selectOverviewMonthNegativeTruckAmount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select time as 'key', count(deviation_status) as val
        from (select date_format(a.receive_time, '%Y-%m') as time,
        deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and a.company_id = b.company_id and a.project_id = b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and type = 1
        and deviation_status = 1
        and a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        and a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -1 YEAR), '%Y-%m-01 00:00:00')) x
        group by x.time

    </select>
    <select id="selectOverviewMonthTotalDiffAmount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select time as 'key', ifnull(sum(weight_send),0) as val
        from (select date_format(a.receive_time, '%Y-%m') as time,
        weight_send
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and a.company_id = b.company_id and a.project_id = b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and type = 1
        and a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        and a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -1 YEAR), '%Y-%m-01 00:00:00')) x
        group by x.time

    </select>
    <select id="selectOverviewMonthTotalTruckAmount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select time as 'key', count(deviation_status) as val
        from (select date_format(a.receive_time, '%Y-%m') as time,
        deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and a.company_id = b.company_id and a.project_id = b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and type = 1
        and a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        and a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -1 YEAR), '%Y-%m-01 00:00:00')) x
        group by x.time

    </select>
    <select id="listCategoryIdList" resultType="java.lang.Integer">
        select distinct a.category_id
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.category_id is not null
        and a.company_id = #{companyId}
        and b.type = 2
        <if test="startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="projectIdList != null and projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="listDetailList"
            resultType="cn.pinming.microservice.material.management.resposity.dto.SummaryDeliverySecondDetailDTO">
        select a.category_id,a.category_name, sum(a.weight_actual) as accumulationCount,
        sum(if(date_format(a.receive_time,'%Y-%m') = date_format(now(), '%Y-%m'), a.weight_actual, 0)) as monthlyCount
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where b.material_validity = 1 and a.material_id is not null
        and b.type = 2
        and a.company_id = #{query.companyId}
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="categoryIdList.size != 0">
            and a.category_id in
            <foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        group by a.category_id
    </select>
    <select id="selectReceiveOverviewSecondByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewSecondDTO">
        select x.time, sum(x.total) as total, sum(x.monthRise) as monthRise
        from (
        select DATE_FORMAT(a.receive_time, '%Y/%m') as time,
        count(*) as total,
        count(case when date_format(a.receive_time, "%Y/%m") = date_format(now(), "%Y/%m") then 1 or null end) AS
        monthRise
        from d_material_send_receive b
        left join d_material_data a on a.receive_id = b.id
        where a.is_deleted = 0 and b.type = #{type}
        and b.company_id = #{query.companyId}
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by time having time is not null
        <if test="type != 2">
            UNION ALL
            SELECT
            DATE_FORMAT( create_time, '%Y/%m' ) AS time,
            count( 1 ) AS total,
            count(case date_format( create_time, '%Y%m' ) when date_format( now(), '%Y%m' ) then 1 else NULL end) AS
            monthRise
            FROM
            d_mobile_receive
            WHERE
            is_deleted = 0 and company_id = #{query.companyId}
            <if test="query.projectIdList.size != 0">
                and project_id in
                <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            GROUP BY time having time is not null
        </if>
        ) x
        group by x.time
        order by x.time asc
    </select>
    <select id="selectOverviewTotal"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select material_id as 'key', count(x.material_id) as val from (select material_id
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and a.company_id = b.company_id and a.project_id = b.project_id
        where a.material_id is not null
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.is_deleted = 0
        and b.is_deleted = 0
        AND a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(NOW(), '%Y-01-01 00:00:00')
        AND a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01 00:00:00')
        union all
        SELECT b.material_id
        FROM d_mobile_receive a
        left join d_mobile_receive_detail b on a.receive_id = b.receive_id
        WHERE b.material_id is not null
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        AND a.is_deleted = 0
        and b.is_deleted = 0
        AND a.create_time <![CDATA[ >= ]]> DATE_FORMAT(NOW(), '%Y-01-01 00:00:00')
        AND a.create_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01 00:00:00')) x
        group by x.material_id
    </select>
    <select id="selectDeviationSecondByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.dto.DeviationOverviewSecondDTO">
        SELECT
        sum(x.countNormalCarsNum) as countNormalCarsNum,
        sum(x.countMinusCarsNum) as countMinusCarsNum,
        sum(x.countPositiveCarsNum) as countPositiveCarsNum,
        x.time

        from (

        SELECT
        count(case when deviation_status = 0 then 1 else null end ) as countNormalCarsNum,
        count(case when deviation_status = 1 then 1 else null end ) AS countMinusCarsNum,
        count(case when deviation_status = 2 then 1 else null end ) as countPositiveCarsNum,
        DATE_FORMAT(a.receive_time, '%Y/%m') as time
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        where
        a.receive_time is not null
        AND a.is_deleted = 0
        AND a.company_id = #{query.companyId}
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            AND a.project_id IN
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        GROUP BY time

        UNION ALL

        SELECT
        count(case when deviation_status = 0 then 1 else null end ) as countNormalCarsNum,
        count(case when deviation_status = 1 then 1 else null end ) AS countMinusCarsNum,
        count(case when deviation_status = 2 then 1 else null end ) as countPositiveCarsNum,
        DATE_FORMAT(create_time, '%Y/%m') as time
        FROM
        d_mobile_receive
        WHERE is_deleted = 0 and company_id = #{query.companyId}
        and create_time is not null
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY time
        ) x
        GROUP BY x.time
        ORDER BY x.time
    </select>
    <select id="deductRankByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, count(a.id) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="deductProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, round(count(if (a.weight_deduct != 0, a.weight_deduct, null))*100/count(*),2) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="deductTotalRankByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, - sum(a.weight_deduct) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="deductTotalProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, - round(sum(a.weight_deduct) * 100 / sum(a.weight_send / a.ratio), 2) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyRankListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, count(a.id) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>


    </select>
    <select id="negativeFrequencyMobileRankListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, count(b.id) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and b.deviation_status in (1, 2)
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id, b.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyAllRankListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select x.supplier_id, sum(x.num) as num, x.deviation_status
        from (select a.supplier_id, count(a.id) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        union all
        select a.supplier_id, count(b.id) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and b.deviation_status in (1, 2)
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id, b.deviation_status) x
        group by x.supplier_id, x.deviation_status order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyRankRateTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">
        select x.supplier_id, x.num , x.diff, ( x.diff * 100 / x.num ) as rate from (
        select a.supplier_id, count(a.id) as num, count(a.deviation_status = 1 or null) as diff
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id) x
        order by rate desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyMobileRankRateTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">
        select x.supplier_id, x.num , x.diff, ( x.diff * 100 / x.num ) as rate from (
        select a.supplier_id, count(b.id) as num, count(b.deviation_status = 1 or null) as diff
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id) x
        order by rate desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeFrequencyAllRankRateTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">
        select x.supplier_id, sum(x.num) as num, sum(x.diff) as diff, ( sum(x.diff) * 100 / sum(x.num) ) as rate
        from (select a.supplier_id, count(a.id) as num, count(a.deviation_status = 1 or null) as diff
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        union all
        select a.supplier_id, count(b.id) as num, count(b.deviation_status = 1 or null) as diff
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id) x
        group by x.supplier_id order by rate desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyRankTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">
        select a.supplier_id, count(a.id) as num, count(a.deviation_status = 1 or null) as diff
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by diff desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeFrequencyMobileRankTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">
        select a.supplier_id, count(b.id) as num, count(b.deviation_status = 1 or null) as diff
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyAllRankTopListByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO">

        select x.supplier_id, sum(x.num) as num, sum(x.diff) as diff
        from (select a.supplier_id, count(a.id) as num, count(a.deviation_status = 1 or null) as diff
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        union all
        select a.supplier_id, count(b.id) as num, count(b.deviation_status = 1 or null) as diff
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id) x
        group by x.supplier_id order by diff desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeTotalProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select a.supplier_id,
        (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum,
        if(a.weight_send is null, 0, a.weight_send) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

    </select>
    <select id="negativeTotalMobileProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select b.supplier_id supplierId,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and
        c.pid is null and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id supplierId,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - d.deviation_ceiling) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and
        d.pid is null and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

    </select>
    <select id="negativeTotalAllProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select b.supplier_id supplierId,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and
        c.pid is null and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id supplierId,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - d.deviation_ceiling) * a.send_settlement_total as positveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and
        d.pid is null and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select a.supplier_id supplierId, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum,
        if(a.weight_send is null, 0, a.weight_send) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

    </select>
    <select id="negativeTotalRankByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select x.supplier_id, round(sum(x.negativeNum) / 100, 2) as negativeNum, round(sum(x.positiveNum) / 100, 2) as
        positiveNum, x.deviation_status from
        (select a.supplier_id, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum, (a.deviation_rate
        - c.deviation_ceiling) * a.weight_send as positiveNum, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeTotalMobileRankByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select x.supplier_id, round(sum(negativeNum) / 100, 2) as negativeNum, round(sum(positiveNum) / 100, 2) as
        positiveNum, x.deviation_status
        from (select b.supplier_id,
        sum((a.deviation_rate - c.deviation_floor) *
        a.send_settlement_total) as negativeNum,
        sum((a.deviation_rate - c.deviation_ceiling) *
        a.send_settlement_total) as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and
        c.pid is null and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.supplier_id, a.deviation_status

        union all

        select b.supplier_id,
        sum((a.deviation_rate - d.deviation_floor) * a.send_settlement_total) as negativeNum, sum((a.deviation_rate -
        d.deviation_floor) * a.send_settlement_total) as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and
        d.pid is null and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.supplier_id, a.deviation_status
        ) x
        group by x.supplier_id, x.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeTotalAllRankByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO">
        select y.supplier_id, ifnull(sum(y.negativeNum) / 100,0) as negativeNum, ifnull(sum(y.positiveNum) / 100,0) as
        positiveNum, y.deviation_status from (
        select x.supplier_id, round(sum(x.negativeNum), 2) as negativeNum, round(sum(x.positiveNum), 2) as positiveNum,
        x.deviation_status from
        (select a.supplier_id, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum, (a.deviation_rate
        - c.deviation_ceiling) * a.weight_send as positiveNum, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status
        union all
        select x.supplier_id, round(sum(negativeNum), 2) as negativeNum, round(sum(positiveNum), 2) as positiveNum,
        x.deviation_status
        from (select b.supplier_id,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum, (a.deviation_rate -
        c.deviation_ceiling) * a.send_settlement_total as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and
        c.pid is null and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum, (a.deviation_rate -
        d.deviation_ceiling) * a.send_settlement_total as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and
        d.pid is null and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status) y
        group by y.supplier_id, y.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id,count(*) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        and a.deviation_status in (1,2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyMobileProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select a.supplier_id, count(*) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and a.supplier_id is not null
        group by a.supplier_id, b.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
    <select id="negativeFrequencyAllProportionByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO">
        select x.supplier_id, sum(x.num) as num, x.deviation_status
        from (select a.supplier_id ,count(*) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        and a.deviation_status in (1,2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        union all
        select a.supplier_id, count(*) as num, a.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on a.receive_id = b.receive_id and a.is_deleted = 0
        where b.company_id = #{query.companyId}
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and b.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and a.supplier_id is not null
        group by a.supplier_id,a.deviation_status) x
        group by x.supplier_id, x.deviation_status order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>

    </select>
</mapper>
