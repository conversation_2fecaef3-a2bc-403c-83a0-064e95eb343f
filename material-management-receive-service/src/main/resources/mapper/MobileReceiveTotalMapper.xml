<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveTotalMapper">

    <select id="selectHistory"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MobileReceiveHistoryVO">
        select a.brand, a.send_number, a.send_content, a.send_settlement_total, a.unit, a.settlement_unit
        from d_mobile_receive_total a
        where a.id = #{totalId}
          and a.is_deleted = 0
    </select>
    <select id="countMobileReceiveNum"
            resultType="cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO">
        SELECT
        category_id,
        sum( send_settlement_total ) weightSend,
        sum( actual_settlement_total ) actualCount
        FROM
        d_mobile_receive_total t
        WHERE
        is_deleted = 0
        AND company_id = #{companyId}
        <if test="projectId != null">
            and project_id = #{projectId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="unit != null and unit != ''">
            AND settlement_unit = #{unit}
        </if>
        <if test="receiveType != null and receiveType > 0">
            and exists (select receive_id from d_mobile_receive r where t.receive_id = r.receive_id and receive_type =
            #{receiveType})
        </if>
        and create_time <![CDATA[ >= ]]> #{startDate}
        and date_format(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        GROUP BY
        category_id
        order by actualCount desc
    </select>
    <select id="countDeviation"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO">
        SELECT date_format(a.create_time, '%Y-%m') AS `date`,
        count(IF(b.deviation_status = 0, 1, NULL)) AS normal,
        count(IF(b.deviation_status is null, 1, null)) as unidentified,
        count(IF(b.deviation_status = 2, 1, NULL)) AS positive,
        count(IF(b.deviation_status = 1, 1, NULL)) AS negative
        FROM
        d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id and a.is_deleted = 0 and b.is_deleted = 0 and
        a.create_time is not null
        WHERE
        a.company_id = #{query.companyId}
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_type in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and a.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and a.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ >= ]]>  date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        GROUP BY `date`
    </select>
    <select id="countDeviationStatus"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO">
        SELECT
        a.deviation_status , count(1) as count
        FROM
        d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id and a.is_deleted = 0 and b.is_deleted = 0
        WHERE a.company_id = #{query.companyId}
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_type in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>

        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and a.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and a.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and a.create_time <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        GROUP BY a.deviation_status
    </select>
</mapper>
