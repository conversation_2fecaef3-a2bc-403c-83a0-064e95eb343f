<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMultiMapper">

    <select id="innerSyncById" resultType="cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO">
        select m.id,m.receive_id,n.receive_no,n.ext_no,n.confirm_ext_no,2 as kind,n.type,n.type_detail,n.distribution,n.truck_no,
        m.supplier_id,null as supplierName,m.material_id,m.length,m.root,
        m.material_theoretical_weight,m.theoretical_weight,n.weight_gross,n.weight_tare,n.weight_deduct,n.weight_net,
        n.moisture_content,m.weight_actual,m.weight_send,m.ratio,m.actual_count,m.actual_receive,m.actual_deviation,m.actual_deviation_rate,
        m.send_deviation_rate as deviationRate,m.deviation_status,m.weight_unit,null as weighId,n.enter_time,n.leave_time,n.receive_time,n.enter_pic,n.leave_pic,
        n.signature_pic,n.document_pic,n.signer_pic,m.wbs_id,m.position,m.brand,n.record_id_1,n.record_id_2,m.remark,n.receiver,n.driver,n.driver_number,
        n.receive_unit,1 as isAddition,m.push_state,m.company_id,m.project_id,m.department_id,m.create_id,m.update_id,m.is_deleted
        from d_material_data_multi m
        left join d_material_send_receive_multi n on n.id = m.receive_id
        where 1 = 1
        <if test="id != null and id != ''">
            and m.id = #{id}
        </if>
        <if test="receiveId != null and receiveId != ''">
            and n.id = #{receiveId}
        </if>
        and (
        <foreach collection="list" item="item" separator="or">
            (n.company_id = #{item.companyId}
            <if test="item.projectIds != null and item.projectIds != ''">
                and find_in_set(n.project_id,#{item.projectIds})
            </if>)
        </foreach>
        )
    </select>
</mapper>
