<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialReviseMapper">

    <select id="selectMaterialRevise"
            resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDataDTO">
        select id as reviseId,
               receive_id,
               material_data_id,
               revise_detail,
               create_id as reviserId,
               company_id,
               create_time as reviseTime,
                revise_remark
        from d_material_revise
        where revise_detail is not null and revise_detail != ''
        <if test="type != 2">
            and receive_id = #{receiveId} and material_data_id = #{id} and is_deleted = 0 and original_data is null
        </if>
        <if test="type == 2">
            and material_data_id = #{id} and is_deleted = 0
        </if>
        order by create_time desc
    </select>

    <select id="selectMaterialReviseDetail"
            resultType="cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDetailDTO">
        select a.id,
               b.id as receive_id,
               a.weigh_id as tUuid,
               b.receive_mode,
               b.receive_no,
               a.document_pic,
               a.supplier_id,
               a.supplier_name,
               b.truck_no,
               a.material_id,
               a.material_name,
               a.weight_send,
               a.purchase_order_id,
               a.contract_detail_id,
               a.unit_price,
               a.total_price,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.weight_net as nWeight,
               a.ratio,
               a.actual_count,
               a.actual_receive,
               a.weight_unit,
               a.deviation_rate,
               a.deviation_status,
               a.moisture_content,
               c.unit,
               c.conversion_rate,
               c.deviation_ceiling,
               c.deviation_floor,
               a.wbs_id,
               a.position,
               e.discharge_point,
               b.type_detail,
               b.ext_no,
               b.receive_unit,
                b.is_addition,
               a.is_device,
            c.deviation_calculate,
            d.order_no
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.is_deleted = 0
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
            left join d_purchase_order d on d.id = a.purchase_order_id
            left join  d_material_data_expand e on e.data_id = a.id and e.is_deleted = 0
        where a.receive_id = #{receiveId}
        and a.id = #{id}
    </select>
</mapper>
