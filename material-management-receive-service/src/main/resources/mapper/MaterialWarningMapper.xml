<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialWarningMapper">
    <select id="selectPageByQuery" resultType="cn.pinming.microservice.material.management.resposity.dto.WarningInfoDTO"
            parameterType="cn.pinming.microservice.material.management.resposity.query.WarningInfoQuery">
        select warning_source_id, warning_source_no, source_project_id, group_concat(warning_type) warningType,
        min(create_time) warningTime, max(handler_name) handlerName, max(handler_id) HandlerId,
        max(handler_time) handlerTime, max(warning_status) warningStatus, max(warning_source) warningSource, max(id)
        warningId
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        <if test="query.sourceProjectIds !=null and query.sourceProjectIds.size > 0">
            and source_project_id in
            <foreach collection="query.sourceProjectIds" item="sourceProjectId" open="(" close=")" separator=",">
                #{sourceProjectId}
            </foreach>
        </if>
        <if test="query.warningSourceNo !=null and query.warningSourceNo !=''">
            and warning_source_no like concat('%', #{query.warningSourceNo},'%')
        </if>
        <if test="query.startTime != null  and query.endTime != null">
            and create_time <![CDATA[ >= ]]> #{query.startTime}
            and create_time <![CDATA[ <= ]]> #{query.endTime}
        </if>
        <if test="query.warningSource !=null and query.warningSource !=''">
            and warning_source = #{query.warningSource}
        </if>
        <if test="query.warningStatus != null and query.warningStatus != 0">
            and warning_status = #{query.warningStatus}
        </if>
        <if test="query.warningKind != null">
            and warning_kind = #{query.warningKind}
        </if>
        group by warning_source_id
        <if test="query.warningType != null">
            having find_in_set(#{query.warningType},group_concat(warning_type))
        </if>
        order by warning_source_id,warningStatus asc, warningTime desc
    </select>

    <select id="listWarningDetail"
            resultType="cn.pinming.microservice.material.management.resposity.dto.WarningDetailDTO">
        select warning_type, warning_info
        from d_material_warning
        where warning_source_id = #{query.warningSourceId}
    </select>
    <select id="queryWarningSummary"
            resultType="cn.pinming.microservice.material.management.resposity.vo.WarningSummaryAnalysisVO">
        select count(t1.warning_source_id) as warningTotal,
        count(if(t1.warningStatus = 1, 1, null)) as warningUnHandleCount,
        count(if(date_format(t1.time, '%Y%m') = date_format(now(), '%Y%m'), 1, null)) aS monthlyCount
        from (select warning_source_id, max(warning_status) as warningStatus, min(create_time) as time
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) is null)))
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by warning_source_id) as t1
    </select>
    <select id="listSummaryWarningByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SummaryWarningVO">
        SELECT
        warning_type as type,
        sum( CASE warning_status WHEN 1 THEN 1 ELSE 0 END ) as unHandleCount,
        sum( CASE warning_status WHEN 2 THEN 1 ELSE 0 END ) as handleCount,
        sum( CASE warning_status WHEN 1 THEN 1 WHEN 2 THEN 1 ELSE 0 END ) as total
        FROM
        d_material_warning where is_deleted = 0 and company_id = #{query.companyId}
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) is null)))
        <if test="query.startDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY
        warning_type
        order by unHandleCount desc
    </select>
    <select id="listSummaryWarningSecondByQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SummaryWarningSecondVO">
        SELECT
        warning_type as type,
        sum( CASE warning_status WHEN 1 THEN 1 ELSE 0 END ) as unHandleCount,
        sum( CASE warning_status WHEN 2 THEN 1 ELSE 0 END ) as handleCount
        FROM
        d_material_warning where company_id = #{query.companyId} and is_deleted = 0
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) is null)))
        <if test="query.startDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY
        warning_type

    </select>
    <select id="queryOverviewWarningSummary"
            resultType="cn.pinming.microservice.material.management.resposity.dto.WarningOverviewSecondDTO">
        select count(*) total,
        count(if(warningStatus = 1, 1, null)) unHandle,
        date_format(create_time, '%Y/%m') time
        from (select warning_source_id,
        min(warning_status) as warningStatus,
        min(create_time) as create_time
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id =
        #{query.companyId})) is null)))
        group by warning_source_id) t1
        group by date_format(create_time, '%Y/%m')
        order by time asc
    </select>
    <select id="queryOverviewWarningTips" resultType="java.lang.Integer">
        select count(distinct warning_source_id) as count
        from d_material_warning
        where is_deleted = 0 and company_id = #{companyId} and warning_status = 1
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        and warning_type in
        <foreach collection="warningTypeList" item="warningType" open="(" close=")" separator=",">
            #{warningType}
        </foreach>
    </select>
    <select id="selectOverviewDiffProject"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        select project_id as 'key', count(warning_status = 2 or null)*100 /count(warning_status) as val
        from d_material_warning
        where is_deleted = 0
        and company_id = #{companyId}
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        and warning_type = 5
        and project_id is not null
        group by project_id
    </select>
</mapper>
