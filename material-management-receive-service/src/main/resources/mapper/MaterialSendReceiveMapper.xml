<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialSendReceiveMapper">
    <select id="selectWeightInfo" resultType="cn.pinming.microservice.material.management.resposity.dto.WeighInfoDTO">
        select
        count(case b.type when 1 then a.id end) as weighingCarNumber,
        count(case b.type when 2 then a.id end) as sendingCarNumber,
        count(case b.type when 1 then a.id end) + count(case b.type when 2 then a.id end) as weighingCarCount,
        sum(a.weight_actual) as weighWeight
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
    </select>

    <select id="selectWeightInfoByDay"
            resultType="cn.pinming.microservice.material.management.resposity.dto.WeighTruckChangeDTO">
        select
        date_format(a.receive_time,'%Y-%m') as weighTime,
        count(case b.type when 1 then a.id end) as receiveNum,
        count(case b.type when 2 then a.id end) as sendNum
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
        group by weighTime
    </select>

    <select id="selectWeightInfos" resultType="cn.pinming.microservice.material.management.resposity.dto.WeighInfosDTO">
        select
        SUM(a.weight_actual) as weighWeight,
        MIN(a.receive_time) as firstUsedTime,
        MAX(a.receive_time) as lastUsedTime,
        ifnull (count(case b.type when 1 then a.id end),0) as weighingCarNumber,
        ifnull (count(case b.type when 2 then a.id end),0) as sendingCarNumber,
        a.project_id
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size != 0 ">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
        GROUP BY a.project_id
    </select>

    <select id="querySummary" resultType="cn.pinming.microservice.material.management.resposity.vo.SummaryAnalysisVO">
        select
        date_format( a.receive_time, '%Y%m' ) as time,
        count(type = 1 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as
        monthReceiveCarsNum,
        count(type = 1 or null) as countReceiveCarsNum,
        count(type = 2 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as
        monthSendCarsNum,
        count(type = 2 or null) as countSendCarsNum,
        count(deviation_status = 1 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as
        monthMinusCarsNum,
        count(deviation_status = 1 or null) as countMinusCarsNum
        from d_material_data a left join d_material_send_receive b on a.receive_id = b.id
        where a.company_id = #{query.companyId}
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        and a.is_deleted = 0
        and a.receive_time is not null
    </select>

    <select id="selectStatistics"
            resultType="cn.pinming.microservice.material.management.resposity.dto.StatisticsDataDTO">
        select sum(a.weight_send) as sendAmount,sum(a.actual_count) as receiveAmount,sum(a.actual_receive) as
        actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.is_deleted = 0
        where b.type = 1 and a.material_validity = 1
        and a.company_id = #{query.companyId}
        and a.weight_unit = #{query.unit}
        and b.material_validity = 1
        and a.is_deleted = 0
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        <if test="query.startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
        </if>
        <if test="query.endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and a.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test='query.receiveType == "4" or query.receiveType == "8" '>
            and a.receive_mode in (1,2)
        </if>
        <if test='query.receiveType == "5" or query.receiveType == "7" '>
            and a.receive_mode = 3
        </if>
    </select>

    <select id="selectDeviationPage"
            resultType="cn.pinming.microservice.material.management.resposity.vo.WeighDeviationDetailVO">
        select *
        from
        (
        select a.receive_no,b.id as receiveId,b.project_id,b.supplier_id,4 as receiveType,a.purchase_id
        ,a.truck_no
        ,b.material_id,b.weight_send as sendSettlementTotal,b.actual_count as actualSettlementTotal,b.actual_receive as
        actualReceive
        from d_material_data b
        left join d_material_send_receive a on b.receive_id = a.id and a.is_deleted = 0
        where b.material_validity = 1
        and a.type = 1
        and b.is_deleted = 0
        and b.weight_unit = #{param1.unit}
        and b.company_id = #{param1.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size > 0">
            and b.project_id in
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        <if test="query.startDate != null">
            AND DATE_FORMAT(b.receive_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
        </if>
        <if test="query.endDate != null">
            AND DATE_FORMAT(b.receive_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and b.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test='query.receiveType == "4" or query.receiveType == "8" '>
            and b.receive_mode in (1,2)
        </if>
        <if test='query.receiveType == "5" or query.receiveType == "7" '>
            and b.receive_mode = 3
        </if>
        <if test='query.receiveType == "3" '>
            and 1 = 0
        </if>
        <if test='query.receiveType == "6" '>
            and 1 = 0
        </if>


        union all

        select m.receive_no,m.receive_id,m.project_id,m.supplier_id,m.receive_type,m.purchase_id
        ,o.truck_no
        ,n.material_id,n.send_settlement_total,n.actual_settlement_total,n.actual_settlement_total as actualReceive
        from d_mobile_receive m
        left join d_mobile_receive_total n on n.receive_id = m.receive_id
        left join d_mobile_receive_truck o on o.receive_id = m.receive_id
        where m.is_deleted = 0
        and n.settlement_unit = #{param1.unit}
        and m.company_id = #{param1.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size > 0">
            and m.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            AND DATE_FORMAT(m.create_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
        </if>
        <if test="query.endDate != null">
            AND DATE_FORMAT(m.create_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and n.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test='query.receiveType == "6" or query.receiveType == "8" '>
            and m.receive_type in (1,2)
        </if>
        <if test='query.receiveType == "3" or query.receiveType == "7" '>
            and m.receive_type = 3
        </if>
        <if test='query.receiveType == "4" '>
            and 1 = 0
        </if>
        <if test='query.receiveType == "5" '>
            and 1 = 0
        </if>
        ) x
    </select>

    <select id="checkByWeighId" resultType="string">
        select a.id
        from d_material_data a
        where a.weigh_id = #{weighId}
          and a.is_deleted = 0
    </select>

    <select id="findDataByWeighId"
            resultType="cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive">
        select *
        from d_material_data a
        where a.weigh_id = #{weighId}
          and a.is_deleted = 0
    </select>

    <select id="findAllWeighId" resultType="java.lang.String">
        select distinct weigh_id
        from d_material_data
        where is_deleted = 0
          and weigh_id is not null
    </select>

    <select id="findWeighDataByWeighId" resultType="java.lang.Integer">
        select count(id)
        from d_material_data
        where weigh_id = #{weighId}
          and is_deleted = 0
    </select>

    <select id="selectForCilent"
            resultType="cn.pinming.microservice.material.management.resposity.dto.StandardMaterialData">
        select a.id as
        dataId,a.company_id,a.project_id,a.supplier_id,a.type,a.receive_no,a.receiver,a.truck_no,a.operator,a.driver,a.remark
        ,b.name as supplierName,b.ext_code as clientSupplierId
        from d_material_data m
        left join d_material_send_receive a on a.id = m.receive_id and a.is_deleted = 0
        left join s_supplier b on b.id = a.supplier_id and b.is_deleted = 0
        where m.company_id = #{companyId}
        <if test="projectId != null">
            and m.project_id = #{projectId}
        </if>
        and m.is_deleted = 0
        and (m.push_state = 0 or m.push_state = 2)
        and m.weight_gross is not null
    </select>

    <select id="getOperateCenterOverview"
            resultType="cn.pinming.microservice.material.management.resposity.dto.OperateCenterSendReceiveDTO">
        select count(if(a.type = 1, 1, null)) weightReceiveCount,
               count(if(a.type = 2, 1, null)) weightSendCount
        from d_material_send_receive a
                 left join d_material_data b on a.id = b.receive_id
        where a.is_deleted = 0
          and b.is_deleted = 0
          and b.weight_gross is not null
    </select>

    <select id="getMobileReceiveCount" resultType="java.lang.Long">
        select count(*) as mobileReceiveCount
        from d_mobile_receive
        where is_deleted = 0
    </select>

    <select id="getMaterialCount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialCountVO">
        SELECT
        count(*) AS materialCount,
        <if test="query.timeType == null or query.timeType == 'month' or query.timeType == 'week'">
            DATE_FORMAT( a.create_time, '%Y-%m-%d' ) AS date
        </if>
        <if test="query.timeType == 'year'">
            DATE_FORMAT( a.create_time, '%Y-%m' ) AS date
        </if>
        <if test="query.timeType == 'today'">
            DATE_FORMAT( a.create_time, '%Y-%m-%d %H' ) AS date
        </if>
        FROM
        d_material_send_receive a
        LEFT JOIN d_material_data b ON a.id = b.receive_id
        WHERE
        a.is_deleted = 0 and b.weight_gross is not null
        AND b.is_deleted = 0
        <if test="query.type == 1">
            and (a.type = 1 or a.type = 2)
        </if>
        <if test="query.type == 2">
            and a.type = 1
        </if>
        <if test="query.type == 3">
            and a.type = 2
        </if>
        <if test="query.beginTime != null and query.endTime != null">
            and date_format(a.create_time, '%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{query.beginTime}, '%Y-%m-%d')
            and date_format(a.create_time, '%Y-%m-%d') <![CDATA[ <= ]]>  date_format(#{query.endTime}, '%Y-%m-%d')
        </if>
        GROUP BY
        date
        ORDER BY
        date
    </select>

    <select id="getMoblieMaterialCount"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialCountVO">
        SELECT
        count(*) AS materialCount,
        <if test="query.timeType == null or query.timeType == 'month' or query.timeType == 'week'">
            DATE_FORMAT( create_time, '%Y-%m-%d' ) AS date
        </if>
        <if test="query.timeType == 'year'">
            DATE_FORMAT( create_time, '%Y-%m' ) AS date
        </if>
        <if test="query.timeType == 'today'">
            DATE_FORMAT( create_time, '%Y-%m-%d %H' ) AS date
        </if>
        FROM
        d_mobile_receive
        WHERE
        is_deleted = 0
        <if test="query.beginTime != null and query.endTime != null">
            and date_format(create_time, '%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{query.beginTime}, '%Y-%m-%d')
            and date_format(create_time, '%Y-%m-%d') <![CDATA[ <= ]]>  date_format(#{query.endTime}, '%Y-%m-%d')
        </if>
        GROUP BY
        date
        ORDER BY
        date
    </select>

    <select id="getProjectMaterialRank"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ProjectMaterialRankVO">
        SELECT
        count(*) AS materialCount,
        a.project_id
        FROM
        d_material_send_receive a
        LEFT JOIN d_material_data b ON a.id = b.receive_id
        WHERE
        a.is_deleted = 0
        AND b.is_deleted = 0 and b.weight_gross is not null
        <if test="query.type == 1">
            and (a.type = 1 or a.type = 2)
        </if>
        <if test="query.type == 2">
            and a.type = 1
        </if>
        <if test="query.type == 3">
            and a.type = 2
        </if>
        <if test="query.beginTime != null and query.endTime != null">
            and date_format(a.create_time, '%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{query.beginTime}, '%Y-%m-%d')
            and date_format(a.create_time, '%Y-%m-%d') <![CDATA[ <= ]]>  date_format(#{query.endTime}, '%Y-%m-%d')
        </if>
        GROUP BY
        project_id
        order by materialCount desc
    </select>

    <select id="getMobileProjectMaterialRank"
            resultType="cn.pinming.microservice.material.management.resposity.vo.ProjectMaterialRankVO">
        SELECT
        count(*) AS materialCount,
        project_id
        FROM
        d_mobile_receive
        WHERE
        is_deleted = 0
        <if test="query.beginTime != null and query.endTime != null">
            and date_format(create_time, '%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{query.beginTime}, '%Y-%m-%d')
            and date_format(create_time, '%Y-%m-%d') <![CDATA[ <= ]]>  date_format(#{query.endTime}, '%Y-%m-%d')
        </if>
        GROUP BY
        project_id
        ORDER BY
        materialCount desc
    </select>

    <select id="listMaterialTruck"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialTruckVO">
        select truck_no, count(*) enterCount, sum(t1.weightNet) as total, max(lastDataId) lastDataId
        from (
        select truck_no,
        if(a.weight_net is null, 0, a.weight_net) as weightNet,
        first_value(a.id) over (partition by b.truck_no order by a.enter_time desc ) lastDataId,
        a.enter_time
        from d_material_data a,
        d_material_send_receive b
        where a.receive_id = b.id
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.weight_gross is not null
        and b.type in (1, 2)
        and a.company_id = #{companyId}
        <if test="truckNo != null">
            and truck_no like concat('%', #{truckNo}, '%')
        </if>
        <if test="startTime != null">
            and date_format(a.enter_time, '%Y-%m-%d') <![CDATA[ >= ]]> date_format(#{startTime}, '%Y-%m-%d')
        </if>
        <if test="endTime != null">
            and date_format(a.enter_time, '%Y-%m-%d')  <![CDATA[ <= ]]> date_format(#{endTime}, '%Y-%m-%d')
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        order by enter_time desc) t1
        group by truck_no
    </select>


    <select id="listMaterialTruckInfo"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialTruckInfoVO">
        select truck_no,
        b.receive_no,
        a.supplier_id,
        a.supplier_name,
        a.material_id,
        a.weight_gross,
        a.weight_tare,
        a.weight_net,
        a.weight_net,
        b.driver,
        b.type,
        a.enter_time,
        a.leave_time,
        a.id,
        b.car_type,
        b.id as receiveId
        from d_material_data a,
        d_material_send_receive b
        where a.receive_id = b.id
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.company_id = #{companyId}
        and a.weight_gross is not null
        and b.type in (1, 2)
        <if test="truckNo != null">
            and truck_no = #{truckNo}
        </if>
        <if test="projectId != null">
            and a.projectId = #{projectId}
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        order by a.enter_time desc
    </select>


    <select id="getTodayTruckStatistic"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MaterialTruckTodayStatisticVO">
        select count(*) as carCount,
        sum(if(a.weight_net is null, 0, a.weight_net)) as total,
        sum(if(timestampdiff(minute, a.enter_time, a.leave_time) is null, 0,
        abs(timestampdiff(minute, a.enter_time, a.leave_time))))
        as totalDiffSecond
        from d_material_data a,
        d_material_send_receive b
        where a.receive_id = b.id
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.weight_gross is not null
        and b.type in (1, 2)
        and a.enter_time <![CDATA[ >= ]]> date_format(now(), '%Y-%m-%d')
        and a.enter_time <![CDATA[ < ]]> date_format(date_add(now(), interval 1 day), '%Y-%m-%d')
        <if test="projectIds != null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>
    <select id="selectReceiveIdByExtNo" resultType="java.lang.String">
        select id
        from d_material_send_receive
        where confirm_ext_no = #{extNo}
          and project_id = #{projectId}
          and is_deleted = 0
    </select>
    <select id="selectYearOverviewDiff"
            resultType="cn.pinming.microservice.material.management.resposity.vo.KeyValVO">
        SELECT
        CASE deviation_status
        WHEN 0 THEN '正常'
        WHEN 1 THEN '超负差'
        WHEN 2 THEN '超正差'
        WHEN 99 THEN '无法确定偏差状态'
        ELSE '未知状态'
        END AS 'key',
        COUNT(*) AS 'val'
        FROM d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id and a.company_id = b.company_id and a.project_id =
        b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        and b.type = 1
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        AND a.receive_time <![CDATA[ >= ]]> DATE_FORMAT(NOW(), '%Y-01-01 00:00:00')
        AND a.receive_time <![CDATA[ < ]]> DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01 00:00:00')
        AND a.deviation_status IS NOT NULL
        GROUP BY deviation_status
    </select>
    <select id="selectCurrentMonthCount" resultType="java.lang.Integer">
        select count(*)
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and a.company_id = b.company_id and a.project_id =
        b.project_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        and b.type = 1
        and b.is_addition = #{isAddition}
        <if test="type == 1">
            AND a.receive_time <![CDATA[ >= ]]>  DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00')
            AND a.receive_time <![CDATA[ < ]]>  DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        </if>
        <if test="type == 2">
            AND a.receive_time <![CDATA[ >= ]]>  DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00')
            AND a.receive_time <![CDATA[ < ]]>  DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-%m-01 00:00:00')
        </if>
    </select>
</mapper>
