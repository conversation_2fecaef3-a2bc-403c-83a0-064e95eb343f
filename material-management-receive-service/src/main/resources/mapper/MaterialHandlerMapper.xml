<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialHandlerMapper">

    <select id="selectListByProjectIdAndHandleType"
            resultType="cn.pinming.microservice.material.management.resposity.dto.HandlerDTO">
        select id, handler_id, handler_name
        from d_material_handler
        where project_id = #{projectId} and handle_type = #{handleType} and is_deleted = 0

    </select>
    <select id="selectListByCompanyIdAndHandleType"
            resultType="cn.pinming.microservice.material.management.resposity.dto.HandlerDTO">
        select id, handler_id, handler_name
        from d_material_handler
        where company_id = #{companyId} and handle_type = #{handleType} and is_deleted = 0
        and project_id is not null
    </select>
    <select id="selectByCompanyIdAndProjectIdIsNullAndHandleType"
            resultType="cn.pinming.microservice.material.management.resposity.dto.HandlerDTO">
        select id, handler_id, handler_name
        from d_material_handler
        where company_id = #{companyId} and handle_type = #{handleType} and is_deleted = 0
        and project_id is null
    </select>
</mapper>
