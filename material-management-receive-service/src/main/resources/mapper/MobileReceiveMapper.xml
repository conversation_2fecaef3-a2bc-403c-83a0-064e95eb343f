<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveMapper">

    <select id="selectReceivePage"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MobileReceiveCardVO">
        select distinct a.receive_id as
        id,a.receive_no,a.receive_status,a.receiver,a.project_id,a.purchase_id,a.receive_type,a.contract_id,a.supplier_id,a.deviation_status
        ,c.truck_no,c.truck_time as createTime,a.is_revise
        from d_mobile_receive a
        left join d_mobile_receive_truck c on c.receive_id = a.receive_id
        left join d_mobile_receive_total m on m.receive_id = a.receive_id
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectId != null and projectId != '' ">
            and a.project_id = #{projectId}
        </if>
        <if test="projectIds != null and projectIds.size != 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="supplierId != null and supplierId != '' ">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="startTime != null">
            and date_format(c.truck_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and date_format(c.truck_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="receiveNo != null and receiveNo != '' ">
            and a.receive_no like concat('%', #{receiveNo}, '%')
        </if>
        <if test="categoryId != null">
            and m.category_id = #{categoryId}
        </if>
        <if test="receiveStatus != null ">
            and a.receive_status = #{receiveStatus}
        </if>
        <if test="deviationStatus != null">
            and a.deviation_status = #{deviationStatus}
        </if>
        <if test="receiveType != null ">
            and a.receive_type = #{receiveType}
        </if>
        <if test="sort == 1">
            order by a.create_time desc
        </if>
        <if test="sort != 1">
            order by a.create_time asc
        </if>
    </select>
    <select id="detail" resultType="cn.pinming.microservice.material.management.resposity.vo.ReceiveCardDetailVO">
        select a.receive_type
             , a.receive_no
             , a.receive_status
             , a.receiver
             , a.`comment`
             , a.create_time
             , a.project_id as receiverProject
             , a.deviation_status
             , a.supplier_id
             , b.order_no
             , b.position
             , b.create_id
             , b.remark
             , b.id         as purchaseId
             , c.truck_no
             , c.truck_time
             , c.truck_pic
             , c.goods_pic
             , c.send_pic
             , c.longitude
             , c.latitude
             , c.location
             , d.name       as contractName
             , d.id         as contractId
        from d_mobile_receive a
                 left join d_purchase_order b on a.purchase_id = b.id
                 left join d_mobile_receive_truck c on a.receive_id = c.receive_id
                 left join d_purchase_contract d on a.contract_id = d.id
        where a.receive_id = #{receiveId}
          and a.is_deleted = 0
    </select>

    <select id="selectPageForVerify"
            resultType="cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVerifyVO">
        select
        a.id,a.material_id,a.send_settlement_total,a.actual_settlement_total,a.unit,a.deviation_rate,a.deviation_status,a.reconciliation_id,
        b.receive_no,b.receive_type,b.purchase_id,
        c.truck_no,c.truck_time
        from d_mobile_receive_total a
        left join d_mobile_receive b on b.receive_id = a.receive_id and b.is_deleted = 0
        left join d_mobile_receive_truck c on c.receive_id = a.receive_id and c.is_deleted = 0
        where b.is_deleted = 0
        <if test="query.projectId != null and query.projectId != '' ">
            and a.project_id = #{query.projectId}
        </if>
        <if test="query.projectIds != null and query.projectIds.size != 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.supplierId != null and query.supplierId != '' ">
            and b.supplier_id = #{query.supplierId}
        </if>
        <if test="query.startTime != null">
            and c.truck_time <![CDATA[ >= ]]> #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and c.truck_time <![CDATA[ <= ]]> #{query.endTime}
        </if>
        <if test="query.receiveType != null">
            and b.receive_type = #{query.receiveType}
        </if>
        <if test="query.reconciliationId != null and query.reconciliationId != ''">
            and b.reconciliation_id = #{query.reconciliationId}
        </if>
    </select>
    <select id="countSummary"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SummaryAnalysisVO">
        SELECT
        count( date_format( a.create_time, '%Y%m' ) = date_format( now(), '%Y%m' ) OR NULL ) AS monthReceiveCarsNum,
        count( a.id ) AS countReceiveCarsNum,
        sum(IF(a.deviation_status = 1 AND date_format( a.create_time, '%Y%m' ) = date_format( now(), '%Y%m' ), 1, 0)) as
        monthMinusCarsNum,
        sum(IF(a.deviation_status = 1, 1, 0)) AS countMinusCarsNum
        from d_mobile_receive a
        WHERE a.is_deleted = 0
        AND a.company_id = #{query.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectSupplierAnalysisByMobileQuery"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO">
        select sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as
        weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectSupplierMobileAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO">
        select a.supplier_id,sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as
        weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id having supplier_id is not null

    </select>
    <select id="selectCurrentMonthCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM d_mobile_receive a
        WHERE a.is_deleted = 0
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="type == 1">
            AND a.create_time <![CDATA[ >= ]]>  DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00')
            AND a.create_time <![CDATA[ < ]]>  DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
        </if>
        <if test="type == 2">
            AND a.create_time <![CDATA[ >= ]]>  DATE_FORMAT(NOW(), '%Y-01-01 00:00:00')
            AND a.create_time <![CDATA[ < ]]>  DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01 00:00:00')
        </if>
    </select>
</mapper>
