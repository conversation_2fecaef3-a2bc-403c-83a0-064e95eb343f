<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialSendReceiveMultiMapper">

<select id="pageByQuery" resultType="cn.pinming.microservice.material.management.resposity.vo.MultiVO">
    select *
    from d_material_send_receive_multi a
    where a.is_deleted = 0
    and a.company_id = #{query.companyId}
    and a.type = #{query.type}
    <if test="query.projectIds != null and query.projectIds.size != 0">
        and a.project_id in
        <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </if>
    <if test="query.projectId != null and query.projectId != ''">
        and a.project_id = #{query.projectId}
    </if>
    <if test="query.receiveNo != null and query.receiveNo != ''">
        and a.receive_no like concat('%', #{query.receiveNo}, '%')
    </if>
    <if test="query.truckNo != null and query.truckNo != ''">
        and a.truck_no like concat('%', #{query.truckNo}, '%')
    </if>
    <if test="query.pushState != null">
        and a.push_state = #{query.pushState}
    </if>
    <if test="query.startTime != null">
        and a.receive_time &gt; #{query.startTime}
    </if>
    <if test="query.endTime != null">
        and a.receive_time &lt; #{query.endTime}
    </if>
</select>
</mapper>
