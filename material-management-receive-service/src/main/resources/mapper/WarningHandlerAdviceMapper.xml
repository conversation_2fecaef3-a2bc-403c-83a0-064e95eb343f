<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.WarningHandlerAdviceMapper">

    <select id="selectLastHandlerAdvice" resultType="cn.pinming.microservice.material.management.resposity.dto.HandleAdviceDTO">
        select t1.warning_id, t1.handler_advice as advice from(
                    select warning_id, max(update_time) update_time from d_warning_handler_advice
                    where is_deleted = 0
                    <if test="warningIds != null and warningIds.size > 0">
                        and warning_id in
                        <foreach collection="warningIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>

                    </if>
                   group by warning_id) t
         left join d_warning_handler_advice t1 on t.warning_id = t1.warning_id and t.update_time = t1.update_time and t1.is_deleted = 0
    </select>
</mapper>
