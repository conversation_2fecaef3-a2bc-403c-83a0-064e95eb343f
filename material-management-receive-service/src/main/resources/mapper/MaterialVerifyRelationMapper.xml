<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.resposity.mapper.MaterialVerifyRelationMapper">

    <select id="check" resultType="integer">
        select count(a.id)
        from d_material_verify_relation a
        left join d_material_verify b on b.id = a.verify_id and b.is_deleted = 0
        where a.receive_data_id = #{id}
        and b.status = 0
        and a.is_deleted = 0
    </select>

</mapper>
