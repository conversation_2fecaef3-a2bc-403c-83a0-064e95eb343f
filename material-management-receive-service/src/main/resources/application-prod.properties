nacos.config.username=${nacos_username:nacos}
nacos.config.password=${nacos_password:nacos}
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.logEnable=true
nacos.config.data-ids=material-common,material-receive-management
nacos.config.group=material
nacos.config.type=properties
nacos.config.max-retry=10
nacos.config.auto-refresh=true
nacos.config.config-retry-time=2333
nacos.config.config-long-poll-timeout=46000
nacos.config.enable-remote-sync-config=false
nacos.config.server-addr=${nacos_server-addr:${nacos_server_addr:172.16.9.88:18848}}
nacos.config.namespace=${nacos_namespace:bf644fca-1276-415a-89de-428331e96a46}