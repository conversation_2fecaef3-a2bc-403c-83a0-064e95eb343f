package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 偏差下钻数据
 *
 * <AUTHOR>
 * @since 2022/4/12 19:55
 */
@Data
public class DeviationSecondVO {

    @ApiModelProperty(value = "单位id")
    private Integer projectId;

    @ApiModelProperty(value = "单位名称")
    private String projectName;

    @ApiModelProperty(value = "偏差详情")
    private List<DeviationSecondDetailVO> deviationSecondDetailVOList;
}
