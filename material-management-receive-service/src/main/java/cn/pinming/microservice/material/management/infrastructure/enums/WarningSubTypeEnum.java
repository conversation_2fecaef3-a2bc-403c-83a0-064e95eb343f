package cn.pinming.microservice.material.management.infrastructure.enums;


import static cn.pinming.microservice.material.management.infrastructure.enums.WarningTypeEnum.*;

/**
 * 预警类型子分类枚举
 * <AUTHOR>
 * @date 2022/6/14
 */
public enum WarningSubTypeEnum {

    WEIGH_CONVERT_ONE(WEIGH_CONVERT, (byte) 1, "合同约定系数为{}，收料时设置系数为{}，实际使用系数为{}"),
    WEIGH_CONVERT_TWO(WEIGH_CONVERT, (byte) 2, "未设置转换系数"),
    WEIGH_CONVERT_THREE(WEIGH_CONVERT, (byte) 3, "转换系数上传值异常,且无合同信息"),
    WEIGH_CONVERT_FOUR(WEIGH_CONVERT, (byte) 4, "转换系数上传值异常:{}"),
    TARE_GROSS_WEIGH_ONE(TARE_GROSS_WEIGH, (byte) 1, "缺少毛重"),
    TARE_GROSS_WEIGH_TWO(TARE_GROSS_WEIGH, (byte) 2, "车辆未及时出场，导致缺少皮重"),
    RECEIVE_NUMBER_ONE(RECEIVE_NUMBER, (byte) 1, "应收数量录入错误"),
    CHARGE_UNIT_ONE(CHARGE_UNIT, (byte) 1, "合同约定结算单位为{}，收料时设置结算单位为{}，实际使用结算单位为{}"),
    CHARGE_UNIT_TWO(CHARGE_UNIT, (byte) 2, "合同约定结算单位为{}，收料时未设置结算单位，实际使用结算单位为{}"),
    CHARGE_UNIT_THREE(CHARGE_UNIT, (byte) 3, "结算单位上传值为空,且无合同信息"),
    CHARGE_UNIT_FOUR(CHARGE_UNIT, (byte) 4, "结算单位上传值为空"),
    NEGATIVE_DEVIATION_ONE(NEGATIVE_DEVIATION, (byte) 1, "合同约定偏差阈值为{}至{}，实际偏差率为{}"),
    INVALID_WEIGHT_ONE(INVALID_WEIGHT, (byte) 1, "收货记录未设置收货物料"),
    INVALID_WEIGHT_TWO(INVALID_WEIGHT, (byte) 2, "地磅或仪表异常，导致毛重值为负"),
    INVALID_WEIGHT_THREE(INVALID_WEIGHT, (byte) 3, "地磅或仪表异常，导致皮重值为负"),
    INVALID_WEIGHT_FOUR(INVALID_WEIGHT, (byte) 4, "车辆重复过磅"),
    MATERIAL_NAME_ONE(MATERIAL_NAME, (byte) 1, "物料在材料库中不存在"),
    MATERIAL_NAME_TWO(MATERIAL_NAME, (byte) 2, "物料在采购单内不存在"),
    // 供应商名称、车牌号、进场时间、超时时长
    TIMEOUT_NO_OUT_ONE(TIMEOUT_NO_OUT, (byte) 1, "【{}】送货车辆【{}】于【{}】进场未在【{}】内完成出场过磅"),
    // 供应商名称、车牌号、对应车辆生成的车次计划送货时间
    TIMEOUT_NO_ENTER_ONE(TIMEOUT_NO_ENTER, (byte) 1, "【{}】送货车辆【{}】计划于【{}】进场，未在7天内送货到场"),
    TARE_DEVIATION_ONE(TARE_DEVIATION, (byte) 1, "车辆皮重异常，超出合理范围{}"),
    WEIGH_INTEGRITY_ONE(WEIGH_INTEGRITY, (byte) 1, "发料称重数据不完整"),
    // 供应商名称、车牌号
    WEIGH_REPETITION_ONE(WEIGH_REPETITION, (byte) 1, "【{}】送货车辆【{}】两次过磅间隔时间过短存在重复过磅"),
    MATERIAL_OUT_ONE(MATERIAL_OUT, (byte) 1, "")
    ;

    private WarningTypeEnum typeEnum;
    private byte type;
    private String desc;

    WarningSubTypeEnum(WarningTypeEnum typeEnum, byte type, String desc) {
        this.typeEnum = typeEnum;
        this.type = type;
        this.desc = desc;
    }

    public byte subType() {
        return this.type;
    }

    public String desc() {
        return this.desc;
    }

}
