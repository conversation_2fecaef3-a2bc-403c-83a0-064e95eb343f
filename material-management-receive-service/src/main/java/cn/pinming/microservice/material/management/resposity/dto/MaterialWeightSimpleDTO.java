package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialWeightSimpleDTO {
    @ApiModelProperty(value = "地磅ID")
    private String weighbridgeId;

    @ApiModelProperty(value = "收货单ID")
    private String receiveId;

    @ApiModelProperty(value = "地磅名称")
    private String deviceName;

    @ApiModelProperty(value = "过磅重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "过磅序号")
    private Byte weightNo;

    @ApiModelProperty(value = "类型，1：收货；2：发货")
    private Byte type;

}
