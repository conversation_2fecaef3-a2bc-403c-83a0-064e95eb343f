package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PicDTO {

    @ApiModelProperty(value = "收料明细id")
    private String id;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "进场照片")
    private String enterPic;

    @ApiModelProperty(value = "出场照片")
    private String leavePic;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

}
