package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialSimpleDTO {

    @ApiModelProperty(value = "下单量")
    private BigDecimal count;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;
}
