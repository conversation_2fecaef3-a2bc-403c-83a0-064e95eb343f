package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 过磅情况query
 * <AUTHOR>
 */
@Data
public class WeighInfoQuery {
    private Integer companyId;

    @ApiModelProperty("组织顶点")
    @NotNull(message = "组织顶点不能为空")
    private Integer point;

    @ApiModelProperty("项目范围")
    private List<Integer> projectIdList;

    @ApiModelProperty("收货开始日期")
    private LocalDate startDate;

    @ApiModelProperty("收货结束日期")
    private LocalDate endDate;
}
