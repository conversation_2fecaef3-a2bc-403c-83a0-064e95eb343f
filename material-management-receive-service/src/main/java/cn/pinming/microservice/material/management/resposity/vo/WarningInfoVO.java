package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/1/17 13:10
 */
@Data
public class WarningInfoVO {
    @ApiModelProperty(value = "预警id")
    private String warningId;

    @ApiModelProperty(value = "预警类型")
    private Set<Byte> warningType;

    @ApiModelProperty(value = "预警来源")
    private String warningSource;

    @ApiModelProperty(value = "收料类型")
    private Byte receiveType;

    @ApiModelProperty(value = "预警来源记录id")
    private String warningSourceId;

    @ApiModelProperty(value = "预警来源记录编号")
    private String warningSourceNo;

    @ApiModelProperty(value = "发生项目")
    private String sourceProject;

    @ApiModelProperty(value = "发生项目id")
    private Integer sourceProjectId;

    @ApiModelProperty(value = "预警时间")
    private LocalDateTime warningTime;

    @ApiModelProperty(value = "处理人姓名")
    private String handlerName;

    @ApiModelProperty(value = "处理人建议")
    private String handlerAdvice;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handlerTime;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    private Byte warningStatus;

    @ApiModelProperty(value = "是否能处理")
    private Boolean enableHandle;

    @ApiModelProperty(value = "预警详情信息")
    List<WarningDetailVO> warningDetailList;

}
