package cn.pinming.microservice.material.management.service.impl;

import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveTruck;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveTruckMapper;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVO;
import cn.pinming.microservice.material.management.service.IMobileReceiveTruckService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <p>
 * 移动收料货车表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:56
 */
@Service
public class MobileReceiveTruckServiceImpl extends ServiceImpl<MobileReceiveTruckMapper, MobileReceiveTruck> implements IMobileReceiveTruckService {

    @Override
    public List<MobileReceiveVO> listByPurchaseId(String purchaseId){
        return this.getBaseMapper().listByPurchaseId(purchaseId);
    }

}
