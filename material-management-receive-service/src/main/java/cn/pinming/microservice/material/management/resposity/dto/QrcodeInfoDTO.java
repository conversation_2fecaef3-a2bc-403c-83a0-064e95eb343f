package cn.pinming.microservice.material.management.resposity.dto;

import cn.pinming.microservice.material.management.resposity.form.StandardMaterialBaseForm;
import cn.pinming.microservice.material.management.resposity.vo.FeedBackVO;
import cn.pinming.microservice.material.management.resposity.vo.PurchaseSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2022/4/14 15:20
 */
@Data
public class QrcodeInfoDTO extends StandardMaterialBaseForm {
    @ApiModelProperty("修订提示: true 存在 false 不存在")
    private boolean exist;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "采购单id")
    private String orderId;

    @ApiModelProperty(value = "收货单明细id")
    private String id;

    @ApiModelProperty(value = "采购单详情")
    private PurchaseSimpleVO purchaseSimpleVO;

   @ApiModelProperty(value = "收料明细信息（已扫码或终端上传）")
    private FeedBackVO feedBackVO;

   @ApiModelProperty(value = "过磅类型")
    private Byte weighType;

}
