package cn.pinming.microservice.material.management.service;


import cn.pinming.material.v2.model.dto.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.form.MobileMaterialBatchForm;
import cn.pinming.microservice.material.management.resposity.form.SDKStandardMaterialForm;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SDKHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.SuccessOrFailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO;
import cn.pinming.microservice.material.management.resposity.vo.WeighReceiveVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 收货/发货明细 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
public interface IMaterialDataService extends IService<MaterialData> {
    String saveSdk(SDKStandardMaterialForm form);

    WeighReceiveVO showWeightDetail(MaterialWeighQuery query);

    SDKHistoryVO sdkHistory(Byte type);

    void refreshPic(String id);

    WeighDataConfirmDetailDTO confirmDetail(String id);

    /**
     *
     * @param type        收发料类型 1：收货；2：发货
     * @param receiveType 1 按合同收料  2 按采购单收料
     * @return
     */
    MaterialReviseDetailVO mobileHistoryByType(Byte type, Byte receiveType);

    SuccessOrFailVO batch(List<MobileMaterialBatchForm> list);

    SupplierAnalysisVO getSupplierAnalysisByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> getSupplierAnalysisPageVO(SupplierAnalysisQuery analysisQuery);

    List<SupplierRankVO> deductRankByQuery(SupplierRankQuery query);

    List<SupplierRankVO> deductProportionByQuery(SupplierRankQuery query);

    List<SupplierRankVO> deductTotalRankByQuery(SupplierRankQuery query);

    List<SupplierRankVO> deductTotalProportionByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyRankListByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyAllRankListByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalRankByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyProportionByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyAllProportionByQuery(SupplierRankQuery query);
}
