package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description
 */
@Data
public class MaterialConfigForm {

    @ApiModelProperty(value = "分组名称")
    @NotNull
    private String name;

    @ApiModelProperty(value = "二级分类ID，多个使用英文逗号分隔")
    @NotNull
    private String categoryIds;

}
