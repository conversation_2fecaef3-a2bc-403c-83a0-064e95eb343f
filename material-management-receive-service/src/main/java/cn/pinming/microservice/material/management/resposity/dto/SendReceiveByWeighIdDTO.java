package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SendReceiveByWeighIdDTO {
    @ApiModelProperty(value = "收货单id")
    private String id;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseId;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "1 临时收料，2 报备收料，3 无归属收料")
    private Byte receiveMode;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;
}
