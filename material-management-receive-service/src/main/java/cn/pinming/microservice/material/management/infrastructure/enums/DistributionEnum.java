package cn.pinming.microservice.material.management.infrastructure.enums;

public enum DistributionEnum {
    ONE(1, "按随车面单量"),
    TWO( 2, "按理重");

    /**
     * 状态值
     */
    private final int value;
    /**
     * 状态的描述
     */
    private final String description;

    DistributionEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(int value) {
        for (DistributionEnum statusEnum : DistributionEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
