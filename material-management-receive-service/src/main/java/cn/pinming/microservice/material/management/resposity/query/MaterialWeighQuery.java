package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 地磅收货列表VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialWeighQuery extends BaseQuery {
    @ApiModelProperty(value = "data明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "物资名称、物料名称")
    private String materialName;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    @ApiModelProperty(value = "是否修订: 1.有修订 2.未修订")
    private Byte isRevise;

    @ApiModelProperty(value = "是否对账: 1.已对账 2.未对账")
    private Byte isVerify;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;

    @ApiModelProperty(value = "收料类型 1 按采购单 2 按合同 3 仅关联供应商 ")
    private Byte receiveType;

    @ApiModelProperty(value = "类型，1：收货；2：发货")
    private Byte type;

    @ApiModelProperty(value = "收货单位(发料查询用)")
    private String receiveProject;

    @ApiModelProperty(value = "对账id")
    private String reconciliationId;

    @ApiModelProperty(value = "对账id")
    private String verifyId;

    @ApiModelProperty(value = "终端称重记录id")
    private String weighId;

    @ApiModelProperty(value = "收料确认方式 1 扫码组装 2 司机自助确认")
    private Integer confirmType;

    @ApiModelProperty(value = "数据类型 1 复磅情况 (基石确认单同步至桩桩判断用)")
    private Integer mixingType;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;
}
