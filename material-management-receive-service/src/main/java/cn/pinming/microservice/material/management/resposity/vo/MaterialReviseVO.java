package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/8 16:16
 */
@Data
public class MaterialReviseVO {

    @ApiModelProperty(value = "修正数据按钮是否显示")
    private Boolean showReviseBtn;

    @ApiModelProperty(value = "修正数据")
    private List<MaterialReviseDataVO> materialReviseDataVOList;

}
