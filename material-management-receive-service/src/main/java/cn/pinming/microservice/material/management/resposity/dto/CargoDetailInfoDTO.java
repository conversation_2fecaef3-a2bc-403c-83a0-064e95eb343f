package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CargoDetailInfoDTO {
    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "二级分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty("是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;
}
