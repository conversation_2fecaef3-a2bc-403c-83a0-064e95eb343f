package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商分析VO.
 *
 * <AUTHOR>
 * @version 2021/9/8 5:23 下午
 */
@Data
public class SupplierAnalysisVO {

    @ApiModelProperty("累计采购下单量")
    private BigDecimal purchaseAmount;

    @ApiModelProperty("累计面单应收量")
    private BigDecimal weightSendAmount;

    @ApiModelProperty("累计实际量")
    private BigDecimal weightActualAmount;

    @ApiModelProperty("累计偏差量")
    private BigDecimal deviationAmount;

    @ApiModelProperty("累计偏差比例")
    private BigDecimal deviationRateAmount;

    @ApiModelProperty("累计实收数量")
    private BigDecimal actualReceive;

}
