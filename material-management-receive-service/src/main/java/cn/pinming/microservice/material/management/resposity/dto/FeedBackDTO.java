package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FeedBackDTO {

    @ApiModelProperty(value = "收货明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "设置称重转换系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "面单应收量；发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "发货时的收货单位，收料时的发货单位")
    private Integer supplierId;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

    @ApiModelProperty(value = "结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "接收方")
    private String supplierName;

    @ApiModelProperty(value = "采购单号")
    private String orderNo;

    @ApiModelProperty(value = "采购单id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "数据库-计划使用部位")
    private String position;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "实重(吨)")
    private BigDecimal weightActual;
}
