package cn.pinming.microservice.material.management.resposity.mapper;


import cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO;
import cn.pinming.microservice.material.management.resposity.dto.DeviationOverviewSecondDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataDetailDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataExpandDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialWeighInfoDTO;
import cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardDTO;
import cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewCardSecondDTO;
import cn.pinming.microservice.material.management.resposity.dto.ReceiveOverviewSecondDTO;
import cn.pinming.microservice.material.management.resposity.dto.StatisticsUnitDTO;
import cn.pinming.microservice.material.management.resposity.dto.SummaryDeliverySecondDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.KeyValVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialDatasVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO;
import cn.pinming.microservice.material.management.resposity.vo.SDKHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryDeliveryVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO;
import cn.pinming.microservice.material.management.vo.MaterialDataStandardVO;
import cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 收货/发货明细 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
public interface MaterialDataMapper extends BaseMapper<MaterialData> {
    @InterceptorIgnore(tenantLine = "true")
    MaterialDataDetailDTO selectWeightDetail(MaterialWeighQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<MaterialDatasVO> selectWeighDetailsWithPurchase(MaterialWeighQuery query);

    @InterceptorIgnore(tenantLine = "true")
    IPage<MaterialWeighInfoDTO> queryWeightReceiveInfo(@Param("query") MaterialWeighQuery query);

    SDKHistoryVO sdkHistory(@Param("projectId") Integer currentProjectId, @Param("type") Byte type, @Param("mid") String mid);

    List<MaterialDataStandardVO> getDataByExtNo(@Param("list") List<String> extNoList);

    List<MaterialDataStandardVO> getDataByIds(@Param("list") List<String> ids);

    List<MaterialDataStandardVO> getDataByPurchaseId(@Param("purchaseOrderId") String purchaseOrderId);

    CargoDetailInfoDTO getCargo(@Param("cargoId") String cargoId);

    int selectVerifyByDataId(@Param("id") String id, @Param("companyId") Integer currentCompanyId);

    MaterialDataExpandDTO mobileHistoryByType(@Param("type") Byte type, @Param("receiveType") Byte receiveType, @Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

    CargoDetailInfoDTO getCargoFromMixing(@Param("cargoId") String cargoId);

    CargoDetailInfoDTO getCargoFromPurchase(@Param("cargoId") String cargoId);

    CargoDetailInfoDTO getCargoFromContract(@Param("cargoId") String cargoId);

    List<MaterialDataResourceDTO> innerSync(@Param("companyIdList") List<String> companyIdList, @Param("projectIdList") List<String> projectIdList);

    List<MaterialDataResourceDTO> innerSyncById(@Param("id") String id, @Param("receiveId") String receiveId, @Param("list") List<PushRangeForm> list);

    List<MaterialDataExpandDTO> warning(@Param("ids") List<String> ids);

    List<CategoryReceiveVO> selectReceiveListByQuery(SupplierAnalysisQuery query);

    SupplierAnalysisVO selectSupplierAnalysisByQuery(SupplierAnalysisQuery query);

    SupplierAnalysisVO selectSupplierAnalysisUnionByQuery(SupplierAnalysisQuery query);

    List<StatisticsUnitDTO> listUnitByCategoryIds(@Param("categoryIds") List<String> categoryIds);

    List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierUnionAnalysisPageVO(SupplierAnalysisQuery query);

    /**
     * 总览-收料总览-大宗材-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveMode 地磅收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> wagonReceiveOverviewCard(@Param("start") String start,
                                                          @Param("end") String end,
                                                          @Param("receiveModes") Integer receiveMode,
                                                          @Param("categoryIds") List<String> categoryIds,
                                                          @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveMode 地磅收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> wagonReceiveOverviewOther(@Param("start") String start,
                                                           @Param("end") String end,
                                                           @Param("receiveModes") List<Byte> receiveMode,
                                                           @Param("categoryIds") List<String> categoryIds,
                                                           @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveType 移动收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> mobileReceiveOverviewOther(@Param("start") String start,
                                                            @Param("end") String end,
                                                            @Param("receiveTypes") List<Byte> receiveType,
                                                            @Param("categoryIds") List<String> categoryIds,
                                                            @Param("projectIds") List<Integer> projectIds);


    /**
     * 总览-收料总览-大宗材-柱状图下钻-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveType 移动收料类型
     * @param materialIds 材料ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewCardSecond(@Param("start") String start,
                                                                       @Param("end") String end,
                                                                       @Param("unit") String unit,
                                                                       @Param("receiveTypes") List<Byte> receiveType,
                                                                       @Param("materialIds") Set<Integer> materialIds,
                                                                       @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-柱状图下钻-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveMode 地磅收料类型
     * @param categoryId  二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewOtherSecond(@Param("start") String start,
                                                                       @Param("end") String end,
                                                                       @Param("unit") String unit,
                                                                       @Param("receiveModes") List<Byte> receiveMode,
                                                                       @Param("categoryId") Integer categoryId,
                                                                       @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-柱状图下钻-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveType 移动收料类型
     * @param categoryId  二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewOtherSecond(@Param("start") String start,
                                                                        @Param("end") String end,
                                                                        @Param("unit") String unit,
                                                                        @Param("receiveTypes") List<Byte> receiveType,
                                                                        @Param("categoryId") Integer categoryId,
                                                                        @Param("projectIds") List<Integer> projectIds);


    /**
     * 总览-收料总览-大宗材-柱状图下钻-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveMode 地磅收料类型
     * @param materialIds 材料ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewCardSecond(@Param("start") String start,
                                                                      @Param("end") String end,
                                                                      @Param("unit") String unit,
                                                                      @Param("receiveModes") List<Byte> receiveMode,
                                                                      @Param("materialIds") Set<Integer> materialIds,
                                                                      @Param("projectIds") List<Integer> projectIds);


    /**
     * 总览-收料总览-大宗材-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveType 移动收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> mobileReceiveOverviewCard(@Param("start") String start,
                                                           @Param("end") String end,
                                                           @Param("receiveTypes") List<Byte> receiveType,
                                                           @Param("categoryIds") List<String> categoryIds,
                                                           @Param("projectIds") List<Integer> projectIds);

    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationVO> selectDeviationByQuery(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    List<ReceiveDeviationSummaryVO> selectDeviationSummaryByQuery(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    List<SummaryDeliveryVO> listSummaryDeliveryByQuery(SummaryDeliveryQuery query);

    List<KeyValVO> selectOverviewMonthNegativeDiffAmount(@Param("projectIdList") List<Integer> projectIdList);

    List<KeyValVO> selectOverviewMonthNegativeTruckAmount(@Param("projectIdList") List<Integer> projectIdList);

    List<KeyValVO> selectOverviewMonthTotalDiffAmount(@Param("projectIdList") List<Integer> projectIdList);

    List<KeyValVO> selectOverviewMonthTotalTruckAmount(@Param("projectIdList") List<Integer> projectIdList);

    List<Integer> listCategoryIdList(SummaryDeliveryQuery query);

    List<SummaryDeliverySecondDetailDTO> listDetailList(@Param("query") SummaryDeliveryQuery query, @Param("categoryIdList") List<Integer> categoryIdList);

    List<ReceiveOverviewSecondDTO> selectReceiveOverviewSecondByQuery(@Param("query") SummaryDeliveryQuery query, @Param("type") byte type);

    List<KeyValVO> selectOverviewTotal(@Param("projectIdList") List<Integer> projectIdList);

    List<DeviationOverviewSecondDTO> selectDeviationSecondByQuery(@Param("query") SummaryDeliveryQuery query);

    List<SupplierRankVO> deductRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductTotalRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductTotalProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyRankListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyAllRankListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyRankRateTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyMobileRankRateTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyAllRankRateTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyRankTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyMobileRankTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyAllRankTopListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyAllProportionByQuery(@Param("query") SupplierRankQuery query);
}
