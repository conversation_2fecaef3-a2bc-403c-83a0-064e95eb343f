package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialReceiveDTO {

    @ApiModelProperty(value = "供应商id")
    private String supplierName;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "车次")
    private Integer amount;

    @ApiModelProperty(value = "单位")
    private String weightUnit;

    @ApiModelProperty(value = "二级分类名称")
    private String categoryName;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "二级分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

}
