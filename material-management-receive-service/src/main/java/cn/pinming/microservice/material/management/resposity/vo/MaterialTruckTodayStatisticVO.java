package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/4/13 15:02
 */
@Data
public class MaterialTruckTodayStatisticVO {

    @ApiModelProperty("今日过磅车次")
    private Long carCount;

    @ApiModelProperty("今日载货总量")
    private BigDecimal total;

    @ApiModelProperty("总共卸料用时")
    private BigDecimal totalDiffSecond;

    @ApiModelProperty("平均卸料用时")
    private BigDecimal averageDiffSecond;

    @ApiModelProperty("平均时间")
    private String averageDiffStr;
}
