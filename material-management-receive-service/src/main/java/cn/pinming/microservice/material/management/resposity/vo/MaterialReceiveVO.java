package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/9 8:00 下午
 */
@Data
@ExcelIgnoreUnannotated
public class MaterialReceiveVO extends SimpleSupplierDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("收料单号")
    @ExcelProperty("收料单号")
    private String receiveNo;

    @ApiModelProperty("项目名称")
    @ExcelProperty("项目名称")
    private String projectTitle;

    @ApiModelProperty("供应商名称")
    @ExcelProperty("供应商")
    private String supplierTitle;

    @ApiModelProperty("车牌")
    @ExcelProperty("车牌")
    private String truckNo;

    @ExcelProperty("收货物料")
    @ApiModelProperty("收货物料")
    private String name;

    @ApiModelProperty("二级分类名称")
    private String categoryName;

    @ApiModelProperty("材料ID")
    private Integer materialId;

    @ApiModelProperty("材料名称")
    private String materialName;

    @ApiModelProperty("规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "毛重(吨)")
    @ExcelProperty("毛重(吨)")
    private String weightGross;

    @ApiModelProperty(value = "皮重(吨)")
    @ExcelProperty("皮重(吨)")
    private String weightTare;

    @ApiModelProperty(value = "净重(吨)")
    @ExcelProperty("净重(吨)")
    private String weightNet;

    @ApiModelProperty(value = "扣重(吨)")
    @ExcelProperty("扣重(吨)")
    private String weightDeduct;

    @ApiModelProperty(value = "实重(吨)")
    @ExcelProperty("实重(吨)")
    private String weightActual;

    @ApiModelProperty(value = "合同转换系数")
    @ExcelProperty("合同转换系数")
    private String conversionRate;

    @ApiModelProperty(value = "实际转换系数")
    @ExcelProperty("实际转换系数")
    private String actualRatio;

    @ApiModelProperty(value = "面单应收数量")
    @ExcelProperty("面单应收")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    @ExcelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    @ExcelProperty("实收数量")
    private String actualReceive;

    @ApiModelProperty(value = "合同结算单位")
    @ExcelProperty("合同结算单位")
    private String unit;

    @ApiModelProperty(value = "收料方式")
    @ExcelProperty("收料方式")
    private String receiveType;

    @ApiModelProperty(value = "偏差量")
    @ExcelProperty("偏差量")
    private BigDecimal deviationCount;

    @ApiModelProperty(value = "偏差率")
    @ExcelProperty("偏差率(%)")
    private String deviation;

    @ApiModelProperty(value = "偏差状态")
    @ExcelProperty("偏差状态")
    private String deviationStatus;

    @ApiModelProperty(value = "进场时间")
    @ExcelProperty("进场时间")
    private String enterTime;

    @ApiModelProperty(value = "出场时间")
    @ExcelProperty("出场时间")
    private String leaveTime;

}
