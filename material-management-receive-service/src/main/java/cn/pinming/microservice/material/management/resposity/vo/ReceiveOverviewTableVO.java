package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveOverviewTableVO {

    @ApiModelProperty(value = "时间范围(yyyy-/MM/dd～yyyy-/MM/dd)")
    private String time;

    @ApiModelProperty(value = "数据")
    private Map<String, List<ReceiveOverviewTable>> map;

}
