package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.dto.OperateCenterSendReceiveDTO;
import cn.pinming.microservice.material.management.resposity.dto.StandardMaterialData;
import cn.pinming.microservice.material.management.resposity.dto.StatisticsDataDTO;
import cn.pinming.microservice.material.management.resposity.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.resposity.dto.WeighInfosDTO;
import cn.pinming.microservice.material.management.resposity.dto.WeighTruckChangeDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.query.BasicQuery;
import cn.pinming.microservice.material.management.resposity.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.resposity.query.MaterialCountQuery;
import cn.pinming.microservice.material.management.resposity.query.MaterialTruckQuery;
import cn.pinming.microservice.material.management.resposity.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.resposity.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.KeyValVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialCountVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialTruckInfoVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialTruckTodayStatisticVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialTruckVO;
import cn.pinming.microservice.material.management.resposity.vo.ProjectMaterialRankVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.WeighDeviationDetailVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 收货/发货单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface MaterialSendReceiveMapper extends BaseMapper<MaterialSendReceive> {

    /**
     * 过磅情况
     *
     * @param query
     * @return
     */
    WeighInfoDTO selectWeightInfo(WeighInfoQuery query);

    /**
     * 按天显示过磅情况
     *
     * @param query
     * @return
     */
    List<WeighTruckChangeDTO> selectWeightInfoByDay(WeighInfoQuery query);

    /**
     * 过磅情况-查看更多
     *
     * @param query
     * @return
     */
    List<WeighInfosDTO> selectWeightInfos(WeighInfoQuery query);

    @InterceptorIgnore(tenantLine = "true")
    SummaryAnalysisVO querySummary(@Param("query") SummaryDeliveryQuery query);

    StatisticsDataDTO selectStatistics(@Param("query") DeviationInfoQuery query);

    IPage<WeighDeviationDetailVO> selectDeviationPage(@Param("query") DeviationInfoQuery query);

    IPage<StandardMaterialData> selectForCilent(BasicQuery query);

    @InterceptorIgnore(tenantLine = "true")
    OperateCenterSendReceiveDTO getOperateCenterOverview();

    @InterceptorIgnore(tenantLine = "true")
    Long getMobileReceiveCount();

    @InterceptorIgnore(tenantLine = "true")
    List<MaterialCountVO> getMaterialCount(@Param("query") MaterialCountQuery materialCountQuery);

    @InterceptorIgnore(tenantLine = "true")
    List<MaterialCountVO> getMoblieMaterialCount(@Param("query") MaterialCountQuery materialCountQuery);

    @InterceptorIgnore(tenantLine = "true")
    List<ProjectMaterialRankVO> getProjectMaterialRank(@Param("query") MaterialCountQuery materialCountQuery);

    @InterceptorIgnore(tenantLine = "true")
    List<ProjectMaterialRankVO> getMobileProjectMaterialRank(@Param("query") MaterialCountQuery materialCountQuery);

    IPage<MaterialTruckVO> listMaterialTruck(MaterialTruckQuery query);

    IPage<MaterialTruckInfoVO> listMaterialTruckInfo(MaterialTruckQuery query);

    MaterialTruckTodayStatisticVO getTodayTruckStatistic(@Param("companyId") Integer currentCompanyId, @Param("projectIds") List<Integer> projectIdList);

    List<String> selectReceiveIdByExtNo(@Param("projectId") Integer projectId, @Param("extNo") String extNo);

    List<KeyValVO> selectYearOverviewDiff(@Param("projectIdList") List<Integer> projectIdList);

    Integer selectCurrentMonthCount(@Param("projectIdList") List<Integer> projectIdList, @Param("isAddition") Integer isAddition, @Param("type") Integer type);
}
