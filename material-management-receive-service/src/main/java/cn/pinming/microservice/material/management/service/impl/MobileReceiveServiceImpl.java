package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.proxy.dto.FileDTO;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.common.wrapper.SupplierWrapper;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.*;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.management.resposity.dto.*;
import cn.pinming.microservice.material.management.resposity.entity.*;
import cn.pinming.microservice.material.management.resposity.form.*;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveDetailMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.*;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.purchase.management.dto.PurchaseOrderDetailSimpleDTO;
import cn.pinming.microservice.purchase.management.service.IMaterialPurchaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 移动收料表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
@Service
@Slf4j
public class MobileReceiveServiceImpl extends ServiceImpl<MobileReceiveMapper, MobileReceive> implements IMobileReceiveService {
    @Resource
    private IMobileReceiveTruckService truckService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IMobileReceiveDetailService detailService;
    @Resource
    private MobileReceiveMapper mobileReceiveMapper;
    @Resource
    private UserUtil userUtil;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private MobileReceiveDetailMapper receiveDetailMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MobileReceiveTotalServiceImpl mobileReceiveTotalService;
    @DubboReference
    private IMaterialPurchaseService materialPurchaseService;
    @DubboReference
    private IMaterialContractService materialContractService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(MobileReceiveForm form) {
        // 移动收料信息保存
        MobileReceive receive = new MobileReceive();
        String receiveId = IdUtil.simpleUUID();
        // 这里的收货项目,实际收货人来源于创建记录的人
        String receiveNo = noUtil.getMobileReceiveNo(form.getProjectId());
        BeanUtils.copyProperties(form, receive);
        receive.setReceiveId(receiveId);
        receive.setReceiveNo(receiveNo);

        if (StrUtil.isNotBlank(form.getPurchaseId()) || StrUtil.isNotBlank(form.getContractId())) {
            // 存在一条超负差
            boolean flag1 = form.getList().stream().anyMatch(e -> e.getDeviationStatus() == DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
            // 不存在超负差，有一个超正差
            boolean flag2 = !flag1 && form.getList().stream().anyMatch(e -> e.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value());
            if (flag1) {
                receive.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
            } else if (flag2) {
                receive.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
            } else {
                receive.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
            }
        }
        super.save(receive);

        // 车辆到场信息保存
        // 可能存在没报备车辆送货情况，所以不做判断
        List<String> totalList = new ArrayList<>();
        totalList.addAll(form.getGoodsPic());
        totalList.addAll(form.getSendPic());
        totalList.addAll(form.getTruckPic());
        checkActualSize(totalList);
        fileServiceProxy.confirmFiles(totalList);

        MobileReceiveTruck receiveTruck = new MobileReceiveTruck();
        BeanUtils.copyProperties(form, receiveTruck);
        receiveTruck.setReceiveId(receiveId);
        receiveTruck.setSendPic(String.join(",", form.getSendPic()));
        receiveTruck.setGoodsPic(String.join(",", form.getGoodsPic()));
        receiveTruck.setTruckPic(String.join(",", form.getTruckPic()));
        truckService.save(receiveTruck);

        // 各材料收料信息录入
        SimpleTransformDTO simpleTransformDTO = new SimpleTransformDTO();
        simpleTransformDTO.setType(form.getType());
        simpleTransformDTO.setReceiveType(form.getReceiveType());
        detailService.add(form.getList(), receiveId, simpleTransformDTO);
    }

    @Override
    public ReceiveCardDetailVO detail(String receiveId) {
        AuthUser user = userUtil.getUser();
        ReceiveCardDetailVO vo = mobileReceiveMapper.detail(receiveId);
        List<String> pointsPicList = receiveDetailMapper.getPointsPic(receiveId);
        List<String> pointsPicAll = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(vo)) {
            List<String> sendPicList = StrUtil.split(vo.getSendPic(), ",", true, true);
            List<String> goodsPicList = StrUtil.split(vo.getGoodsPic(), ",", true, true);
            List<String> truckPicList = StrUtil.split(vo.getTruckPic(), ",", true, true);
            if (CollUtil.isNotEmpty(pointsPicList)) {
                pointsPicList.forEach(e -> {
                    List<String> pointsPic = StrUtil.split(e, ",", true, true);
                    pointsPicAll.add(pointsPic.get(0));
                });
                List<FileDTO> fileList = fileServiceProxy.getFileList(String.join(",", pointsPicAll));
                if (CollUtil.isNotEmpty(fileList)) {
                    vo.setPointsPic(fileList.stream().map(FileDTO::getUrl).collect(Collectors.joining(",")));
                }
            }
            if (CollectionUtil.isNotEmpty(sendPicList)) {
                List<FileDTO> fileList = fileServiceProxy.getFileList(String.join(",", sendPicList));
                vo.setSendPic(fileList.stream().map(FileDTO::getUrl).collect(Collectors.joining(",")));
            }
            if (CollectionUtil.isNotEmpty(goodsPicList)) {
                List<FileDTO> fileList = fileServiceProxy.getFileList(String.join(",", goodsPicList));
                vo.setGoodsPic(fileList.stream().map(FileDTO::getUrl).collect(Collectors.joining(",")));
            }
            if (CollectionUtil.isNotEmpty(truckPicList)) {
                List<FileDTO> fileList = fileServiceProxy.getFileList(String.join(",", truckPicList));
                vo.setTruckPic(fileList.stream().map(FileDTO::getUrl).collect(Collectors.joining(",")));
            }

            if (vo.getSupplierId() != null) {
                supplierWrapper.wrap(vo, user.getCurrentCompanyId());
            }
            if (vo.getReceiverProject() != null) {
                ProjectDTO projectVO = projectServiceProxy.getProjectById(vo.getReceiverProject());
                if (ObjectUtil.isNotEmpty(projectVO)) {
                    vo.setReceiverProjectTitle(projectVO.getProjectTitle());
                }
            }
            if (vo.getCreateId() != null) {
                EmployeeSimpleDTO dto = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), vo.getCreateId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    vo.setCreateName(dto.getMemberName());
                }
            }

            List<ReceiveCardMaterialDetailDTO> detailDTOs = receiveDetailMapper.detail(receiveId);
            List<ReceiveCardMaterialDetailVO> vos = new ArrayList<>();
            detailDTOs.forEach(e -> {
                ReceiveCardMaterialDetailVO detailVO = new ReceiveCardMaterialDetailVO();

                BeanUtils.copyProperties(e, detailVO);
                if (StrUtil.isNotBlank(e.getPurchaseId())) {
                    PurchaseOrderDetailSimpleDTO dto = materialPurchaseService.getPurchaseOrderDetail(e.getPurchaseId(), e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(dto)) {
                        detailVO.setCount(dto.getCount());
                        detailVO.setDeviationCeiling(dto.getDeviationCeiling());
                        detailVO.setDeviationFloor(dto.getDeviationFloor());
                    }
                }
                if (StrUtil.isNotBlank(e.getContractDetailId())) {
                    SimpleContractDetailDTO dto = materialContractService.querySimpleContractDetail(e.getContractDetailId());
                    if (ObjectUtil.isNotEmpty(dto)) {
                        detailVO.setDeviationFloor(dto.getDeviationFloor());
                        detailVO.setDeviationCeiling(dto.getDeviationCeiling());
                    }
                }
                if (StrUtil.isBlank(e.getContractDetailId()) && StrUtil.isNotBlank(e.getContractId())) {
                    SimpleContractDetailDTO dto = materialContractService.querySimpleContractDetail(e.getContractId(), e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(dto)) {
                        detailVO.setDeviationFloor(dto.getDeviationFloor());
                        detailVO.setDeviationCeiling(dto.getDeviationCeiling());
                    }
                }

                detailVO.setRemark(e.getPurchaseRemark());
//              材料全称
                MaterialDto dto = materialServiceProxy.materialById(e.getMaterialId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    String str = StrUtil.format("{}/{}/{}", dto.getMaterialCategoryName(), dto.getMaterialName(), dto.getMaterialSpec());
                    detailVO.setMaterialName(str);
                    detailVO.setMaterialCategoryName(dto.getMaterialCategoryName());
                    detailVO.setMaterialType(dto.getMaterialName());
                    detailVO.setMaterialSpec(dto.getMaterialSpec());
                }

                vos.add(detailVO);

            });

            vo.setList(vos);
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fresh(MobileReceiveUpdateForm form) {
        this.lambdaUpdate()
                .eq(MobileReceive::getReceiveId,form.getReceiveId())
                .set(MobileReceive::getContractId,form.getContractId())
                .set(MobileReceive::getReceiveType,1)
                .set(MobileReceive::getSupplierId,form.getSupplierId())
                .set(MobileReceive::getIsRevise,1)
                .update();
        // 偏差状态变更
        List<MobileReceiveTotal> collect = form.getList().stream().map(e -> {
            MobileReceiveTotal total = new MobileReceiveTotal();
            total.setId(e.getTotalId());
            total.setDeviationStatus(e.getDeviationStatus());
            total.setContractDetailId(e.getContractDetailId());
            return total;
        }).collect(Collectors.toList());
        mobileReceiveTotalService.updateBatchById(collect);
    }

    private void checkActualSize(List<String> list) {
        long count = list.parallelStream().filter(StrUtil::isNotBlank).count();
        if (list.size() != count) {
            throw new BOException(BOExceptionEnum.PIC_SIZE_ERROR);
        }
    }
}
