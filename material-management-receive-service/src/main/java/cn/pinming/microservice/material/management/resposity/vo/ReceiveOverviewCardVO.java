package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description 总览-收料总览-卡片
 */
@Data
public class ReceiveOverviewCardVO {

    @ApiModelProperty(value = "材料分类ID")
    private String categoryId;

    @ApiModelProperty(value = "材料分类名称")
    private String categoryName;

    @ApiModelProperty(value = "单位-柱状图数据")
    private Map<String, List<ReceiveOverviewHistogram>> graph;

}
