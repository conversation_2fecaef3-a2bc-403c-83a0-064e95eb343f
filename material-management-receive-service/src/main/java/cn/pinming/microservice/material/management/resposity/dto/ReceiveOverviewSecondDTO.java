package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收料总览下钻dto
 *
 * <AUTHOR>
 * @since 2022/3/31 9:53
 */
@Data
public class ReceiveOverviewSecondDTO {

    @ApiModelProperty(value = "日期")
    private String time;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "累计")
    private Integer total;

    @ApiModelProperty(value = "本月新增")
    private Integer monthRise;
}
