package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 收货/发货明细一车多料
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Getter
@Setter
@TableName("d_material_data_multi")
@ApiModel(value = "MaterialDataMulti对象", description = "收货/发货明细一车多料")
public class MaterialDataMulti implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("收货单id")
    private String receiveId;

    @ApiModelProperty("发货时的收货单位，收料时的发货单位")
    private Integer supplierId;

    @ApiModelProperty("采购单ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String purchaseId;

    @ApiModelProperty("合同ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractId;

    @ApiModelProperty("合同明细ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractDetailId;

    @ApiModelProperty("材料ID")
    private Integer materialId;

    @ApiModelProperty("分类ID")
    private Integer categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("随车面单量")
    private BigDecimal weightSend;

    @ApiModelProperty("实称换算数量")
    private BigDecimal actualCount;

    @ApiModelProperty("实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty("定尺长度")
    private BigDecimal length;

    @ApiModelProperty("实点根数")
    private BigDecimal root;

    @ApiModelProperty("材料库理论值")
    private BigDecimal materialTheoreticalWeight;

    @ApiModelProperty("理论重量")
    private BigDecimal theoreticalWeight;

    @ApiModelProperty("实理偏差")
    private BigDecimal actualDeviation;

    @ApiModelProperty("实理偏差率")
    private BigDecimal actualDeviationRate;

    @ApiModelProperty("实称重量")
    private BigDecimal weightActual;

    @ApiModelProperty("换算系数")
    private BigDecimal ratio = BigDecimal.ONE;

    @ApiModelProperty("面单偏差量")
    private BigDecimal sendDeviation;

    @ApiModelProperty("面单偏差率")
    private BigDecimal sendDeviationRate;

    @ApiModelProperty("偏差状态 0 正常 1 负偏差 2 正偏差 ")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Byte deviationStatus;

    @ApiModelProperty("结算单位")
    private String weightUnit;

    @ApiModelProperty("外部系统单号")
    private String extNo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("0.未推送 1.已推送 2.推送失败")
    private Integer pushState;

    @ApiModelProperty("约定偏差阈值上限")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal deviationCeiling;

    @ApiModelProperty("约定偏差阈值下限")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal deviationFloor;

    @ApiModelProperty("品牌要求")
    private String brand;

    @ApiModelProperty("wbsId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long wbsId;

    @ApiModelProperty("选择类型 1 钢筋 2 其他非理重")
    private Integer type;

    @ApiModelProperty("计划使用部位")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String position;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;
}
