package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标题偏差总览DTO
 *
 * <AUTHOR>
 * @since 2022/4/11 17:03
 */
@Data
public class DeviationOverviewSecondDTO {


    @ApiModelProperty(value = "超负差")
    private Integer countMinusCarsNum;

    @ApiModelProperty(value = "正常")
    private Integer countNormalCarsNum;

    @ApiModelProperty(value = "超正差")
    private Integer countPositiveCarsNum;

    @ApiModelProperty(value = "时间")
    private String time;

}
