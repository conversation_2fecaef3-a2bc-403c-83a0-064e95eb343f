package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 总览预警提示
 *
 * <AUTHOR>
 * @since 2022/4/19 9:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WarningOverviewTipsVO {

    @ApiModelProperty(value = "异常数量")
    private Integer count;

    @ApiModelProperty(value = "预警类型")
    private List<Byte> warningTypeList;

    @ApiModelProperty(value = "项目ids")
    private List<Integer> projectIds;
}
