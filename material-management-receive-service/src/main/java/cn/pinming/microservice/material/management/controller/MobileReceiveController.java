package cn.pinming.microservice.material.management.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.management.dto.CompanyConfigDTO;
import cn.pinming.microservice.base.management.service.ICompanyConfService;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveForm;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveUpdateForm;
import cn.pinming.microservice.material.management.resposity.query.MobileCardQuery;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveCardVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVerifyVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveCardDetailVO;
import cn.pinming.microservice.material.management.service.IMobileReceiveDetailService;
import cn.pinming.microservice.material.management.service.IMobileReceiveService;
import cn.pinming.microservice.material.management.service.IMobileReceiveTotalService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "移动收料-controller", value = "zh")
@RestController
@RequestMapping("/api/mobile")
@Slf4j
public class MobileReceiveController {
    @Resource
    private IMobileReceiveService mobileReceiveService;
    @Resource
    private IMobileReceiveDetailService mobileReceiveDetailService;
    @Resource
    private IMobileReceiveTotalService totalService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @DubboReference
    private ICompanyConfService companyConfigService;
    @Resource
    private UserUtil userUtil;

    @ApiOperation(value = "移动收料列表")
    @PostMapping("/list")
    public ResponseEntity list(@RequestBody MobileCardQuery query) {
        List<MobileReceiveVerifyVO> list = mobileReceiveDetailService.selectPageForVerify(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "新增收料记录")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@Validated @Valid @RequestBody MobileReceiveForm form) {
        // TODO:2025/1/8 增加合同明细id 同步前端
        AuthUser user = userUtil.getUser();
        form.setCompanyId(user.getCurrentCompanyId());
        form.setProjectId(user.getCurrentProjectId());
        form.setReceiver(user.getMemberName());
        mobileReceiveService.add(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "编辑收料记录")
    @PostMapping("/update")
    public ResponseEntity<Response> update(@Validated @Valid @RequestBody MobileReceiveUpdateForm form) {
        // TODO:2025/1/8 增加合同明细id 同步前端
        mobileReceiveService.fresh(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = " 移动收料单卡片列表页", response = MobileReceiveCardVO.class)
    @PostMapping("/page")
    public ResponseEntity<Response> page(@RequestBody MobileCardQuery query) {
        AuthUser user = userUtil.getUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<MobileReceiveCardVO> page = mobileReceiveDetailService.selectPage(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "查看点验历史", response = MobileReceiveHistoryVO.class)
    @GetMapping("/history/{totalId}")
    public ResponseEntity<Response> history(@PathVariable("totalId") String totalId) {
        MobileReceiveHistoryVO vo = totalService.history(totalId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "是否需要展示理重 true 需要 false 不需要")
    @GetMapping("/isShow")
    public ResponseEntity<Response> isShow() {
        boolean flag = false;
        AuthUser user = userUtil.getUser();
        if (user.getCurrentProjectId() == null) {
            flag = false;
        }
        CompanyConfigDTO companyConfig = companyConfigService.getCompanyConfigByType(user.getCurrentCompanyId(), 3);
        if (ObjectUtil.isNotNull(companyConfig)) {
            List<String> projectIdList = StrUtil.split(companyConfig.getProjectIds(), ",");
            if (companyConfig.getIsEnable() == 2 && projectIdList.contains(String.valueOf(user.getCurrentProjectId()))) {
                flag = true;
            }
        }
        return ResponseEntity.ok(new SuccessResponse(flag));
    }

    @ApiOperation(value = "收料单详情", response = ReceiveCardDetailVO.class)
    @GetMapping("/detail/{receiveId}")
    public ResponseEntity<Response> detail(@PathVariable String receiveId) {
        ReceiveCardDetailVO vo = mobileReceiveService.detail(receiveId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }


}
