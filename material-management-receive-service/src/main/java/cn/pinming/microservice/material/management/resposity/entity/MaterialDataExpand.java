package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 收货/发货明细扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Getter
@Setter
@TableName("d_material_data_expand")
@ApiModel(value = "MaterialDataExpand对象", description = "收货/发货明细扩展表")
public class MaterialDataExpand implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("收发货明细id")
    private String dataId;

    @ApiModelProperty("卸料点")
    private String dischargePoint;
}
