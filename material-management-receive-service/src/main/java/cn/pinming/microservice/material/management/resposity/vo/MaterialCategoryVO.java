package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 2021/9/8 4:13 下午
 */
@Data
public class MaterialCategoryVO {

    @ApiModelProperty("材料分类id")
    private Integer id;

    @ApiModelProperty("材料分类-名称")
    private String name;

    /**
     * 下级节点，构建树结构时会使用
     * 如果下级节点为空，则此处数组为空
     */
    List<MaterialCategoryVO> children = new ArrayList<>(0);

}
