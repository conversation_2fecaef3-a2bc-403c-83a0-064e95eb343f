package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.material.management.resposity.entity.MaterialDataMulti;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



@Data
public class MultiDetailVO extends MaterialDataMulti {
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "规格名称")
    private String materialSpec;

    @ApiModelProperty(value = "是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    @ApiModelProperty("采购单ID")
    private String purchaseOrderId;
}
