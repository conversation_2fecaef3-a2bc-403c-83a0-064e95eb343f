package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * 处理类型枚举类
 * <AUTHOR>
 * @Date 2022/3/7 14:34
 */
public enum HandleTypeEnum {

    WARNING_HANDLE((byte) 1, "预警处理"),
    REVISE_HANDLE((byte) 2, "数据修订处理"),
    CHECK_HANDLE((byte) 3, "对账处理"),
    RECORD_PRIVILEGE((byte) 4, "扫码添加收料记录"),
    SUPPLIER_MANAGER((byte) 5, "供应商管理人"),
    SEND_PRIVILEGE((byte) 6, "扫码添加发料记录"),
    CUSTOM_MANAGE((byte) 7, "客户管理人"),
    ;

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    HandleTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }
}
