package cn.pinming.microservice.material.management.resposity.vo;
import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/7/7 16:36
 */
@Data
public class SupplierRankDeviationVO extends SimpleSupplierDTO {

    @ApiModelProperty("负差")
    private BigDecimal negativeNum;

    @ApiModelProperty("正差")
    private BigDecimal positiveNum;

    @ApiModelProperty("总量")
    private BigDecimal total;

    @ApiModelProperty("偏差状态")
    private Byte deviationStatus;
}
