package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.management.resposity.dto.HandlerDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialHandler;
import cn.pinming.microservice.material.management.resposity.form.HandlerForm;
import cn.pinming.microservice.material.management.resposity.form.MaterialHandlerForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialHandlerMapper;
import cn.pinming.microservice.material.management.resposity.vo.HandlerVO;
import cn.pinming.microservice.material.management.service.IMaterialHandlerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 处理人信息 服务实现类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 16:38:04
 */
@Service
public class MaterialHandlerServiceImpl extends ServiceImpl<MaterialHandlerMapper, MaterialHandler> implements IMaterialHandlerService {


    @Resource
    private UserUtil userUtil;

    @Resource
    private MaterialHandlerMapper materialHandlerMapper;

    @Override
    public List<HandlerVO> listHandler(Byte handleType) {
        List<HandlerVO> result;
        AuthUser currentUser = userUtil.getUser();
        Integer projectId = currentUser.getCurrentProjectId();
        List<HandlerDTO> handlerDTOList;
        if (projectId == null || projectId == 0) {
            handlerDTOList = materialHandlerMapper.selectByCompanyIdAndProjectIdIsNullAndHandleType(currentUser.getCurrentCompanyId(), handleType);
        } else {
            handlerDTOList = materialHandlerMapper.selectListByProjectIdAndHandleType(projectId, handleType);
        }
        result = handlerDTOList.stream().map(handler -> {
            HandlerVO handlerVO = new HandlerVO();
            BeanUtils.copyProperties(handler, handlerVO);
            return handlerVO;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public void saveHandler(MaterialHandlerForm materialHandlerForm) {
        Integer projectId = userUtil.getProjectId();
        if (projectId == null || projectId == 0) {
            // 保存为企业层处理人员
            saveCompanyHandler(materialHandlerForm);
            return;
        }
        // 保存为项目层处理人员
        List<HandlerForm> handlerFormList = materialHandlerForm.getHandlerFormList();

        if (CollUtil.isNotEmpty(handlerFormList)) {
            Map<String, HandlerForm> handlerFormMap = handlerFormList.stream()
                    .filter(handlerForm -> handlerForm.getHandlerId() != null)
                    .collect(Collectors.toMap(HandlerForm::getHandlerId, e -> e));
            // 该项目下指定处理类型 处理人员列表
            List<MaterialHandler> existHandlerList = this.lambdaQuery().eq(MaterialHandler::getProjectId, projectId)
                    .eq(MaterialHandler::getHandleType, materialHandlerForm.getHandleType()).list();
            Map<String, MaterialHandler> existHandlerMap = existHandlerList.stream()
                    .collect(Collectors.toMap(MaterialHandler::getHandlerId, e -> e));

            // 要删除的人员列表
            List<MaterialHandler> delHandlerList = existHandlerList.stream()
                    .filter(handler -> handlerFormMap.get(handler.getHandlerId()) == null).collect(Collectors.toList());
            // 要保存的人员列表
            List<HandlerForm> saveHandlerList = handlerFormList.stream()
                    .filter(handler -> existHandlerMap.get(handler.getHandlerId()) == null).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(delHandlerList)) {
                List<String> delIds = delHandlerList.stream().map(MaterialHandler::getId).collect(Collectors.toList());
                removeByIds(delIds);
            }

            if (CollUtil.isNotEmpty(saveHandlerList)) {
                List<MaterialHandler> saveHandlers = saveHandlerList.stream().map(handler -> {
                    MaterialHandler materialHandler = new MaterialHandler();
                    BeanUtils.copyProperties(handler, materialHandler);
                    // 保存处理人 处理类型
                    materialHandler.setHandleType(materialHandlerForm.getHandleType());
                    return materialHandler;
                }).collect(Collectors.toList());
                saveBatch(saveHandlers);
            }
        }

    }

    private void saveCompanyHandler(MaterialHandlerForm materialHandlerForm) {
        AuthUser currentUser = userUtil.getUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        // 企业层的处理人
        List<HandlerDTO> handlerDTOList = materialHandlerMapper.selectByCompanyIdAndProjectIdIsNullAndHandleType(companyId, materialHandlerForm.getHandleType());
        if (CollUtil.isNotEmpty(handlerDTOList)) {
            List<String> del = handlerDTOList.stream().map(HandlerDTO::getId).collect(Collectors.toList());
            materialHandlerMapper.deleteBatchIds(del);
        }
        // 添加的处理人
        List<HandlerForm> saveHandlerList = materialHandlerForm.getHandlerFormList();
        if (CollUtil.isNotEmpty(saveHandlerList)) {
            List<MaterialHandler> saveHandlers = saveHandlerList.stream().map(handler -> {
                MaterialHandler materialHandler = new MaterialHandler();
                BeanUtils.copyProperties(handler, materialHandler);
                // 保存处理人 处理类型
                materialHandler.setHandleType(materialHandlerForm.getHandleType());
                return materialHandler;
            }).collect(Collectors.toList());
            saveBatch(saveHandlers);
        }

    }

    @Override
    public Boolean enableHandle(String userId, Byte handleType) {
        // 管理员权限人员列表
        List<HandlerVO> adminList = listHandler((byte) 0);
        List<HandlerVO> handlerVOList = listHandler(handleType);
        handlerVOList.addAll(adminList);
        // 判断当前用户是否在处理人中
        long count = handlerVOList.stream().filter(handlerVO -> handlerVO.getHandlerId().equals(userId)).count();
        return count > 0;
    }

    @Override
    public List<HandlerVO> listProjectsHandler(Byte handleType) {
        AuthUser currentUser = userUtil.getUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        List<HandlerDTO> handlerDTOList;
        if (currentUser.getCurrentProjectId() == null || currentUser.getCurrentProjectId() == 0) {
            handlerDTOList = materialHandlerMapper.selectListByCompanyIdAndHandleType(currentCompanyId, handleType);
        } else {
            handlerDTOList = materialHandlerMapper.selectListByProjectIdAndHandleType(currentUser.getCurrentProjectId(), handleType);
        }
        return handlerDTOList.stream().map(handler -> {
            HandlerVO handlerVO = new HandlerVO();
            BeanUtils.copyProperties(handler, handlerVO);
            return handlerVO;
        }).collect(Collectors.toList());
    }

}
