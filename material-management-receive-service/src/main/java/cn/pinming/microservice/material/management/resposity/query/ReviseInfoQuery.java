
package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/9 10:28
 */
@Data
public class ReviseInfoQuery extends BaseQuery {

    @ApiModelProperty(value = "物资明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    /**
     * 修订类型 1/null 地磅数据修订 2 移动数据修订
     */
    private Byte type;
}
