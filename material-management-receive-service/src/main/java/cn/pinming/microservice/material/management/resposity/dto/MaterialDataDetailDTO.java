package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地磅收货明细
 *
 * <AUTHOR>
 */
@Data
public class MaterialDataDetailDTO {

    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货项目id")
    private Integer receiverProject;

    @ApiModelProperty(value = "收料人")
    private String receiver;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "采购单id")
    private String orderId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "供应商id")
    private String supplierName;

    @ApiModelProperty(value = "车牌")
    private String truckNo;

    @ApiModelProperty(value = "进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "进场图片")
    private String enterPic;

    @ApiModelProperty(value = "出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "出场图片")
    private String leavePic;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduction;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "重量单位 ")
    private String unit;

    @ApiModelProperty(value = "车辆过磅记录编号")
    private String no;

    @ApiModelProperty(value = "1 临时收料，2 报备收料，3 无归属收料 0 无效收料")
    private Byte receiveMode;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "下单人")
    private String createId;

    @ApiModelProperty(value = "1 有效，2 无效")
    private Byte materialValidity;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "是否为自研终端数据 1 否 2 是")
    private Byte isDevice;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;

    @ApiModelProperty(value = "是否使用采购单")
    private String IsUsed;

    @ApiModelProperty("领用单位")
    private String receiveUnit;

    private Long wbsId;

    @ApiModelProperty(value = "是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;
}
