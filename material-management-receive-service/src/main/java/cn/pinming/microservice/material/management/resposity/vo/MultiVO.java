package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceiveMulti;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MultiVO extends MaterialSendReceiveMulti {
    @ApiModelProperty(value = "进场图片")
    private List<String> enterPics;

    @ApiModelProperty(value = "出场图片")
    private List<String> leavePics;

    @ApiModelProperty(value = "单据照片")
    private List<String> documentPics;

    @ApiModelProperty(value = "收料人")
    private String receiver;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "品种名称")
    private String categoryName;

    @ApiModelProperty(value = "收发料明细列表")
    private List<MultiDetailVO> list;
}
