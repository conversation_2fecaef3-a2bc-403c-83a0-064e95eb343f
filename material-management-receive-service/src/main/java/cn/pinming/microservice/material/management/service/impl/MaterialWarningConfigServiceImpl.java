package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarningConfig;
import cn.pinming.microservice.material.management.resposity.form.WarningTypeConfigForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialWarningConfigMapper;
import cn.pinming.microservice.material.management.resposity.vo.WarningTypeVO;
import cn.pinming.microservice.material.management.service.IMaterialWarningConfigService;
import cn.pinming.microservice.material.management.service.IMaterialWarningService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警统计配置 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-08-24 10:27:10
 */
@Service
public class MaterialWarningConfigServiceImpl extends ServiceImpl<MaterialWarningConfigMapper, MaterialWarningConfig> implements IMaterialWarningConfigService {

    @Resource
    private UserUtil userUtil;
    @Resource
    private MaterialWarningConfigMapper warningConfigMapper;
    @Resource
    private IMaterialWarningService warningService;

    @Override
    public List<String> listWarningConfig() {
        AuthUser currentUser = userUtil.getUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        MaterialWarningConfig warningTypeConfig = warningConfigMapper.getWarningTypeConfig(companyId, projectId);
        return getFinalConfigType(warningTypeConfig);
    }

    @Override
    public void saveWarningTypeConfig(WarningTypeConfigForm warningTypeConfigForm) {
        String warningType = warningTypeConfigForm.getWarningType();
        AuthUser currentUser = userUtil.getUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        MaterialWarningConfig warningTypeConfig = warningConfigMapper.getWarningTypeConfig(companyId, projectId);
        if (warningTypeConfig != null) {
            warningTypeConfig.setWarningType(warningType);
            updateById(warningTypeConfig);
        } else {
            warningTypeConfig = new MaterialWarningConfig();
            warningTypeConfig.setWarningType(warningType);
            save(warningTypeConfig);
        }
    }

    private List<String> getFinalConfigType(MaterialWarningConfig warningTypeConfig) {
        List<WarningTypeVO> warningTypeVOList = warningService.listWarningType();
        List<String> warningTypeList = warningTypeVOList.stream().map(WarningTypeVO::getWarningType).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        if (warningTypeConfig == null || StrUtil.isEmpty(warningTypeConfig.getWarningType())) {
            return warningTypeList;
        }
        Set<String> configTypeSet = Arrays.stream(warningTypeConfig.getWarningType().split(StrUtil.COMMA)).collect(Collectors.toSet());
        return warningTypeList.stream().filter(type -> !configTypeSet.contains(type)).collect(Collectors.toList());
    }

}
