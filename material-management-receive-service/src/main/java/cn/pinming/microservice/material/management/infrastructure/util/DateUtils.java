package cn.pinming.microservice.material.management.infrastructure.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/24
 * @description
 */
public class DateUtils {

    private static String YYYY_MM_DD = "yyyy-MM-dd";
    public static DateTimeFormatter DD_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD);
    private static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static DateTimeFormatter SS_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    private static Map<Pattern, DateTimeFormatter> patternTimeFormatMap = Maps.newHashMap();
    private static List<Pattern> patternList = Lists.newArrayList();
    private final static Pattern PATTERN_ONE = Pattern.compile("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");
    private final static DateTimeFormatter FORMAT_ONE = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static Pattern PATTERN_TWO = Pattern.compile("\\d{4}\\d{2}\\d{2} \\d{2}:\\d{2}:\\d{2}");
    private final static DateTimeFormatter FORMAT_TWO = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");

    static {
        patternTimeFormatMap.put(PATTERN_ONE, FORMAT_ONE);
        patternTimeFormatMap.put(PATTERN_TWO, FORMAT_TWO);
        patternList.add(PATTERN_ONE);
        patternList.add(PATTERN_TWO);
    }

    public static String yesterdayStr() {
        String yesterday = LocalDateTime.now().minusDays(1).format(DD_DATE_TIME_FORMATTER);
        return yesterday;
    }

    public static String todayStr() {
        String today = LocalDateTime.now().format(DD_DATE_TIME_FORMATTER);
        return today;
    }

    public static String tomorrowStr() {
        String tomorrow = LocalDateTime.now().plusDays(1).format(DD_DATE_TIME_FORMATTER);
        return tomorrow;
    }

    public static LocalDate parseLocalDate(String time) {
        return LocalDate.parse(time, DD_DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parseLocalDateTime(String time) {
        return LocalDateTime.parse(time, SS_DATE_TIME_FORMATTER);
    }

    public static String parseLocalDate(LocalDate time) {
        return time.format(DD_DATE_TIME_FORMATTER);
    }

    public static String parseLocalDateTime(LocalDateTime time, DateTimeFormatter formatter) {
        return time.format(formatter);
    }

    public static List<String> formatTimerShaft(String start, String end) {
        List<String> shafts = Lists.newArrayList();
        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
            return shafts;
        }

        shafts.add(start);
        LocalDate startTime = parseLocalDate(start);
        LocalDate endTime = parseLocalDate(end);

        long days = endTime.toEpochDay() - startTime.toEpochDay();
        for (int i = 1; i <= days; i++) {
            shafts.add(parseLocalDate(startTime.plusDays(i)));
        }
        return shafts;
    }

    @NotNull
    public static List<String> buildDateRange(LocalDate beginTime, LocalDate endTime, String pattern) {
        List<String> res = Lists.newArrayList();
        if (beginTime == null || endTime == null || StringUtils.isBlank(pattern) || beginTime.isAfter(endTime)) {
            return res;
        }
        Date begin = Date.from(beginTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<DateTime> dateTimes = cn.hutool.core.date.DateUtil.rangeToList(begin, end, DateField.DAY_OF_YEAR);
        res = dateTimes.stream().map(dateTime -> cn.hutool.core.date.DateUtil.format(dateTime, pattern)).collect(Collectors.toList());
        return res;
    }

    public static LocalDateTime parseDateStr(String dateStr) {
        for (Pattern pattern : patternList) {
            Matcher matcher = pattern.matcher(dateStr);
            if (matcher.find()) {
                DateTimeFormatter dateTimeFormatter = patternTimeFormatMap.get(pattern);
                try {
                    return LocalDateTime.parse(dateStr, dateTimeFormatter);
                } catch (DateTimeParseException dateTimeParseException) {
                    throw new BOException(BOExceptionEnum.IMPORT_EXIT_TIME_ILLEGAL);
                }
            }
        }
        return null;
    }

}
