package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class MobileWeighInfoQuery{
    @ApiModelProperty("组织顶点")
    @NotNull(message = "组织顶点不能为空")
    private Integer point;

    @ApiModelProperty("项目范围")
    private List<Integer> projectIdList;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    private Integer companyId;

    private Integer projectId;
}
