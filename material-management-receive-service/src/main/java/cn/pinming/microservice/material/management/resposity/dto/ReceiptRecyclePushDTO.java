package cn.pinming.microservice.material.management.resposity.dto;

import cn.pinming.microservice.material.management.infrastructure.config.LocalDateTimeDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptRecyclePushDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户id")
    private String uid;

    @ApiModelProperty("主code")
    private String primaryCode;

    @ApiModelProperty("合同id")
    private String contractId;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("单据识别类型(1-自助称重类单据、2-非称重类单据)")
    private Integer ocrType;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("回收时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime recycleTime;

    @ApiModelProperty("创建时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty("回收图片地址列表")
    private List<PicUrlDTO> recyclePicUrls;

    @ApiModelProperty("称重数据")
    private List<ReceiptRecycleWeighDTO> weighList;

    @ApiModelProperty("模板数据组")
    private List<ReceiptRecycleModuleDTO> moduleList;



    @Data
    public static class ReceiptRecycleWeighDTO{

        @ApiModelProperty("终端记录id")
        private String recordId;

        @ApiModelProperty("设备机器码")
        private String deviceSn;

        @ApiModelProperty("重量")
        private BigDecimal weight;

        @ApiModelProperty("称重单位")
        private String unit;

        @ApiModelProperty("称重时间")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime weighTime;

        @ApiModelProperty("风险等级（低：LOW，中：MIDDLE，高：HIGH）")
        private String riskGrade;

        @ApiModelProperty("图片地址列表")
        private List<PicUrlDTO> fileIdUrls;
    }


    @Data
    public static class ReceiptRecycleModuleDTO {

        @ApiModelProperty("数据组名称")
        private String groupName;

        @ApiModelProperty("模板键值")
        private String keyName;

        @ApiModelProperty("模板键值")
        private String keyValue;
    }
}
