package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/18 10:09
 */
@Data
public class HandleAdviceForm {

    @ApiModelProperty(value = "预警来源id")
    private List<String> warningSourceId;

    @ApiModelProperty(value = "处理建议")
    @NotBlank(message = "处理建议为空")
    private String handlerAdvice;

    @ApiModelProperty(value = "预警状态:1 未处理 2 已处理")
    @NotNull(message = "预警状态为空")
    private Byte warningStatus;
}