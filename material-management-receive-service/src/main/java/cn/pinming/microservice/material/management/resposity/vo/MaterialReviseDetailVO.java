package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 修正数据详情
 *
 * <AUTHOR>
 * @Date 2022/3/9 10:34
 */
@Data
public class MaterialReviseDetailVO {
    @ApiModelProperty(value = "tUuid")
    private String tUuid;

    @ApiModelProperty(value = "收货单明细ID")
    private String id;

    @ApiModelProperty(value = "收货单ID")
    private String receiveId;

    @ApiModelProperty(value = "收料单号")
    private String receiveNo;

    @ApiModelProperty(value = "1 临时收料，2 报备收料，3 无归属收料")
    private Byte receiveMode;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "采购单号")
    private String orderNo;

    @ApiModelProperty(value = "原始单据")
    private List<String> documentPics;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "材料id")
    private String materialId;

    @ApiModelProperty(value = "二级材料id")
    private Integer categoryId;

    @ApiModelProperty(value = "材料分类名称")
    private String categoryName;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String materialSpec;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal nWeight;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "合同的结算单位")
    private String unit;

    @ApiModelProperty(value = "合同约定系数")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "面单应收量：发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    @ApiModelProperty(value = "偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  "  )
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;

    private Long wbsId;

    @ApiModelProperty(value = "领用单位")
    private String receiveUnit;

    @ApiModelProperty("是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;
}

