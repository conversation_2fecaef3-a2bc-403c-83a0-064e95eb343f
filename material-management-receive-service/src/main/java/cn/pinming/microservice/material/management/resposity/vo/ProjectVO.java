package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:55 下午
 */
@Data
public class ProjectVO {

    @ApiModelProperty("自维护项目关联ID")
    private String relId;

    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty("项目名称")
    private String projectTitle;

    @ApiModelProperty("所属部门ID")
    private Integer departmentId;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("是否选中")
    private Boolean checked = false;

    @ApiModelProperty("项目类型：1：自维护项目 2：桩桩平台项目")
    private Integer type;
}
