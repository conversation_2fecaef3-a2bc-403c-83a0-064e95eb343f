package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.entity.MobileReceive;
import cn.pinming.microservice.material.management.resposity.query.MobileCardQuery;
import cn.pinming.microservice.material.management.resposity.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveCardVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVerifyVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveCardDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
public interface MobileReceiveMapper extends BaseMapper<MobileReceive> {
    @InterceptorIgnore(tenantLine = "true")
    IPage<MobileReceiveCardVO> selectReceivePage(MobileCardQuery query);

    ReceiveCardDetailVO detail(@Param("receiveId") String receiveId);

    List<MobileReceiveVerifyVO> selectPageForVerify(@Param("query") MobileCardQuery query);

    @InterceptorIgnore(tenantLine = "true")
    SummaryAnalysisVO countSummary(@Param("query") SummaryDeliveryQuery query);

    SupplierAnalysisVO selectSupplierAnalysisByMobileQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierMobileAnalysisPageVO(SupplierAnalysisQuery query);

    Integer selectCurrentMonthCount(@Param("projectIdList") List<Integer> projectIdList, @Param("type") Integer type);
}
