package cn.pinming.microservice.material.management.resposity.form;

import cn.pinming.microservice.material.management.resposity.dto.SingleOcrMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 称重磅单批量上传form
 * <AUTHOR>
 */
@Data
public class MobileMaterialBatchForm extends StandardMaterialBaseForm{
    @ApiModelProperty("收发料类型 1 收料 2 发料")
    private Byte kind;

    @ApiModelProperty("收发料类型 1 收料 2 发料")
    private Byte weighType;

    @ApiModelProperty("修订提示: true 存在 false 不存在")
    private Boolean exist;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供         发料:4 发料  5 废旧物料  6 调出  7 售出")
    private Byte typeDetail;

    @ApiModelProperty("合同明细id")
    private String contractDetailId;

    @ApiModelProperty("采购单id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "二级分类id")
    @NotNull(message = "二级分类id不能为空")
    private Integer categoryId;

    @ApiModelProperty("收料单据照片")
    private List<String> documentPics;

    @ApiModelProperty("收料进场照片")
    private List<String> enterPics;

    @ApiModelProperty("收料出场照片")
    private List<String> leavePics;

    @ApiModelProperty("随车面单量")
    private BigDecimal weightSend;

    @ApiModelProperty("转换系数")
    private BigDecimal ratio;

    @ApiModelProperty("结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "材料规格")
    private String materialSpec;

    @ApiModelProperty(value = "采购单no")
    private String orderNo;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同约定转换系数")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "基石数据id1")
    private String recordId1;

    @ApiModelProperty(value = "基石数据id2")
    private String recordId2;

    private Long wbsId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;

    @ApiModelProperty(value = "领用单位")
    private String receiveUnit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    @ApiModelProperty(value = "收货单位")
    private String receiveProject;

    @ApiModelProperty(value = "材料选择列表")
    private List<SingleOcrMaterialDTO> materialChooseList;
}
