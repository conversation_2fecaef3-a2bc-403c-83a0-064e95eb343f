package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MaterialWeighReceiveExportVO {
    @ApiModelProperty(value = "收料单号")
    @ExcelProperty("收料单号")
    private String receiveNo;

    @ApiModelProperty(value = "收料项目")
    @ExcelProperty("收料项目")
    private String projectTitle;

    @ApiModelProperty(value = "品种及规格")
    @ExcelProperty("品种及规格")
    private String materialName;

    @ApiModelProperty(value = "物资名称")
    @ExcelProperty("物资名称")
    private String categoryName;

    @ApiModelProperty(value = "面单应收数量")
    @ExcelProperty("面单应收数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    @ExcelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "结算单位")
    @ExcelProperty("结算单位")
    private String unit;

    @ApiModelProperty(value = "偏差率")
    @ExcelProperty("偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态")
    @ExcelProperty("偏差状态")
    private String deviationStatusStr;

    @ApiModelProperty(value = "供应商")
    @ExcelProperty("供应商")
    private String supplierName;

    @ApiModelProperty(value = "称重确认人")
    @ExcelProperty("称重确认人")
    private String receiver;

    @ApiModelProperty(value = "收料时间")
    @ExcelProperty("收料时间")
    @ColumnWidth(25)
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "收料方式")
    @ExcelProperty("收料方式")
    private String receiveType;

    @ApiModelProperty(value = "是否已推仓库 0,否 1,是")
    @ExcelProperty("是否已推仓库")
    private String isPushedStr;

    @ApiModelProperty(value = "是否扣量")
    @ExcelProperty("是否扣量")
    private String isDeductStr;

    @ApiModelProperty(value = "是否有完整性预警")
    @ExcelProperty("收料数据是否完整")
    private String integrityStr;
}
