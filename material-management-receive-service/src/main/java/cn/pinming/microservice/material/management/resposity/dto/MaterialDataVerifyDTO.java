package cn.pinming.microservice.material.management.resposity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 修订列表DTO
 * <AUTHOR>
 * @Date 2022/3/18 13:26
 */
@Data
public class MaterialDataVerifyDTO {

    @ApiModelProperty(value = "对账Id")
    private String id;

    @ApiModelProperty(value = "对账编号")
    private String verifyNo;

    @ApiModelProperty(value = "报账项目id")
    private Integer verifyProjectId;

    @ApiModelProperty(value = "对账供应商")
    private Integer verifySupplierId;

    @ApiModelProperty(value = "收料方式 1 地磅收料 2 移动收料")
    private Byte verifyType;

    @ApiModelProperty(value = "对账人id")
    private String verifyPersonId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "对账状态 0 已归档 1 对账中")
    private Byte status;

    @ApiModelProperty(value = "归档时间")
    private LocalDateTime fileTime;
}
