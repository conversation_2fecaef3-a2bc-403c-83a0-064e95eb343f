package cn.pinming.microservice.material.management.resposity.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 地磅收料 form.
 *
 * <AUTHOR>
 * @version 2022/4/15 16:02
 */
@Data
public class WeighReceiveCommitForm {

    @ApiModelProperty(value = "收料方式类型（1-采购单收料，2-合同收料，3-无合同收料），前端传入类型")
    private Integer reviceType;

    @ApiModelProperty(value = "材料id")
    @NotNull(message = "材料id为空")
    private Integer materialId;

    @ApiModelProperty(value = "结算单位")
    @NotBlank(message = "结算单位为空")
    private String weightUnit;

    @ApiModelProperty(value = "设置称重转换系数")
    @NotNull(message = "设置称重转换系数为空")
    @Digits(fraction = 4,message = "换算系数小数点上限为4位", integer = 999)
    @DecimalMin(value = "0",message = "换算系数必须为正数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "面单应收量/发货数量")
    @DecimalMin(value = "0",message = "面单应收量/发货数量必须为正数")
    private BigDecimal weightSend;

    @ApiModelProperty("毛重")
    @NotNull(message = "毛重为空")
    @JsonProperty(value = "gWeight")
    private BigDecimal gWeight;

    @ApiModelProperty("皮重")
    @NotNull(message = "皮重为空")
    @JsonProperty(value = "tWeight")
    private BigDecimal tWeight;

    @ApiModelProperty("扣重")
    @NotNull(message = "扣重为空")
    @JsonProperty(value = "bWeight")
    private BigDecimal bWeight;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty("单据流水号")
    @NotBlank(message = "单据流水号为空")
    @JsonProperty(value = "sNo")
    private String sNo;

    @ApiModelProperty("车牌号")
    @NotBlank(message = "车牌号为空")
    @JsonProperty(value = "tNo")
    private String tNo;

    @ApiModelProperty("称重记录UUID")
    @NotBlank(message = "称重记录UUID为空")
    @JsonProperty(value = "tUuid")
    private String tUuid;

    @ApiModelProperty("企业id")
    @NotNull(message = "企业id为空")
    private Integer coId;

    @ApiModelProperty("项目id")
    @NotNull(message = "项目id为空")
    private Integer pjId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "实收数量")
//    @NotNull(message = "实收数量为空",groups = {Receive.class})
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "单据照片")
    private List<String> documentPics;

    @ApiModelProperty(value = "过磅类型 1 收料 2 发料 ")
    private Byte weighType;

    @ApiModelProperty(value = "出场时间（皮重时间）")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "进场时间（毛重时间）")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "发料-接收方名称")
    private String receiverName;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "含水率")
    @Digits(fraction = 2,message = "含水率小数点上限为2位", integer = 999)
    @DecimalMin(value = "0",message = "含水率必须为正数")
    @DecimalMax(value = "100",message = "含水率不能大于100")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "收料:是否立即完成对账 1 是 2 否;  发料:是否需要立即推送 1 是 2 否")
    private Byte isAutoVerify;

    @ApiModelProperty(value = "传1 标示为非终端传输")
    private Byte isWebPushed;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供         发料:4 发料  5 废旧物料  6 调出  7 售出")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "实际数量：实重 * 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty("收料进场照片")
    private List<String> enterPics;

    @ApiModelProperty("收料出场照片")
    private List<String> leavePics;

    @ApiModelProperty(value = "基石数据id1")
    private String recordId1;

    @ApiModelProperty(value = "基石数据id2")
    private String recordId2;
}
