package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标题预警总览下钻DTO
 *
 * <AUTHOR>
 * @since 2022/4/11 13:16
 */
@Data
public class WarningOverviewSecondDTO {

    @ApiModelProperty(value = "日期")
    private String time;

    @ApiModelProperty(value = "总计")
    private Integer total;

    @ApiModelProperty(value = "未处理数量")
    private Integer unHandle;
}
