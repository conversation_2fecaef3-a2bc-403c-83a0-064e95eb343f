package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商排行VO.
 *
 * <AUTHOR>
 * @version 2022/4/11 11:10
 */
@Data
public class SupplierRankDiffVO extends SimpleSupplierDTO {

    @ApiModelProperty("车次")
    private Integer num;

    @ApiModelProperty("超负差数")
    private Integer diff;

    @ApiModelProperty("超负率")
    private BigDecimal rate;

}
