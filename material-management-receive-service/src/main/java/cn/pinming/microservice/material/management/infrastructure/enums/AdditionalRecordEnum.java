package cn.pinming.microservice.material.management.infrastructure.enums;

public enum AdditionalRecordEnum {
    NO((byte)1, "否"),
    YES((byte)2, "是");

    /**
     * 状态值
     */
    private  byte value;
    /**
     * 状态的描述
     */
    private  String description;

    AdditionalRecordEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (AdditionalRecordEnum statusEnum : AdditionalRecordEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
