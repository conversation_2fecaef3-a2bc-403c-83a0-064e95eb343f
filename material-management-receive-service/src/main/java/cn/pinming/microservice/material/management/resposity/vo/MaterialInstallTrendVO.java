package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/1/3 10:10
 */
@Data
public class MaterialInstallTrendVO {

    @ApiModelProperty("累计安装数")
    private Long installTotal;

    @ApiModelProperty("本月新装数")
    private Long monthCount;

    @ApiModelProperty("月同比")
    private BigDecimal MonthlyYearOnYear;

    @ApiModelProperty("月环比")
    private BigDecimal monthOnMonth;

    @ApiModelProperty("月份数据")
    List<MaterialMonthInstallTrendVO> monthInstallTrendVOList;
}
