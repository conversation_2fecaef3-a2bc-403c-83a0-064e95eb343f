package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 重量基础VO类
 */
@Data
public class WeightBaseVO implements Serializable {
    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "面单应收量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差量")
    private BigDecimal deviation;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "结算单位")
    private String unit;
}
