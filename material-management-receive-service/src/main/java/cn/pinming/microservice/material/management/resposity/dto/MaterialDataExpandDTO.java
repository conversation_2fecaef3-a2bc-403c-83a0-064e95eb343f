package cn.pinming.microservice.material.management.resposity.dto;

import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialDataExpandDTO extends MaterialData {
    @ApiModelProperty("收发货明细id")
    private String dataId;

    @ApiModelProperty("收发货单号")
    private String receiveNo;

    @ApiModelProperty("收发货类型")
    private Byte receiveType;

    @ApiModelProperty("卸料点")
    private String dischargePoint;
}
