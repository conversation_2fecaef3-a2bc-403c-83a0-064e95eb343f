package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商排行查询条件.
 *
 * <AUTHOR>
 * @version 2022/4/11 11:16
 */
@Data
public class SupplierRankQuery {

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "组织id")
    private Integer departmentId;

    @ApiModelProperty(value = "项目id列表")
    private List<Integer> projectId;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "收料方式 1全部收料 2地磅收料 3移动收料(有合同) ")
    private Byte type;

    @ApiModelProperty(value = "返回条数")
    private Integer limit;

}
