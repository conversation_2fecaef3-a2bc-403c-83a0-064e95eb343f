package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 预警下钻vo
 *
 * <AUTHOR>
 * @since 2022/4/6 14:06
 */
@Data
public class WarningSecondVO {

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "下级单位")
    private String projectName;

    @ApiModelProperty(value = "各单位详情")
    private List<WarningSecondDetailVO> warningSecondDetailVOList;
}
