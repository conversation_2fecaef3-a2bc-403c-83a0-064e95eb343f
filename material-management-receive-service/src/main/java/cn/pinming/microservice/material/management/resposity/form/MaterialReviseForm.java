package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class MaterialReviseForm extends BaseForm{
    @ApiModelProperty(value = "收货单明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    @NotBlank(message = "收货单id不能为空")
    private String receiveId;

    @ApiModelProperty(value = "采购单id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "采购单no")
    private String orderNo;

    @ApiModelProperty(value = "合同明细id")
//    @NotBlank(message = "请重新选择物料")
    private String contractDetailId;

    @ApiModelProperty(value = "收料方式类型（1-采购单收料，2-合同收料，3-无合同收料），前端传入类型")
    private Integer reviceType;

    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "请重新选择供应商")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;

    @ApiModelProperty(value = "材料id")
    @NotNull(message = "请重新选择材料")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    @NotBlank(message = "材料名称不能为空")
    private String materialName;

    @ApiModelProperty(value = "结算单位")
    @NotBlank(message = "结算单位不能为空")
    private String weightUnit;

    @ApiModelProperty(value = "面单应收量：发货数量")
    @DecimalMin(value = "0",message = "面单应收量必须为正数")
    @Digits(fraction = 3,message = "面单应收量小数点上限为3位", integer = 999)
    private BigDecimal weightSend;

    @ApiModelProperty(value = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    @Length(max = 10, message = "车牌号长度过长")
    private String truckNo;

    @ApiModelProperty(value = "毛重")
    @DecimalMin(value = "0",message = "毛重必须为正数")
    @Digits(fraction = 3,message = "毛重小数点上限为3位", integer = 999)
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    @DecimalMin(value = "0",message = "皮重必须为正数")
    @Digits(fraction = 3,message = "皮重小数点上限为3位", integer = 999)
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    @DecimalMin(value = "0",message = "扣重必须为正数")
    @Digits(fraction = 3,message = "扣重小数点上限为3位", integer = 999)
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "换算系数")
    @DecimalMin(value = "0",message = "换算系数必须为正数")
    @NotNull(message = "换算系数不能为空")
    @Digits(fraction = 4,message = "换算系数小数点上限为4位", integer = 999)
    private BigDecimal ratio;

    @ApiModelProperty(value = "实际数量：实重 * 换算系数")
    @NotNull(message = "实际数量不能为空")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
//    @NotNull(message = "实收数量不能为空")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "单价")
//    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额")
//    @NotNull(message = "金额不能为空")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "偏差率")
    @NotNull(message = "偏差率不能为空")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    @NotNull(message = "偏差状态不能为空")
    private Byte deviationStatus;

    @ApiModelProperty(value = "是否使用合同转换系数 1，是 2，否")
    @NotNull(message = "请选择是否使用合同转换系数")
    private Byte isContractRateUsed;

    @ApiModelProperty(value = "修订说明")
    @Length(max = 500, message = "修订说明过长")
    private String reviseRemark;

    @ApiModelProperty(value = "单据照片")
    private List<String> documentPics;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "含水率")
    @Digits(fraction = 2,message = "含水率小数点上限为2位", integer = 999)
    @DecimalMin(value = "0",message = "含水率必须为正数")
    @DecimalMax(value = "100",message = "含水率不能大于100")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "收料:是否立即完成对账 1 是 2 否;  发料:是否需要立即推送 1 是 2 否")
    private Byte isAutoVerify;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供         发料:4 发料  5 废旧物料  6 调出  7 售出")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty("领用单位")
    private String receiveUnit;

    // 其他
    private Long wbsId;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;
}
