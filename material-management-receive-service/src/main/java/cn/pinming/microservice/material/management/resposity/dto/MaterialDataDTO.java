package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MaterialDataDTO implements Serializable {
    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String material;

    @ApiModelProperty(value = "材料单位")
    private String weightUnit;

    @ApiModelProperty(value = "发货重量 面单量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量：土方数量，按体积计量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;
}