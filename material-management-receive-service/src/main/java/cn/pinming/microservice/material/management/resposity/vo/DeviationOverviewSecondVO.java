package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 标题偏差总览下钻页面
 *
 * <AUTHOR>
 * @since 2022/4/11 16:57
 */
@Data
public class DeviationOverviewSecondVO {

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "累计")
    private Integer total;

    @ApiModelProperty(value = "本月新增")
    private Integer monthRise;

    @ApiModelProperty(value = "各单位每月偏差次数")
    private List<DeviationOverviewSecondDetailVO> deviationOverviewSecondDetailVOS;
}
