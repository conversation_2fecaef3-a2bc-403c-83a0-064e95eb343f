package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警处理人信息
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_material_revise")
@ApiModel(value = "MaterialRevise对象", description = "数据修订对象")
public class MaterialRevise implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收发货单id")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "收发货单明细id")
    @TableField("material_data_id")
    private String materialDataId;

    @ApiModelProperty(value = "原始终端称重数据")
    @TableField("original_data")
    private String originalData;

    @ApiModelProperty(value = "修订项")
    @TableField("revise_detail")
    private String reviseDetail;

    @ApiModelProperty(value = "修订说明")
    @TableField("revise_remark")
    private String reviseRemark;

    @ApiModelProperty(value = "修订类型 1 地磅收料修订  2 移动收料修订")
    @TableField("type")
    private Byte type;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
