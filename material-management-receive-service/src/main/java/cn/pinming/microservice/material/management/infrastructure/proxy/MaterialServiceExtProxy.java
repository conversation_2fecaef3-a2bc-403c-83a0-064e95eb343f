package cn.pinming.microservice.material.management.infrastructure.proxy;

import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/3 2:15 下午
 */
public interface MaterialServiceExtProxy {

    List<MaterialDto> listMaterialByIds(Collection<Integer> materialIds);

    List<MaterialCategoryDto> listCategoryByIds(Integer companyId, Collection<Integer> categoryIds);

    Map<Integer, String> materialSpec(Set<Integer> materialIds);

    List<Integer> getMaterialIdsByCategoryId(Integer companyId, Integer categoryId);

    List<Integer> getMaterialIdsByCategoryIds(Integer companyId, List<Integer> categoryIds);

}
