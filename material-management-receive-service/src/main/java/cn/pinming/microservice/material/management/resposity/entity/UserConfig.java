package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户配置表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2023-04-17 15:41:33
 */
@Getter
@Setter
@TableName("s_user_config")
@ApiModel(value = "UserConfig对象", description = "用户配置表")
public class UserConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("配置类型 1,导出字段配置-地磅收料")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("配置项")
    @TableField("collection")
    private String collection;

    @ApiModelProperty("公司id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty("项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty("组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty("创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty("修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
