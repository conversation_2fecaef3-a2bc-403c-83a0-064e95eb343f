package cn.pinming.microservice.material.management.controller;

import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.infrastructure.listener.WeighDataListener;
import cn.pinming.microservice.material.management.resposity.entity.MaterialDataMulti;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceiveMulti;
import cn.pinming.microservice.material.management.resposity.form.SendReceiveMultiForm;
import cn.pinming.microservice.material.management.resposity.query.MultiQuery;
import cn.pinming.microservice.material.management.resposity.vo.MultiVO;
import cn.pinming.microservice.material.management.service.IMaterialDataMultiService;
import cn.pinming.microservice.material.management.service.save.IMaterialSendReceiveMultiService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收货/发货单一车多料 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Api(tags = "一车多料", value = "zh")
@RestController
@RequestMapping("api/sendReceive/multi")
public class MaterialSendReceiveMultiController {
    @Resource
    private IMaterialSendReceiveMultiService materialSendReceiveMultiService;
    @Resource
    private IMaterialDataMultiService materialDataMultiService;
    @Resource
    private WeighDataListener weighDataListener;

    @ApiOperation(value = "新增收料")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@RequestBody SendReceiveMultiForm form) {
        String id = materialSendReceiveMultiService.add(form);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/page")
    public ResponseEntity<Response> pageByQuery(@RequestBody MultiQuery query) {
        Page<MultiVO> page = materialSendReceiveMultiService.pageByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseEntity<Response> detail(@PathVariable("id") String sendReceiveId) {
        MultiVO vo = materialSendReceiveMultiService.detail(sendReceiveId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable("id") String sendReceiveId) {
        materialSendReceiveMultiService.lambdaUpdate()
                .eq(MaterialSendReceiveMulti::getId, sendReceiveId)
                .set(MaterialSendReceiveMulti::getIsDeleted, 1)
                .update();
        materialDataMultiService.lambdaUpdate()
                .eq(MaterialDataMulti::getReceiveId, sendReceiveId)
                .set(MaterialDataMulti::getIsDeleted, 1)
                .update();
        // 内部推送
        weighDataListener.AfterCompletion(null, sendReceiveId, 2);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "批量修改外部系统单号")
    @PostMapping("/batchUpdate")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Response> batchUpdate(@RequestBody Map<String, String> map) {
        List<MaterialSendReceiveMulti> list = new ArrayList<>();
        List<String> receiveIdList = new ArrayList<>();
        map.forEach((k, v) -> {
            MaterialSendReceiveMulti materialSendReceiveMulti = new MaterialSendReceiveMulti();
            materialSendReceiveMulti.setId(k);
            materialSendReceiveMulti.setExtNo(v);
            materialSendReceiveMulti.setPushState(0);
            list.add(materialSendReceiveMulti);
            receiveIdList.add(k);
        });
        materialSendReceiveMultiService.updateBatchById(list);
        materialDataMultiService.lambdaUpdate()
                .in(MaterialDataMulti::getReceiveId, receiveIdList)
                .set(MaterialDataMulti::getPushState, 0)
                .update();

        // 内部推送
        receiveIdList.forEach(e -> {
            weighDataListener.AfterCompletion(null, e, 2);
        });
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "批量编辑明细")
    @PostMapping("/detailUpdate")
    public ResponseEntity<Response> detailUpdate(@RequestBody SendReceiveMultiForm form) {
        materialSendReceiveMultiService.detailUpdate(form);
        return ResponseEntity.ok(new SuccessResponse());
    }
}
