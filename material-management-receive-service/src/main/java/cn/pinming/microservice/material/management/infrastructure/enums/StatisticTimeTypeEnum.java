package cn.pinming.microservice.material.management.infrastructure.enums;

import cn.hutool.core.date.DateField;

/**
 * <AUTHOR>
 * @since 2023/2/14 13:46
 */
public enum StatisticTimeTypeEnum {

    YEAR("year", "yyyy-MM", DateField.MONTH),
    MONTH("monty", "yyyy-MM-dd", DateField.DAY_OF_MONTH),
    WEEK("week", "yyyy-MM-dd", DateField.DAY_OF_MONTH),
    DAY("today", "yyyy-MM-dd HH", DateField.HOUR_OF_DAY),
    SCOPE(null, "yyyy-MM-dd", DateField.DAY_OF_MONTH)
    ;


    private final String value;
    private final String pattern;
    private final DateField dateField;

    StatisticTimeTypeEnum(String value, String pattern, DateField dateField) {
        this.value = value;
        this.pattern = pattern;
        this.dateField = dateField;
    }

    public String value() {
        return value;
    }
    public String pattern() {
        return pattern;
    }

    public static String getTimePattern(String timeType) {
        if (null == timeType) {
            return StatisticTimeTypeEnum.SCOPE.pattern;
        }

        for (StatisticTimeTypeEnum value : StatisticTimeTypeEnum.values()) {
            if (timeType.equals(value.value)) {
                return value.pattern;
            }
        }
        return StatisticTimeTypeEnum.SCOPE.pattern;
    }

    public static DateField getTimeDateField(String timeType) {
        if (null == timeType) {
            return StatisticTimeTypeEnum.SCOPE.dateField;
        }

        for (StatisticTimeTypeEnum value : StatisticTimeTypeEnum.values()) {
            if (timeType.equals(value.value)) {
                return value.dateField;
            }
        }
        return StatisticTimeTypeEnum.SCOPE.dateField;
    }
}
