package cn.pinming.microservice.material.management.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class WeightConverterUtil {

    private static final String KG_CHINESE = "千克";
    private static final String KG = "kg";
    private static final String POUND = "磅";
    private static final String KG_SPECIAL = "Kg";
    private static final String TONS = "吨";
    private static final List<String> VALID_UNITS = Arrays.asList(KG_CHINESE,KG,POUND, TONS);

    /**
     * 其他单位转换为吨
     * @param weight
     * @param unit
     * @return
     */
    public BigDecimal toTons(BigDecimal weight,String unit) {
        List<String> unitCheck = VALID_UNITS.stream().filter(e -> e.equals(unit)).collect(Collectors.toList());
        if (CollUtil.isEmpty(unitCheck)) {
        }

        boolean flag = unit.equals(KG_CHINESE) || unit.equals(KG) || unit.equals(KG_SPECIAL) || unit.equals(KG.toUpperCase());
        if (flag) {
            weight = NumberUtil.div(weight,1000);
        } else if (unit.equals(POUND)) {
            weight = NumberUtil.div(weight,2204.6);
        }
        return weight;
    }
}
