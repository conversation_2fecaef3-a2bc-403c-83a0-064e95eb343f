package cn.pinming.microservice.material.management.infrastructure.annotation;



import cn.pinming.microservice.material.management.infrastructure.annotation.enums.BusinessType;
import cn.pinming.microservice.material.management.infrastructure.annotation.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 日志
 * <AUTHOR>
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

}
