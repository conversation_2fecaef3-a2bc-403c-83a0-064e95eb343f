package cn.pinming.microservice.material.management.service;


import cn.pinming.microservice.material.management.resposity.vo.StatisticsConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description
 */
public interface MaterialGroupConfigService {

    /**
     * 总览-统计设置-物料
     *
     * @param tag  是否有自定义分类名称
     * @param unit 是否需要单位
     * @return
     */
    List<StatisticsConfigVO> materialGroupConfig(Boolean tag, Boolean unit);

    /**
     * 总览-统计设置-物料-关联材料类别ID
     *
     * @param tag 是否有自定义分类名称
     * @return
     */
    List<String> categoryIds(Boolean tag);

}
