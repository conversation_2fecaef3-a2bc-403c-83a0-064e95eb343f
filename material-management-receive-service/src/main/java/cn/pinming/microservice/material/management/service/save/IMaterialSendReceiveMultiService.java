package cn.pinming.microservice.material.management.service.save;

import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceiveMulti;
import cn.pinming.microservice.material.management.resposity.form.SendReceiveMultiForm;
import cn.pinming.microservice.material.management.resposity.query.MultiQuery;
import cn.pinming.microservice.material.management.resposity.vo.MultiVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 收货/发货单一车多料 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface IMaterialSendReceiveMultiService extends IService<MaterialSendReceiveMulti> {

    String add(SendReceiveMultiForm form);

    Page<MultiVO> pageByQuery(MultiQuery query);

    MultiVO detail(String sendReceiveId);

    void detailUpdate(SendReceiveMultiForm form);
}
