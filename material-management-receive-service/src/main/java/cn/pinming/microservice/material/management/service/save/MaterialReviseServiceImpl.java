package cn.pinming.microservice.material.management.service.save;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.management.dto.ProjectConfigDTO;
import cn.pinming.microservice.base.management.service.IProjectConfService;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.*;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.listener.WeighDataListener;
import cn.pinming.microservice.material.management.infrastructure.util.PicEchoUtil;
import cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDataDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.*;
import cn.pinming.microservice.material.management.resposity.form.MaterialReviseForm;
import cn.pinming.microservice.material.management.resposity.form.WeighSendFixForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialReviseMapper;
import cn.pinming.microservice.material.management.resposity.query.ReviseInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDataVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseVO;
import cn.pinming.microservice.material.management.service.*;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警处理人信息 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
@Slf4j
@Service
public class MaterialReviseServiceImpl extends ServiceImpl<MaterialReviseMapper, MaterialRevise> implements IMaterialReviseService {
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private MaterialReviseMapper materialReviseMapper;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @DubboReference
    private IMaterialContractService materialContractService;
    @Resource
    private SupplierServiceProxy supplierServiceProxy;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @DubboReference
    private IProjectConfService projectConfigService;
    @Resource
    private PicEchoUtil picEchoUtil;
    @Resource
    private UserUtil userUtil;
    @Resource
    private WeighDataListener weighDataListener;
    @Resource
    private IMaterialDataExpandService materialDataExpandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(MaterialReviseForm materialReviseForm) {
        AuthUser user = userUtil.getUser();
        //参数校验和数据查询
        ValidResult result = getValidResult(materialReviseForm, user);

        SimpleContractDetailDTO purchaseContractDetail = result.purchaseContractDetail;
        MaterialSendReceive sendReceive = result.sendReceive;
        MaterialData materialDataById = result.materialDataById;

//      更新收货单明细
        MaterialData materialData = new MaterialData();
        MaterialDataExpand materialDataExpand = new MaterialDataExpand();
        BeanUtil.copyProperties(materialReviseForm, materialData);
        // 净重实重处理
        BigDecimal weightNet = NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare());
        BigDecimal weightActual = NumberUtil.mul(NumberUtil.sub(1, NumberUtil.div(materialData.getMoistureContent(), 100)), NumberUtil.sub(weightNet, materialData.getWeightDeduct()));
        materialData.setWeightNet(weightNet);
        materialData.setWeightActual(weightActual);
//      实际数量
        materialData.setActualCount(NumberUtil.div(materialData.getWeightActual(), materialData.getRatio()));
//      偏差量
        BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(), materialData.getWeightSend()), materialData.getWeightSend(), 4), 100);
        materialData.setDeviationRate(deviation);
//      是否修正处理
        materialData.setIsRevise(MaterialReviseEnum.ALREADY_REVISE.value());
        materialData.setCompanyId(user.getCurrentCompanyId());
        materialData.setProjectId(user.getCurrentProjectId());
        materialData.setReceiveTime(LocalDateTime.now());

        // 材料库选择收料
        materialData.setDeviationStatus(null);
        if (StrUtil.isBlank(materialReviseForm.getContractDetailId())) {
            materialData.setDeviationStatus(DeviationStatusEnum.UNIDENTIFIED.value());
            materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
            materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.NO.value());
            //更新字段为空
            materialDataService.lambdaUpdate().eq(MaterialData::getId, materialData.getId()).set(MaterialData::getPurchaseOrderId, null)
                    .set(MaterialData::getContractDetailId, null).set(MaterialData::getReconciliationId, null).update();
        } else {
            //是否使用合同结算单位处理
            if (materialData.getWeightUnit().equals(purchaseContractDetail.getUnit())) {
                materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.YES.value());
            }
            // 按合同收料-将采购单信息置为null
            if (StrUtil.isBlank(materialReviseForm.getPurchaseOrderId())) {
                materialDataService.lambdaUpdate().eq(MaterialData::getId, materialData.getId()).set(MaterialData::getPurchaseOrderId, null).update();
            }
            //偏差状态
            if (ObjectUtil.isNotNull(purchaseContractDetail.getDeviationCalculate()) && purchaseContractDetail.getDeviationCalculate() == 1) {
                int flag = deviation.compareTo(purchaseContractDetail.getDeviationFloor());
                int flag1 = deviation.compareTo(purchaseContractDetail.getDeviationCeiling());
                if (flag < 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                }
                if (flag1 > 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                }
                if (flag >= 0 && flag1 <= 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                }
            }
        }

//      二级分类id和名称
        MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
        if (ObjectUtil.isNotEmpty(materialDto)) {
            materialData.setMaterialName(materialDto.getMaterialName());
            materialData.setCategoryId(materialDto.getMaterialCategoryId());
            materialData.setCategoryName(materialDto.getMaterialCategoryName());
        }
//      是否修订字段更改
        materialData.setIsRevise(MaterialReviseEnum.ALREADY_REVISE.value());
//      单据照片追加或保存
        if (CollUtil.isNotEmpty(materialReviseForm.getDocumentPics())) {
            fileServiceProxy.confirmFiles(materialReviseForm.getDocumentPics());
            String picStr = String.join(",", materialReviseForm.getDocumentPics());
            String pic;
            if (StrUtil.isNotBlank(materialDataById.getDocumentPic())) {
                pic = StrUtil.format("{},{}", materialDataById.getDocumentPic(), picStr);
                materialData.setDocumentPic(pic);
            } else {
                materialData.setDocumentPic(picStr);
            }
        }
        // 重置推送状态
        materialData.setPushState((byte) 0);
        materialDataService.updateById(materialData);
        MaterialDataExpand materialExpand = materialDataExpandService.lambdaQuery()
                .eq(MaterialDataExpand::getDataId, materialData.getId())
                .one();
        BeanUtils.copyProperties(materialReviseForm, materialDataExpand);
        materialDataExpand.setDataId(materialData.getId());
        if (ObjectUtil.isNotEmpty(materialExpand)) {
            materialDataExpand.setId(materialExpand.getId());
        }
        materialDataExpandService.saveOrUpdate(materialDataExpand);

//      更新收货单
        MaterialSendReceive materialSendReceive = new MaterialSendReceive();
        materialSendReceive.setId(sendReceive.getId());
        materialSendReceive.setTruckNo(materialReviseForm.getTruckNo());
        materialSendReceive.setTypeDetail(materialReviseForm.getTypeDetail());
        materialSendReceive.setExtNo(materialReviseForm.getExtNo());
        materialSendReceive.setReceiveUnit(materialReviseForm.getReceiveUnit());
        EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), user.getId());
        if (ObjectUtil.isNotNull(employee)) {
            materialSendReceive.setReceiver(employee.getMemberName());
        }
        materialSendReceiveService.updateById(materialSendReceive);

        String str = reviseStr(materialReviseForm, sendReceive, materialDataById);
//      数据修订实体保存
        if (StrUtil.isNotBlank(str) && !str.isEmpty()) {
            MaterialRevise materialRevise = new MaterialRevise();
            materialRevise.setReceiveId(materialReviseForm.getReceiveId());
            materialRevise.setMaterialDataId(materialReviseForm.getId());
            materialRevise.setReviseDetail(str);
            materialRevise.setReviseRemark(materialReviseForm.getReviseRemark());
            this.save(materialRevise);
        }

        // 内部推送
        weighDataListener.AfterCompletion(materialData.getId(),null,1);
    }

    @NotNull
    private ValidResult getValidResult(MaterialReviseForm materialReviseForm, AuthUser user) {
        SimpleContractDetailDTO simpleContractDetailDTO = null;
        MaterialSendReceive sendReceive;
        MaterialData materialDataById;
        if (StrUtil.isNotBlank(materialReviseForm.getContractDetailId())) {
            simpleContractDetailDTO = materialContractService.querySimpleContractDetail(materialReviseForm.getContractDetailId());
            // 合同校验
            if (ObjectUtil.isEmpty(simpleContractDetailDTO)) {
                throw new BOException(BOExceptionEnum.CONTRACT_DETAIL_IS_NOT_EXIST);
            }
            if (ObjectUtil.isNotEmpty(simpleContractDetailDTO) && !simpleContractDetailDTO.getMaterialId().equals(materialReviseForm.getMaterialId())) {
                throw new BOException(BOExceptionEnum.CHOOSE_CURRENT_MATERIAL);
            }
        } else {
            materialReviseForm.setContractDetailId(null);
            materialReviseForm.setPurchaseOrderId(null);
            materialReviseForm.setOrderNo(null);
        }
        // 毛皮重校验
        BigDecimal sub = NumberUtil.sub(materialReviseForm.getWeightGross(), materialReviseForm.getWeightTare());
        boolean tareJudge = sub.compareTo(BigDecimal.ZERO) <= 0;
        if (tareJudge) {
            throw new BOException(BOExceptionEnum.TARE_OR_GROSS_IS_ERROR);
        }
        boolean netJudge = materialReviseForm.getWeightDeduct().compareTo(sub) >= 0;
        if (netJudge) {
            throw new BOException(BOExceptionEnum.WEIGHT_DEDUCT_IS_ERROR);
        }

        sendReceive = materialSendReceiveService.getById(materialReviseForm.getReceiveId());
        materialDataById = materialDataService.getById(materialReviseForm.getId());
        if (ObjectUtil.isEmpty(sendReceive)) {
            throw new BOException(BOExceptionEnum.RECEIVE_IS_NOT_EXIST);
        }
        if (ObjectUtil.isEmpty(materialDataById)) {
            throw new BOException(BOExceptionEnum.MATERIAL_DATA_IS_NOT_EXIST);
        }

        // 含水率校验
        if (materialReviseForm.getMoistureContent() == null) {
            materialReviseForm.setMoistureContent(BigDecimal.ZERO);
        }
        if (materialDataById.getMoistureContent().compareTo(materialReviseForm.getMoistureContent()) != 0) {
            ProjectConfigDTO projectConfig = projectConfigService.getProjectConfigByType(user.getCurrentCompanyId(), user.getCurrentProjectId(), 2);
            if (ObjectUtil.isNotNull(projectConfig)) {
                if (projectConfig.getIsEnable() == 2) {
                    List<String> split = StrUtil.split(projectConfig.getScope(), ",");
                    List<BigDecimal> collect = split.stream().map(BigDecimal::new).collect(Collectors.toList());
                    boolean symbol = collect.contains(materialReviseForm.getMoistureContent().setScale(2, RoundingMode.HALF_UP));
                    if (!symbol) {
                        throw new BOException(BOExceptionEnum.MUST_IN_SCOPE);
                    }
                }
            }
        }
        return new ValidResult(simpleContractDetailDTO, sendReceive, materialDataById);
    }

    private static class ValidResult {
        public final SimpleContractDetailDTO purchaseContractDetail;
        public final MaterialSendReceive sendReceive;
        public final MaterialData materialDataById;

        public ValidResult(SimpleContractDetailDTO purchaseContractDetail, MaterialSendReceive sendReceive, MaterialData materialDataById) {
            this.purchaseContractDetail = purchaseContractDetail;
            this.sendReceive = sendReceive;
            this.materialDataById = materialDataById;
        }
    }

    @Override
    public MaterialReviseVO getMaterialRevise(ReviseInfoQuery reviseInfoQuery) {
        List<MaterialReviseDataDTO> materialReviseDataDTOList = materialReviseMapper.selectMaterialRevise(reviseInfoQuery);

        MaterialReviseVO materialReviseVO = new MaterialReviseVO();
        // 封装修订数据
        List<MaterialReviseDataVO> materialReviseDataVOList = materialReviseDataDTOList.stream().map(materialReviseDataDTO -> {
            MaterialReviseDataVO materialReviseDataVO = new MaterialReviseDataVO();
            BeanUtil.copyProperties(materialReviseDataDTO, materialReviseDataVO);
            EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(materialReviseDataDTO.getCompanyId(), materialReviseDataDTO.getReviserId());
            Optional.ofNullable(employee).ifPresent(e -> materialReviseDataVO.setReviseName(e.getMemberName()));
            return materialReviseDataVO;
        }).collect(Collectors.toList());
        materialReviseVO.setMaterialReviseDataVOList(materialReviseDataVOList);

        // 查询当前用户是否有权限修正数据 默认无权限
        materialReviseVO.setShowReviseBtn(false);

        AuthUser user = userUtil.getUser();
        if (user != null) {
            Boolean enable = materialHandlerService.enableHandle(user.getId(), HandleTypeEnum.REVISE_HANDLE.value());
            materialReviseVO.setShowReviseBtn(enable);
            if (!enable) {
                return materialReviseVO;
            }
        }

        return materialReviseVO;
    }

    @Override
    public MaterialReviseDetailVO getMaterialReviseDetail(ReviseInfoQuery reviseInfoQuery) {
        String receiveId = reviseInfoQuery.getReceiveId();
        Optional.ofNullable(receiveId).orElseThrow(() -> new BOException(BOExceptionEnum.RECEIVE_IS_NOT_EXIST));
        MaterialReviseDetailDTO materialReviseDetailDTO = materialReviseMapper.selectMaterialReviseDetail(reviseInfoQuery);
        if (materialReviseDetailDTO != null) {
            MaterialReviseDetailVO materialReviseDetailVO = new MaterialReviseDetailVO();
            BeanUtil.copyProperties(materialReviseDetailDTO, materialReviseDetailVO);
            String materialId = materialReviseDetailDTO.getMaterialId();
            // 材料id存在 取二级分类名称
            if (materialId != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(Integer.valueOf(materialId));
                if (materialDto != null) {
                    materialReviseDetailVO.setCategoryId(materialDto.getMaterialCategoryId());
                    materialReviseDetailVO.setCategoryName(materialDto.getMaterialCategoryName());
                    materialReviseDetailVO.setMaterialName(materialDto.getMaterialName());
                    materialReviseDetailVO.setMaterialSpec(StrUtil.format("{}", materialDto.getMaterialSpec()));
                }
            }
            // 封装供应商信息
            if (materialReviseDetailDTO.getSupplierId() != null) {
                SupplierDTO supplierDTO = supplierServiceProxy.findById(materialReviseDetailDTO.getSupplierId());
                if (ObjectUtil.isNotNull(supplierDTO)) {
                    materialReviseDetailVO.setSupplierName(supplierDTO.getName());
                }
            }

            materialReviseDetailVO.setDocumentPics(picEchoUtil.echo(materialReviseDetailDTO.getDocumentPic(), -1));

            DecimalFormat df = new DecimalFormat("###.####");
            if (Objects.nonNull(materialReviseDetailVO.getDeviationCeiling()) && Objects.nonNull(materialReviseDetailVO.getDeviationFloor())) {
                materialReviseDetailVO.setDeviationCeiling(materialReviseDetailVO.getDeviationCeiling());
                materialReviseDetailVO.setDeviationFloor(materialReviseDetailVO.getDeviationFloor());
            }

            return materialReviseDetailVO;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendFix(WeighSendFixForm form) {
        MaterialData data = new MaterialData();
        MaterialSendReceive materialSendReceive = new MaterialSendReceive();
        MaterialRevise materialRevise = new MaterialRevise();

        String tUuid = form.getTUuid();
        MaterialData materialData = materialDataService.getOne(new LambdaQueryWrapper<MaterialData>().eq(MaterialData::getWeighId, tUuid));

        BeanUtil.copyProperties(form, data);
        data.setWeightGross(materialData.getWeightGross());
        data.setWeightTare(materialData.getWeightTare());
        data.setWeightDeduct(materialData.getWeightDeduct());
        BigDecimal sub = NumberUtil.sub(NumberUtil.sub(data.getWeightGross(), data.getWeightTare()), data.getWeightDeduct());
        data.setWeightActual(sub);
        data.setId(materialData.getId());
        data.setReceiveTime(LocalDateTime.now());
        MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
        if (ObjectUtil.isNotEmpty(materialDto)) {
            data.setMaterialName(materialDto.getMaterialName());
            data.setCategoryId(materialDto.getMaterialCategoryId());
            data.setCategoryName(materialDto.getMaterialCategoryName());
        }
        if (CollUtil.isNotEmpty(form.getDocumentPics())) {
            fileServiceProxy.confirmFiles(form.getDocumentPics());
            String picStr = String.join(",", form.getDocumentPics());
            String pic;
            if (StrUtil.isNotBlank(materialData.getDocumentPic())) {
                pic = StrUtil.format("{},{}", materialData.getDocumentPic(), picStr);
                data.setDocumentPic(pic);
            } else {
                data.setDocumentPic(picStr);
            }
        }
        materialSendReceive.setId(form.getSendId());
        materialSendReceive.setExtNo(form.getExtNo());
        materialSendReceive.setTypeDetail(form.getTypeDetail());
        materialSendReceive.setReceiveUnit(form.getReceiveUnit());
        materialSendReceive.setTruckNo(form.getTruckNo());
        EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(userUtil.getCompanyId(), userUtil.getMid());
        if (ObjectUtil.isNotNull(employee)) {
            materialSendReceive.setReceiver(employee.getMemberName());
        }
        data.setIsRevise(MaterialReviseEnum.ALREADY_REVISE.value());
        materialDataService.updateById(data);
        materialSendReceiveService.updateById(materialSendReceive);
        String str = this.revise(materialData, data);
        if (StrUtil.isNotBlank(str) && !str.isEmpty()) {
            materialRevise.setMaterialDataId(data.getId());
            materialRevise.setReceiveId(form.getSendId());
            materialRevise.setReviseDetail(str);
            materialRevise.setReviseRemark(form.getReviseRemark());
            this.save(materialRevise);
        }

        // 内部推送
        weighDataListener.AfterCompletion(data.getId(),null,1);
    }

    private String revise(MaterialData materialData, MaterialData data) {
        String str = "";
//      接收方
        if (materialData.getSupplierName() != null && !materialData.getSupplierName().equals(data.getSupplierName())) {
            str = str + StrUtil.format("接收方从{}更换为{}；\n", materialData.getSupplierName(), data.getSupplierName());
        }
        if (materialData.getSupplierName() == null) {
            str = str + StrUtil.format("接收方从null更换为{}；\n", data.getSupplierName());
        }
//      材料
        if (materialData.getMaterialId() != null && !materialData.getMaterialId().equals(data.getMaterialId())) {
            str = str + StrUtil.format("材料从{}更换为{}；\n", materialData.getMaterialName(), data.getMaterialName());
        }
        if (materialData.getMaterialId() == null) {
            str = str + StrUtil.format("材料从null更换为{}；\n", data.getMaterialName());
        }
//      转换系数
        if (materialData.getRatio() != null && materialData.getRatio().compareTo(data.getRatio()) != 0) {
            str = str + StrUtil.format("转换系数从{}更换为{}；\n", materialData.getRatio(), data.getRatio());
        }
        if (materialData.getRatio() == null) {
            str = str + StrUtil.format("转换系数从null更换为{}；\n", data.getRatio());
        }
//      发货数量
        if (materialData.getWeightSend() != null && materialData.getWeightSend().compareTo(data.getWeightSend()) != 0) {
            str = str + StrUtil.format("发货数量从{}更换为{}；\n", materialData.getWeightSend(), data.getWeightSend());
        }
        if (materialData.getWeightSend() == null) {
            str = str + StrUtil.format("发货数量从null更换为{}；\n", data.getWeightSend());
        }
//      实际数量
        if (materialData.getActualCount() != null && materialData.getActualCount().compareTo(data.getActualCount()) != 0) {
            str = str + StrUtil.format("实际数量从{}更换为{}；\n", materialData.getActualCount(), data.getActualCount());
        }
        if (materialData.getActualCount() == null) {
            str = str + StrUtil.format("实际数量从null更换为{}；\n", data.getActualCount());
        }
//      计量单位
        if (StrUtil.isNotBlank(materialData.getWeightUnit()) && !materialData.getWeightUnit().equals(data.getWeightUnit())) {
            str = str + StrUtil.format("结算单位从{}更换为{}；\n", materialData.getWeightUnit(), data.getWeightUnit());
        }
        if (materialData.getWeightUnit() == null) {
            str = str + StrUtil.format("结算单位从null更换为{}；\n", data.getWeightUnit());
        }
        return str;
    }

    private String reviseStr(MaterialReviseForm materialReviseForm, MaterialSendReceive sendReceive, MaterialData materialDataById) {
        String str = "";
//      供应商
        boolean supplierJudge1 = materialDataById.getSupplierId() == null && StrUtil.isBlank(materialDataById.getSupplierName());
        boolean supplierJudge2 = materialDataById.getSupplierId() == null && StrUtil.isNotBlank(materialDataById.getSupplierName()) && !materialDataById.getSupplierName().equals(materialReviseForm.getSupplierName());
        boolean supplierJudge3 = materialDataById.getSupplierId() != null && !materialDataById.getSupplierId().equals(materialReviseForm.getSupplierId());
        if (supplierJudge1) {
//          原供应商id和名称为空，对供应商进行修订
            str = str + StrUtil.format("供应商从无修订为{}；\n", materialReviseForm.getSupplierName());
        }
        if (supplierJudge2) {
//          原供应商id为空，名称不为空，对供应商进行修订
            str = str + StrUtil.format("供应商从{}修订为{}；\n", materialDataById.getSupplierName(), materialReviseForm.getSupplierName());
        }
        if (supplierJudge3) {
//          原供应商id不为空，对供应商进行修订
            str = str + StrUtil.format("供应商从{}修订为{}；\n", materialDataById.getSupplierName(), materialReviseForm.getSupplierName());
        }

//      材料
        boolean materialJudge1 = materialDataById.getMaterialId() == null && StrUtil.isBlank(materialDataById.getMaterialName());
        boolean materialJudge2 = materialDataById.getMaterialId() == null && StrUtil.isNotBlank(materialDataById.getMaterialName());
        boolean materialJudge3 = materialDataById.getMaterialId() != null && !materialDataById.getMaterialId().equals(materialReviseForm.getMaterialId());
        if (materialJudge1) {
//          原材料id和名称为空，对材料进行修订
            str = str + StrUtil.format("材料从无修订为{}；\n", materialReviseForm.getMaterialName());
        }
        if (materialJudge2) {
//          原材料id为空，名称不为空，对材料进行修订
            str = str + StrUtil.format("材料从{}修订为{}；\n", materialDataById.getMaterialName(), materialReviseForm.getMaterialName());
        }
        if (materialJudge3) {
//          原材料id不为空，对材料进行修订
            str = str + StrUtil.format("材料从{}修订为{}；\n", materialDataById.getMaterialName(), materialReviseForm.getMaterialName());
        }

//      转换系数
        boolean ratioJudge1 = materialDataById.getRatio() == null;
        boolean ratioJudge2 = materialDataById.getRatio() != null && materialDataById.getRatio().compareTo(materialReviseForm.getRatio()) != 0;
        if (ratioJudge1) {
//          原转换系数为空,对转换系数进行修订
            str = str + StrUtil.format("转换系数从无修订为{}；\n", materialReviseForm.getRatio());
        }
        if (ratioJudge2) {
//          原转换系数不为空，对转换系数进行修订
            str = str + StrUtil.format("转换系数从{}修订为{}；\n", materialDataById.getRatio(), materialReviseForm.getRatio());
        }

//      面单应收量
        boolean weightSendJudge1 = materialDataById.getWeightSend() == null;
        boolean weightSendJudge2 = materialDataById.getWeightSend() != null && materialDataById.getWeightSend().compareTo(materialReviseForm.getWeightSend()) != 0;
        if (weightSendJudge1) {
//          原面单应收为空,对面单应收进行修订
            str = str + StrUtil.format("面单应收量从无修订为{}；\n", materialReviseForm.getWeightSend());
        }
        if (weightSendJudge2) {
//          原面单应收不为空,对面单应收进行修订
            str = str + StrUtil.format("面单应收量从{}修订为{}；\n", materialDataById.getWeightSend(), materialReviseForm.getWeightSend());
        }

//      实收数量
        if (ObjectUtil.isNotNull(materialReviseForm.getActualReceive())) {
            boolean actualReceiveJudge1 = materialDataById.getActualReceive() == null;
            boolean actualReceiveJudge2 = materialDataById.getActualReceive() != null && materialDataById.getActualReceive().compareTo(materialReviseForm.getActualReceive()) != 0;
            if (actualReceiveJudge1) {
//          原实收数量为空,对实收数量进行修订
                str = str + StrUtil.format("实收数量从无修订为{}；\n", materialReviseForm.getActualReceive());
            }
            if (actualReceiveJudge2) {
//          原实收数量不为空,对实收数量进行修订
                str = str + StrUtil.format("实收数量从{}修订为{}；\n", materialDataById.getActualReceive(), materialReviseForm.getActualReceive());
            }
        }

//      车牌号
        boolean truckNoJudge1 = sendReceive.getTruckNo() == null;
        boolean truckNoJudge2 = sendReceive.getTruckNo() != null && !sendReceive.getTruckNo().equals(materialReviseForm.getTruckNo());
        if (truckNoJudge1) {
//          原车牌号为空,对车牌号进行修订
            str = str + StrUtil.format("车牌号从无修订为{}；\n", materialReviseForm.getTruckNo());
        }
        if (truckNoJudge2) {
//          原车牌号不为空,对车牌号进行修订
            str = str + StrUtil.format("车牌号从{}修订为{}；\n", sendReceive.getTruckNo(), materialReviseForm.getTruckNo());
        }

//      毛重
        boolean weightGrossJudge = materialDataById.getWeightGross().compareTo(materialReviseForm.getWeightGross()) != 0;
        if (weightGrossJudge) {
//          原毛重不为空,对毛重进行修订
            str = str + StrUtil.format("毛重从{}修订为{}；\n", materialDataById.getWeightGross(), materialReviseForm.getWeightGross());
        }

//      皮重
        boolean weightTareJudge = materialDataById.getWeightTare().compareTo(materialReviseForm.getWeightTare()) != 0;
        if (weightTareJudge) {
//          原皮重不为空,对皮重进行修订
            str = str + StrUtil.format("皮重从{}修订为{}；\n", materialDataById.getWeightTare(), materialReviseForm.getWeightTare());
        }

//      扣重
        boolean weightDeductJudge = materialDataById.getWeightDeduct().compareTo(materialReviseForm.getWeightDeduct()) != 0;
        if (weightDeductJudge) {
//          原扣重不为空,对扣重进行修订
            str = str + StrUtil.format("扣重从{}修订为{}；\n", materialDataById.getWeightDeduct(), materialReviseForm.getWeightDeduct());
        }

//      实际数量
        boolean actualCountJudge1 = materialDataById.getActualCount() == null;
        boolean actualCountJudge2 = materialDataById.getActualCount() != null && materialDataById.getActualCount().compareTo(materialReviseForm.getActualCount()) != 0;
        if (actualCountJudge1) {
//          原实际数量为空,对实际数量进行修订
            str = str + StrUtil.format("实际数量从无修订为{}；\n", materialReviseForm.getActualCount());
        }
        if (actualCountJudge2) {
//          原实际数量不为空,对实际数量进行修订
            str = str + StrUtil.format("实际数量从{}修订为{}；\n", materialDataById.getActualCount(), materialReviseForm.getActualCount());
        }

//      单据照片
        if (CollUtil.isNotEmpty(materialReviseForm.getDocumentPics())) {
            str = str + StrUtil.format("上传了{}张单据照片；\n", materialReviseForm.getDocumentPics().size());
        }
//      计划使用部位
        if (StrUtil.isNotBlank(materialReviseForm.getPosition()) && StrUtil.isBlank(materialDataById.getPosition())) {
            str = str + StrUtil.format("计划使用部位从null修订为{}；\n", materialReviseForm.getPosition());
        }
        if (StrUtil.isNotBlank(materialReviseForm.getPosition()) && StrUtil.isNotBlank(materialDataById.getPosition()) && !materialReviseForm.getPosition().equals(materialDataById.getPosition())) {
            str = str + StrUtil.format("计划使用部位从{}修订为{}；\n", materialDataById.getPosition(), materialReviseForm.getPosition());
        }
//      含水率
        if (materialDataById.getMoistureContent().compareTo(materialReviseForm.getMoistureContent()) != 0) {
            str = str + StrUtil.format("含水率从{}修订为{}；\n", materialDataById.getMoistureContent(), materialReviseForm.getMoistureContent());
        }

        return str;
    }
}
