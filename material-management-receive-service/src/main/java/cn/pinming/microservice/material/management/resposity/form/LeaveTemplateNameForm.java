package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 出场名称模板form
 * <AUTHOR>
 * @since 2022/6/28 14:55
 */
@Data
public class LeaveTemplateNameForm {

    @ApiModelProperty(value = "旧模板名")
    private String oldTemplateName;

    @ApiModelProperty(value = "新模板名")
    @NotBlank(message = "模板名不能为空")
    private String newTemplateName;

    @ApiModelProperty(value = "模板类型 0 出场单 1 称重单")
    private Byte templateType;
}
