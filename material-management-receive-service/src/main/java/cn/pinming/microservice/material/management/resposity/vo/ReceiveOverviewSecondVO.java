package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/30 15:42
 */
@Data
public class ReceiveOverviewSecondVO {

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "累计")
    private Integer total;

    @ApiModelProperty(value = "本月新增")
    private Integer monthRise;

    @ApiModelProperty(value = "各单位每月收料车次数")
    private List<ReceiveOverviewCarCount> receiveOverviewCarCountList;

}
