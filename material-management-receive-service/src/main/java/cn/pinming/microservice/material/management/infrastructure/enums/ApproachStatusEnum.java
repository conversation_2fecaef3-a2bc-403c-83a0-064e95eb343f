package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * 进场状态枚举
 */
public enum ApproachStatusEnum {

    QUALIFIED((byte)1, "合格进场"),
    UNQUALIFIED((byte)2, "不合格进场");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    ApproachStatusEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (ApproachStatusEnum statusEnum : ApproachStatusEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }

}
