package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 收货/发货单一车多料
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Getter
@Setter
@TableName("d_material_send_receive_multi")
@ApiModel(value = "MaterialSendReceiveMulti对象", description = "收货/发货单一车多料")
public class MaterialSendReceiveMulti implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("收货单号")
    private String receiveNo;

    @ApiModelProperty("类型，1：收货；2：发货")
    private Integer type;

    @ApiModelProperty("收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采 15 直入直出    发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Integer typeDetail;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("分配方式 1 按随车面单量 2 按理重")
    private Integer distribution;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("净重")
    private BigDecimal weightNet;

    @ApiModelProperty("扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty("含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty("实重")
    private BigDecimal weightActual;

    @ApiModelProperty("进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty("出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty("收货/发货时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty("外部运单发货时间")
    private LocalDateTime deliveryTime;

    @ApiModelProperty("进场图片")
    private String enterPic;

    @ApiModelProperty("出场图片")
    private String leavePic;

    @ApiModelProperty("签名照片")
    private String signaturePic;

    @ApiModelProperty("签名人照片")
    private String signerPic;

    @ApiModelProperty("单据照片")
    private String documentPic;

    @ApiModelProperty("基石数据id1")
    @TableField(value = "record_id_1")
    private String recordId1;

    @ApiModelProperty("基石数据id2")
    @TableField(value = "record_id_2")
    private String recordId2;

    @ApiModelProperty("基石确认单id")
    private String confirmId;

    @ApiModelProperty("外部系统单号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String extNo;

    @ApiModelProperty("确认单外部系统编号")
    private String confirmExtNo;

    @ApiModelProperty("收货人")
    private String receiver;

    @ApiModelProperty("司磅员")
    private String operator;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("司机电话")
    private String driverNumber;

    @ApiModelProperty("备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    @ApiModelProperty("领用单位")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String receiveUnit;

    @ApiModelProperty("0.未推送 1.已推送 2.推送失败")
    private Integer pushState;

    @ApiModelProperty("收料确认方式 1 扫码组装 2 司机自助确认 3 OCR结果落库")
    private Integer confirmType;

    @ApiModelProperty("数据类型 1 复磅情况 (基石确认单同步至桩桩判断用)")
    private Integer mixingType;

    @ApiModelProperty(value = "面单总偏差量")
    private BigDecimal sendDeviationAll;

    @ApiModelProperty(value = "面单总偏差率")
    private BigDecimal sendDeviationRateAll;

    @ApiModelProperty(value = "理重总偏差量")
    private BigDecimal theoreticalDeviationAll;

    @ApiModelProperty(value = "理重总偏差率")
    private BigDecimal theoreticalDeviationRateAll;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差")
    private Integer deviationStatus;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;
}
