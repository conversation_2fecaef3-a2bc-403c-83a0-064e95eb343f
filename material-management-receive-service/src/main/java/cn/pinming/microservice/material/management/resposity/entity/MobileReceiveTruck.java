package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 移动收料货车表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mobile_receive_truck")
@ApiModel(value = "MobileReceiveTruck对象", description = "移动收料货车表")
public class MobileReceiveTruck implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收货单id")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "车牌号码")
    @TableField("truck_no")
    private String truckNo;

    @ApiModelProperty(value = "车辆到场时间")
    @TableField("truck_time")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "车头车尾图片")
    @TableField("truck_pic")
    private String truckPic;

    @ApiModelProperty(value = "货/铭牌图片")
    @TableField("goods_pic")
    private String goodsPic;

    @ApiModelProperty(value = "送货单图片")
    @TableField("send_pic")
    private String sendPic;

    @ApiModelProperty(value = "经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty(value = "位置详情")
    @TableField("location")
    private String location;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
