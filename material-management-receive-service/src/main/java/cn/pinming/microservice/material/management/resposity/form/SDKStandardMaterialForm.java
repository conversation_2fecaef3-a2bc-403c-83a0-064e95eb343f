package cn.pinming.microservice.material.management.resposity.form;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SDK称重验收标准Form
 */
@Data
public class SDKStandardMaterialForm {
    // 收发料信息
    @NotNull(message = "过磅类型不能为空")
    @ApiModelProperty(value = "过磅类型 1 收料 2 发料")
    private Byte type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采  15 直入直出     发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    @NotNull(message = "收发料子类型不能为空")
    private Byte typeDetail;

    // 材料信息
    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Integer supplierId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "材料ID")
    @NotNull(message = "材料ID不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "二级分类id")
    @NotNull(message = "二级分类id不能为空")
    private Integer categoryId;

    @ApiModelProperty(value = "结算单位")
    @NotBlank(message = "结算单位不能为空")
    private String unit;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    // 重量信息
    @ApiModelProperty(value = "毛重")
    @NotNull(message = "毛重不能为空")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    @NotNull(message = "皮重不能为空")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "随车面单数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "转换系数")
    @NotNull(message = "转换系数不能为空")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    // 进出场信息
    @ApiModelProperty(value = "进场时间")
    @NotNull(message = "进场时间不能为空")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    @NotNull(message = "出场时间不能为空")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "进场照片")
    private List<String> enterPic;

    @ApiModelProperty(value = "出场照片")
    private List<String> leavePic;

    @ApiModelProperty(value = "单据照片 base64")
    private List<String> documentPic;

    @ApiModelProperty(value = "单据照片 uuid")
    private List<String> documentPicUuid;

    @ApiModelProperty(value = "收货/发货时间")
    private LocalDateTime receiveTime;

    // 车辆信息
    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    // SDK
    @NotBlank(message = "基石称重数据id不能为空")
    @ApiModelProperty("基石终端称重数据id一")
    private String recordId1;

    @NotBlank(message = "基石称重数据id不能为空")
    @ApiModelProperty("基石终端称重数据id二")
    private String recordId2;

    // 其他
    private Long wbsId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否使用合同转换系数 1，是 2，否")
    private Byte isContractRateUsed;

    @ApiModelProperty(value = "是否使用合同结算单位 1，是 2，否")
    private Byte isContractUnitUsed;

    @ApiModelProperty("领用单位")
    private String receiveUnit;

    @ApiModelProperty("是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    // 发料
    @ApiModelProperty(value = "收货单位")
    private String receiveProject;
}
