package cn.pinming.microservice.material.management.infrastructure.enums;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


public enum WeighTypeDetailEnum {
    ONE((byte)1,"采购"),
    TWO((byte)2,"调入"),
    THREE((byte)3,"甲供"),
    FOUR((byte)4,"发料"),
    FIVE((byte)5,"废旧物料"),
    SIX((byte)6,"调出"),
    SEVEN((byte)7,"售出"),
    EIGHT((byte)8,"自采"),
    NINE((byte)9,"集采"),
    TEN((byte)10,"调拨出库"),
    EVELEN((byte)11,"领用出库"),
    TWELVE((byte)12,"废品处置"),
    THIRTEEN((byte)13,"余料处理"),
    FOURTEEN((byte)14,"其他"),
    FIVETEEN((byte)15,"直入直出"),
    SIXTEEN((byte)16,"退料出库");


    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WeighTypeDetailEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String getWeighTypeDetailDes(Byte key) {
        List<WeighTypeDetailEnum> list = Arrays.stream(WeighTypeDetailEnum.values())
                .filter(type -> type.value() == key).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0).description();
        }
        return null;
    }
}
