
package cn.pinming.microservice.material.management.resposity.dto;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("物料收货单，推送数据标准对象，IOT平台接收的物料数据")
@Getter
@Setter
@ToString
public class PushDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "供应商id")
    private Integer dataSourceType;

    @ApiModelProperty(value = "设备sn号")
    private String deviceSn;

    @ApiModelProperty(value = "采购单编号")
    private String purchaseId;

    @ApiModelProperty(value = "类型，1：收货；2：发货 4：其他模式")
    private Byte type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货人")
    private String receiver;

    @ApiModelProperty(value = "卡车车牌")
    private String truckNo;

    @ApiModelProperty(value = "进场时间（毛重时间）")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "进场图片")
    private List<String> enterPicList;

    @ApiModelProperty(value = "出场时间（皮重时间）")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "出场图片")
    private List<String> leavePicList;

    @ApiModelProperty(value = "单据图片")
    private List<String> documentPicList;

    @ApiModelProperty(value = "收货/发货时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "司磅员")
    private String operator;

    @ApiModelProperty(value = "司机")
    private String driver;

    @ApiModelProperty(value = "材料主管")
    private String materialManager;

    @ApiModelProperty(value = "备注 暂时放车辆预报备id")
    private String remark;

    /**
     * 扩展，1条收货记录对应2条称重记录，一条卸货前，一条卸货后
     */
    @ApiModelProperty(value = "物料过磅称重")
    private List<MaterialWeightDTO> materialWeightList;

    /**
     * 扩展，一条收货单对应多条收货记录
     */
    @ApiModelProperty(value = "物料收货数据")
    private List<MaterialDataDTO> materialDataList;

    public String getPreTruckReportDetailId() {
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getStr("preReportId");
        }
        return remark;
    }

    public Integer getCompanyId() {
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getInt("companyId");
        }
        return null;
    }

    public Integer getProjectId() {
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getInt("projectId");
        }
        return null;
    }

    public Integer getSupplierId() {
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getInt("supplierId");
        }
        return null;
    }

    public String getSupplierName() {
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getStr("supplierName");
        }
        return null;
    }

    public String getReceiverName() {

        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getStr("receiverName");
        }
        return null;
    }

    public String getWeighId(){
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getStr("weighId");
        }
        return null;
    }

    public String getActualReceive(){
        if (StrUtil.isNotEmpty(remark) && JSONUtil.isTypeJSON(remark)) {
            JSONObject obj = JSONUtil.parseObj(remark);
            return obj.getStr("actualReceive");
        }
        return null;
    }
}
