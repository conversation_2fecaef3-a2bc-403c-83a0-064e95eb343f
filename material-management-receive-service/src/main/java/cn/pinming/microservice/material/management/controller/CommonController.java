package cn.pinming.microservice.material.management.controller;

import cn.pinming.material.v2.common.response.Response;
import cn.pinming.material.v2.common.response.SingleResponse;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.resposity.dto.ReceiptRecyclePushDTO;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.management.service.save.IMaterialDataSaveService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "common", tags = {"common"})
@RestController
@RequestMapping("/api/common")
@Slf4j
public class CommonController {
    @Resource
    private IMaterialDataSaveService materialDataSaveService;
    @Resource
    private FileServiceProxy fileServiceV2Proxy;

    @PostMapping("/sync")
    public SingleResponse<?> OCRSync(@RequestBody ReceiptRecyclePushDTO dto) {
        log.info("收到单据回收:{}",dto);
        materialDataSaveService.OCRSync(dto);
        return SingleResponse.of(Response.buildSuccess());
    }

    @PostMapping("/confirmPic")
    public SingleResponse<?> confirmPic(@RequestBody List<String> uuids) {
        fileServiceV2Proxy.confirmFiles(uuids);
        return SingleResponse.of(Response.buildSuccess());
    }

    @PostMapping("/inner/sync")
    public SingleResponse<?> innerSync(@RequestBody List<PushRangeForm> range) {
        materialDataSaveService.innerSync(range);
        return SingleResponse.of(Response.buildSuccess());
    }

    @PostMapping("/warning")
    public SingleResponse<?> warning(@RequestBody List<String> ids) {
        materialDataSaveService.warning(ids);
        return SingleResponse.of(Response.buildSuccess());
    }
}
