package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SummaryDeliverySecondVO implements Serializable {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    private List<SummaryDeliverySecondDetailVO> list;
}
