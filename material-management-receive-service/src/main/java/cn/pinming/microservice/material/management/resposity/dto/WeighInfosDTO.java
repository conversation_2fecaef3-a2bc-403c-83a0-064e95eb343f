package cn.pinming.microservice.material.management.resposity.dto;

import cn.pinming.weaponx.wrapper.dto.SimpleProjectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighInfosDTO extends SimpleProjectDTO {

    @ApiModelProperty("过磅重量")
    private BigDecimal weighWeight;

    @ApiModelProperty("第一次使用时间")
    private LocalDateTime firstUsedTime;

    @ApiModelProperty("最后一次使用时间")
    private LocalDateTime lastUsedTime;

    @ApiModelProperty("收料过磅车次")
    private Integer weighingCarNumber;

    @ApiModelProperty("发料过磅车次")
    private Integer sendingCarNumber;

    @ApiModelProperty("过磅总车次")
    private Integer weighingCarCount;
}
