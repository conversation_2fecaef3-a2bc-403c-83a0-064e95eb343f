package cn.pinming.microservice.material.management.service.impl;

import cn.pinming.microservice.material.management.resposity.entity.StatisticsUseConfig;
import cn.pinming.microservice.material.management.resposity.mapper.StatisticsUseConfigMapper;
import cn.pinming.microservice.material.management.service.IStatisticsUseConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 统计配置表-使用率 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-11 19:22:51
 */
@Service
public class StatisticsUseConfigServiceImpl extends ServiceImpl<StatisticsUseConfigMapper, StatisticsUseConfig> implements IStatisticsUseConfigService {

}
