package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MobileReceiveVerifyVO {
    @ApiModelProperty(value = "收货单id")
    private String id;

    @ApiModelProperty(value = "收货单编号")
    private String receiveNo;

    @ApiModelProperty(value = "收货物料id")
    private Integer materialId;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "材料二级名称")
    private String categoryName;

    @ApiModelProperty(value = "品种及规格")
    private String materialName;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty(value = "偏差状态")
    private String deviationStatusStr;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "面单应收量")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "结算单位")
    private String unit;

    @ApiModelProperty(value = "收料类型 1有合同收料-按合同，2有合同收料-按采购单，3无合同收料")
    private String receiveType;

    @ApiModelProperty(value = "采购单id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "是否被该对账选择")
    private Boolean isChoose;

    @ApiModelProperty(value = "对账id")
    private String reconciliationId;
}
