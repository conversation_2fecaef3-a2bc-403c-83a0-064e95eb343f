package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:58
 */
@Data
public class MaterialVerifyReceiveDetailDTO {

    @ApiModelProperty(value = "收料详情ID")
    private String receiveId;

    @ApiModelProperty(value = "收料详情ID")
    private String receiveDataId;

    @ApiModelProperty(value = "收料单号")
    private String receiveNo;

    @ApiModelProperty(value = "车牌/车号")
    private String truckNo;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "分类ID")
    private String categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "面单数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态(0 正常 1 负偏差 2 正偏差)")
    private Byte deviationStatus;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "是否选择")
    private Boolean isChoose;

    @ApiModelProperty(value = "进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "合同详情id")
    private String contractDetailId;

    @ApiModelProperty(value = "是否推送至仓库 0,否 1,是")
    private Byte isPushed;

}
