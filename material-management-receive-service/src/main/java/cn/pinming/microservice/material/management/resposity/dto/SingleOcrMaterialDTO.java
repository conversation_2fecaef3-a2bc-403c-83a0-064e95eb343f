package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SingleOcrMaterialDTO {
    @ApiModelProperty("合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "合同约定转换系数")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "材料规格")
    private String materialSpec;

    @ApiModelProperty(value = "源计量单位")
    private String sourceUnitName;

    @ApiModelProperty(value = "目标计量单位")
    private String targetUnitName;
}
