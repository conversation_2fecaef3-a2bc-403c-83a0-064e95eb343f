package cn.pinming.microservice.material.management.infrastructure.enums;

public enum ReceiveTypeEnum {
    CONTRACT((byte)1, "移动收料-按合同"),
    PURCHASE((byte)2, "移动收料-按采购单"),
    NONE((byte)3,"移动收料-无合同无采购单"),
    CONTRACTANDPURCHASE((byte)6,"移动收料-有合同有采购单"),

    WEIGHWITHPURCHASE((byte)4,"地磅收料-按采购单"),
    WEIGHWITHOUTPURCHASE((byte)5,"地磅收料-无采购单"),

    NULL((byte)7,"移动收料-无合同无采购料，地磅收料-无采购单"),
    HAVE((byte)8,"移动收料-有合同有采购单，地磅收料-有采购单");


    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    ReceiveTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }
}
