package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:58
 */
@Data
public class MaterialVerifyReceiveDTO extends MaterialVerifyReceiveDetailDTO {

    @ApiModelProperty(value = "收料id")
    private String weighRecordId;

    @ApiModelProperty(value = "收料详情id")
    private String receiveId;

    @ApiModelProperty(value = "收料详情id")
    private String receiveDataId;

    @ApiModelProperty(value = "预警来源编号")
    private String warningSourceNo;

    @ApiModelProperty(value = "预警是否处理完毕")
    private Boolean warningStatus;

    @ApiModelProperty(value = "是否修正")
    private Boolean isRevise;



}
