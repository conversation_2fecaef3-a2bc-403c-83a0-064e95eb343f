package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MobileReceiveMaterialDetailForm {

//    @ApiModelProperty(value = "到场品牌,多个以,分隔")
//    private String brand;

    @ApiModelProperty(value = "是否标准件 1，是；2，否")
//    @NotNull
    private Byte isStandard;

    @ApiModelProperty(value = "件数")
    @Min(value = 0,message = "件数必须为正整数")
    private Integer number;

    @ApiModelProperty(value = "每件数含量")
    @Digits(fraction = 6,message = "每件数含量小数点上限为6位", integer = 999)
    @DecimalMin(value = "0",message = "每件数含量必须为正数")
    private BigDecimal content;

    @ApiModelProperty(value = "含量单位")
//    @NotBlank
    private String unit;

    @ApiModelProperty(value = "结算单位")
//    @NotBlank
    private String settlementUnit;

    @ApiModelProperty(value = "换算系数")
    @DecimalMin(value = "0",message = "换算系数必须为正数")
    @Digits(fraction = 6,message = "换算系数小数点上限为6位", integer = 999)
    private BigDecimal ratio;

    @ApiModelProperty(value = "智能点数图片")
    private List<String> pointsPic;

    @ApiModelProperty(value = "每\"含量单位\"定尺")
    @Digits(fraction = 2,message = "每\"含量单位\"定尺小数点上限为2位", integer = 999)
    @DecimalMin(value = "0",message = "每\"含量单位\"定尺必须为正数")
    private BigDecimal lengthPerUnit;

    @ApiModelProperty(value = "实际称重小计")
    @Digits(fraction = 3,message = "实际称重小计小数点上限为3位", integer = 999)
    @DecimalMin(value = "0",message = "实际称重小计必须为正数")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "实理偏差值")
    @Digits(fraction = 3,message = "实理偏差值小数点上限为3位", integer = 999)
    private BigDecimal deviationValue;

    @ApiModelProperty(value = "实理偏差率")
    @Digits(fraction = 2,message = "实理偏差率小数点上限为2位", integer = 999)
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "理重小计")
    @Digits(fraction = 3,message = "理重小计小数点上限为3位", integer = 999)
    @DecimalMin(value = "0",message = "理重小计必须为正数")
    private BigDecimal theoreticalWeight;
}
