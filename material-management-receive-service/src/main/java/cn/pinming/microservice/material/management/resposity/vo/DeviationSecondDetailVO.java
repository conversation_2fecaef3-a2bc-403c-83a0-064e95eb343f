package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 偏差下钻详情数据
 *
 * <AUTHOR>
 * @since 2022/4/12 19:57
 */
@Data
public class DeviationSecondDetailVO {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("超负差")
    private int negative;

    @ApiModelProperty("超正差")
    private int positive;

    @ApiModelProperty("正常")
    private int normal;

    @ApiModelProperty("无法确定偏差状态")
    private int unidentified;
}
