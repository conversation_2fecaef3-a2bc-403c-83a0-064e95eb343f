package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WeighTypeDetailEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.util.WarningUtil;
import cn.pinming.microservice.material.management.resposity.dto.MaterialWeighInfoDTO;
import cn.pinming.microservice.material.management.resposity.dto.WarningJudgeResultDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialWeighBaseVO;
import cn.pinming.microservice.material.management.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.purchase.management.service.IMaterialPurchaseService;
import cn.pinming.microservice.purchase.management.vo.PurchaseOrderVO;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收货/发货单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Slf4j
@Service
public class MaterialSendReceiveServiceImpl extends ServiceImpl<MaterialSendReceiveMapper, MaterialSendReceive> implements IMaterialSendReceiveService {
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private SupplierServiceProxy cooperateServiceProxy;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private UserUtil userUtil;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private WarningUtil warningUtil;
    @DubboReference
    private IMaterialContractService materialContractService;
    @DubboReference
    private IMaterialPurchaseService materialPurchaseService;

    /**
     * 地磅收货列表
     *
     * @param query
     * @return
     */
    @Override
    public IPage<MaterialWeighBaseVO> showWeightInfo(MaterialWeighQuery query) {
        AuthUser user = userUtil.getUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
        }
//        // 如果项目角色为拌合站，那收料列表只展示原料进场和mixing_type为null的数据
//        List<Integer> projectIdsByPluginNo = projectServiceProxy.getProjectIdsByPluginNo("mixing-plant-management");
//        if (CollUtil.isNotEmpty(projectIdsByPluginNo)) {
//            if (projectIdsByPluginNo.contains(user.getCurrentProjectId())) {
//                query.setMixingType(1);
//            }
//        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<MaterialWeighInfoDTO> materialWeighInfoDTOS = materialDataMapper.queryWeightReceiveInfo(query);
        IPage<MaterialWeighBaseVO> result = this.getVO(materialWeighInfoDTOS, query);
        return result;
    }

    private IPage<MaterialWeighBaseVO> getVO(IPage<MaterialWeighInfoDTO> materialWeighInfoDTOS, MaterialWeighQuery query) {
        IPage<MaterialWeighBaseVO> materialWeighInfoVOS = new Page<>();

        List<MaterialWeighBaseVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(materialWeighInfoDTOS.getRecords())) {
            AuthUser user = userUtil.getUser();
            Map<String, SimpleContractDetailDTO> contractDetailDTOMap = new HashMap<>();

            List<Integer> materialIdList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getMaterialId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Integer> receiveProjectList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getReceiveProject)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Integer> supplierIds = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getSupplierId).filter(Objects::nonNull)
                    .map(Integer::valueOf).distinct().collect(Collectors.toList());
            List<String> createIdList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getCreateId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<String> idList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getId)
                    .collect(Collectors.toList());
            List<String> contractDetailList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getContractDetailId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<String> purchaseIdList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getPurchaseOrderId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());


            List<MaterialDto> materialList = materialServiceProxy.listMaterialByIds(materialIdList);
            List<ProjectDTO> receiveProjectInfoList = projectServiceProxy.getSimpleProjects(receiveProjectList);
            List<SupplierDTO> supplierList = cooperateServiceProxy.findListByIds(query.getCompanyId(), supplierIds);
            List<EmployeeSimpleDTO> employeeDetailDtos = employeeServiceProxy.employeeList(user.getCurrentCompanyId(), createIdList);

            Map<String, String> purchaseOrderMap = new HashMap<>();
            if (CollUtil.isNotEmpty(purchaseIdList)) {
                List<PurchaseOrderVO> purchaseOrderList = materialPurchaseService.getPurchaseOrderList(purchaseIdList);
                if (CollUtil.isNotEmpty(purchaseOrderList)) {
                    purchaseOrderMap = purchaseOrderList.stream().collect(Collectors.toMap(PurchaseOrderVO::getId, PurchaseOrderVO::getOrderNo));
                }
            }

            Map<String, WarningJudgeResultDTO> judgeMap = warningUtil.judge(idList);
            if (CollUtil.isNotEmpty(contractDetailList)) {
                List<SimpleContractDetailDTO> contractorDTOList = materialContractService.querySimpleContractDetails(contractDetailList);
                if (CollUtil.isNotEmpty(contractorDTOList)) {
                    contractDetailDTOMap = contractorDTOList.stream()
                            .collect(Collectors.toMap(SimpleContractDetailDTO::getContractDetailId, e -> e));
                }
            }

            Map<Integer, MaterialDto> materialMap = materialList.stream()
                    .collect(Collectors.toMap(MaterialDto::getMaterialId, obj -> obj));
            Map<Integer, ProjectDTO> receiveProjectInfoMap = receiveProjectInfoList.stream()
                    .collect(Collectors.toMap(ProjectDTO::getProjectId, obj -> obj));
            Map<String, SupplierDTO> supplierMap = supplierList.stream()
                    .collect(Collectors.toMap(obj -> String.valueOf(obj.getId()), obj -> obj));
            Map<String, String> createNameMap = employeeDetailDtos.stream()
                    .collect(Collectors.toMap(EmployeeSimpleDTO::getMemberId, EmployeeSimpleDTO::getMemberName));

            Map<String, SimpleContractDetailDTO> finalContractDetailDTOMap = contractDetailDTOMap;
            Map<String, String> finalPurchaseOrderMap = purchaseOrderMap;
            materialWeighInfoDTOS.getRecords().forEach(e -> {
                MaterialWeighBaseVO vo = new MaterialWeighBaseVO();
                BeanUtils.copyProperties(e, vo);
                vo.setOrderNo(finalPurchaseOrderMap.get(e.getPurchaseOrderId()));
                // 材料类目
                if (e.getMaterialId() != null && CollUtil.isNotEmpty(materialMap)) {
                    MaterialDto materialDto = materialMap.get(e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(materialDto)) {
                        vo.setCategoryName(materialDto.getMaterialCategoryName());
                        vo.setMaterialName(materialDto.getMaterialName() + '/' + materialDto.getMaterialSpec());
                        vo.setMaterialCategoryName(vo.getMaterialName());
                    }
                }
                // 收货项目(收料)/发货单位(发料)
                if (e.getReceiveProject() != null && CollUtil.isNotEmpty(receiveProjectInfoMap)) {
                    ProjectDTO dto = receiveProjectInfoMap.get(e.getReceiveProject());
                    if (ObjectUtil.isNotNull(dto)) {
                        vo.setProjectTitle(dto.getProjectTitle());
                    }
                }
                // 供应商
                if (StrUtil.isNotBlank(e.getSupplierId()) && CollUtil.isNotEmpty(supplierMap)) {
                    SupplierDTO supplier = supplierMap.get(e.getSupplierId());
                    if (ObjectUtil.isNotNull(supplier)) {
                        vo.setSupplierName(supplier.getName());
                    }
                }
                // 称重确认人
                if (StrUtil.isBlank(vo.getReceiver()) && StrUtil.isNotBlank(e.getCreateId()) && CollUtil.isNotEmpty(createNameMap)) {
                    vo.setReceiver(createNameMap.get(e.getCreateId()));
                }
                // 合同信息
                if (CollUtil.isNotEmpty(finalContractDetailDTOMap) && ObjectUtil.isNotNull(finalContractDetailDTOMap.get(e.getContractDetailId()))) {
                    SimpleContractDetailDTO dto = finalContractDetailDTOMap.get(e.getContractDetailId());
                    vo.setDeviationCeiling(dto.getDeviationCeiling());
                    vo.setDeviationFloor(dto.getDeviationFloor());
                }

                WarningJudgeResultDTO judge = judgeMap.get(vo.getId());
                BeanUtils.copyProperties(judge, vo);
                vo.setDeviationStatusStr(DeviationStatusEnum.chooseDeviationStatus(e.getDeviationStatus()));
                vo.setDeviationStatus(vo.getDeviationStatusStr());
                vo.setPushStateStr(e.getIsPushed() == 0 ? "未推送" : e.getIsPushed() == 1 ? "已推送" : "推送失败");
                if (ObjectUtil.isNotNull(e.getIsAddition())) {
                    vo.setIsAdditionStr(e.getIsAddition() == 1 ? "否" : "是");
                }
                if (ObjectUtil.isNotNull(e.getBWeight()) || ObjectUtil.isNotNull(e.getMoistureContent())) {
                    vo.setIsDeductStr("是");
                } else {
                    vo.setIsDeductStr("否");
                }
                if (e.getType().equals(WeighTypeEnum.DELIVERY.value()) && ObjectUtil.isNotNull(e.getTypeDetail())) {
                    vo.setReceiveType(WeighTypeDetailEnum.getWeighTypeDetailDes(e.getTypeDetail()));
                } else if (e.getType().equals(WeighTypeEnum.RECEIVE.value())) {
                    if (StrUtil.isNotBlank(vo.getPurchaseOrderId())) {
                        vo.setReceiveType("按采购单");
                    } else if (StrUtil.isBlank(vo.getPurchaseOrderId()) && StrUtil.isNotBlank(vo.getContractDetailId())) {
                        vo.setReceiveType("按合同");
                    } else {
                        vo.setReceiveType("仅关联供应商");
                    }
                }
                list.add(vo);
            });

            BeanUtils.copyProperties(materialWeighInfoDTOS, materialWeighInfoVOS);
            materialWeighInfoVOS.setRecords(list);
        }
        return materialWeighInfoVOS;
    }

}
