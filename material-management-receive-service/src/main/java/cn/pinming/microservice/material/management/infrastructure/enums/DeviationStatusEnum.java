package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * 偏差状态
 */
public enum DeviationStatusEnum {
    UNIDENTIFIED((byte) 99, "无法确定偏差状态"),
    NORMAL((byte) 0, "正常"),
    NEGATIVEDIFFERENCE((byte) 1, "超负差"),
    POSITIVEDIFFERENCE((byte) 2, "超正差");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    DeviationStatusEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (DeviationStatusEnum statusEnum : DeviationStatusEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }

    public static String chooseDeviationStatus(Byte value){
        if (value == null) return null;
        if(value == 1){
            return NEGATIVEDIFFERENCE.description;
        }else if(value == 2){
            return POSITIVEDIFFERENCE.description;
        }else if(value == 0){
            return NORMAL.description();
        }
        return null;
    }
}
