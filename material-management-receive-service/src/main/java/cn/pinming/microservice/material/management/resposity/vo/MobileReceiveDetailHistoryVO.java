package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MobileReceiveDetailHistoryVO {
    @ApiModelProperty(value = "件数")
    private Integer number;

    @ApiModelProperty(value = "每件数含量")
    private BigDecimal content;

    @ApiModelProperty(value = "是否标准件 1，是；2，否")
    private Byte isStandard;

    @ApiModelProperty(value = "结算合计")
    private BigDecimal settlementTotal;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "智能点数图片")
    private String pointsPic;

    @ApiModelProperty(value = "每\"含量单位\"定尺")
    private BigDecimal lengthPerUnit;

    @ApiModelProperty(value = "实际称重小计")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "实理偏差值")
    private BigDecimal deviationValue;

    @ApiModelProperty(value = "实理偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "理重小计")
    private BigDecimal theoreticalWeight;
}
