package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SimpleWeighBridgeVO {

    @ApiModelProperty(value = "地磅系统名称")
    private String weighSystemName;

    @ApiModelProperty(value = "地磅系统编码")
    private String weighSystemNo;

    @ApiModelProperty(value = "地磅供应商id")
    private Integer weighSupplier;

    @ApiModelProperty(value = "地磅供应商")
    private String weighSupplierName;

    @ApiModelProperty(value = "磅点名称")
    private String weighPointName;

    @ApiModelProperty(value = "数据上传方式, 1 IOT上传 2 接口上传 ")
    private Byte uploadType;
}
