package cn.pinming.microservice.material.management.infrastructure.enums;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum WeighTypeEnum {

    RECEIVE((byte)1, "收料"),
    DELIVERY((byte)2, "发料"),
    OTHER((byte)4,"其他模式");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WeighTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String getWeighTypeDes(Byte key) {
        List<WeighTypeEnum> weighTypeEnumList = Arrays.stream(WeighTypeEnum.values())
                .filter(type -> type.value() == key).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(weighTypeEnumList)) {
            return weighTypeEnumList.get(0).description();
        }
        return null;
    }
}
