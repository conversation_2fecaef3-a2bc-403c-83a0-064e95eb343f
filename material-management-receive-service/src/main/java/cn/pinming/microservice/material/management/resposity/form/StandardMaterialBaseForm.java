package cn.pinming.microservice.material.management.resposity.form;

import cn.pinming.microservice.material.management.infrastructure.config.LocalDateTimeDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 称重磅单标准form 只保留磅单数据
 * <AUTHOR>
 */
@Data
public class StandardMaterialBaseForm implements Serializable {
    @ApiModelProperty("外部系统单号")
    private String extNo;

    @ApiModelProperty("单据类型")
    private String type;

    @ApiModelProperty("车牌号")
    private String tNo;

    @ApiModelProperty("称重记录UUID")
    private String tUuid;

    @ApiModelProperty("单据流水号")
    private String sNo;

    @ApiModelProperty("毛重时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gTime;

    @ApiModelProperty("皮重时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tTime;

    @ApiModelProperty("采购单号")
    private String purNo;

    @ApiModelProperty("企业id")
    private Integer coId;

    @ApiModelProperty("项目id")
    private Integer pjId;

    @ApiModelProperty("毛重")
    private BigDecimal gWeight;

    @ApiModelProperty("皮重")
    private BigDecimal tWeight;

    @ApiModelProperty("扣重")
    private BigDecimal bWeight;

    @ApiModelProperty("净重")
    private BigDecimal nWeight;
}
