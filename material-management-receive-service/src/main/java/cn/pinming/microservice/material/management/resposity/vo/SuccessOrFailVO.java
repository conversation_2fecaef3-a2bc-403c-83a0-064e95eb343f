package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SuccessOrFailVO {
//    @ApiModelProperty(value = "发料新增成功数")
//    private Integer sendAddSuccess = 0;
//    @ApiModelProperty(value = "发料新增失败数")
//    private Integer sendAddFail = 0;
//    @ApiModelProperty(value = "发料更新成功数")
//    private Integer sendUpdateSuccess = 0;
//    @ApiModelProperty(value = "发料更新失败数")
//    private Integer sendUpdateFail = 0;
//    @ApiModelProperty(value = "收料新增成功数")
//    private Integer receiveAddSuccess = 0;
//    @ApiModelProperty(value = "收料新增失败数")
//    private Integer receiveAddFail = 0;
    @ApiModelProperty(value = "收料更新成功数")
    private Integer receiveUpdateSuccess = 0;
    @ApiModelProperty(value = "收料更新失败数")
    private Integer receiveUpdateFail = 0;

    @ApiModelProperty(value = "错误信息")
    private Map<String,String> errorMap;

//    public void sendAddSuccess() {++sendAddSuccess;}
//    public void sendAddFail() {++sendAddFail;}
//    public void sendUpdateSuccess() {++sendUpdateSuccess;}
//    public void sendUpdateFail() {++sendUpdateFail;}
//    public void receiveAddSuccess() {++receiveAddSuccess;}
//    public void receiveAddFail() {++receiveAddFail;}
    public void receiveUpdateSuccess() {++receiveUpdateSuccess;}
    public void receiveUpdateFail() {++receiveUpdateFail;}

}
