package cn.pinming.microservice.material.management.infrastructure.proxy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.infrastructure.proxy.MaterialServiceExtProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/3 2:16 下午
 */
@Slf4j
@Component
public class MaterialServiceExtProxyImpl implements MaterialServiceExtProxy {

    @DubboReference
    private MaterialService materialService;

    @Override
    public List<MaterialCategoryDto> listCategoryByIds(Integer companyId, Collection<Integer> categoryIds) {
        return materialService.listCategoryByIds(companyId, categoryIds);
    }

    @Override
    public List<MaterialDto> listMaterialByIds(@NonNull Collection<Integer> materialIds) {
        List<MaterialDto> list = materialService.listMaterialByIds(materialIds);
        if (list == null) {
            return new ArrayList<>(0);
        }
        return list;
    }


    @Override
    public Map<Integer, String> materialSpec(Set<Integer> materialIds) {
        Map<Integer, String> map = new HashMap<>();

        List<MaterialDto> materialDtos = Lists.newArrayList();
        Set<Integer> materialSet = materialIds.stream().filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(materialSet)) {
            materialDtos = listMaterialByIds(materialSet);
        }

        for (Integer materialId : materialIds) {
            if (materialId != null) {
                Optional<MaterialDto> any = materialDtos.stream().filter(item -> materialId.intValue() == item.getMaterialId()).findAny();
                if (any.isPresent()) {
                    MaterialDto materialDto = any.get();
                    map.put(materialId, StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                } else {
                    map.put(materialId, "--");
                }
            } else {
                map.put(materialId, "--");
            }
        }
        return map;
    }


    @Override
    public List<Integer> getMaterialIdsByCategoryId(Integer companyId, Integer categoryId) {
        List<MaterialDto> materialDtos = materialService.listAllMaterialByCategoryId(companyId, categoryId);
        assert materialDtos != null;
        return materialDtos.stream().map(MaterialDto::getMaterialId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getMaterialIdsByCategoryIds(Integer companyId, List<Integer> categoryIds) {
        List<MaterialDto> materialDtos = materialService.listAllMaterialByCategoryIds(companyId, categoryIds);
        assert materialDtos != null;
        return materialDtos.stream().map(MaterialDto::getMaterialId)
                .collect(Collectors.toList());
    }

}
