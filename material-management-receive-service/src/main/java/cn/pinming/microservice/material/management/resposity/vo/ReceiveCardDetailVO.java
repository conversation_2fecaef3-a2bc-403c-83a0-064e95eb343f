package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReceiveCardDetailVO extends SimpleSupplierDTO {
    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "收料类型")
    private Byte receiveType;

    @ApiModelProperty(value = "收货单编号")
    private String receiveNo;

    @ApiModelProperty(value = "进场状态code")
    private Byte receiveStatus;

    @ApiModelProperty(value = "实际收货人")
    private String receiver;

    @ApiModelProperty(value = "实际收货时间")
    private String createTime;

    @ApiModelProperty(value = "验收意见")
    private String comment;

    @ApiModelProperty(value = "车牌号码")
    private String truckNo;

    @ApiModelProperty(value = "车辆到场时间")
    private String truckTime;

    @ApiModelProperty(value = "车头车尾图片")
    private String truckPic;

    @ApiModelProperty(value = "货/铭牌图片")
    private String goodsPic;

    @ApiModelProperty(value = "送货单图片")
    private String sendPic;

    @ApiModelProperty(value = "智能点数图片")
    private String pointsPic;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "供应商ID")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商")
    private String supplierTitle;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "收货项目id")
    private Integer receiverProject;

    @ApiModelProperty(value = "收货项目")
    private String receiverProjectTitle;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "下单人id")
    private String createId;

    @ApiModelProperty(value = "下单人名字")
    private String createName;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty(value = "收货材料列表")
    private List<ReceiveCardMaterialDetailVO> list;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "地址详情")
    private String location;

}
