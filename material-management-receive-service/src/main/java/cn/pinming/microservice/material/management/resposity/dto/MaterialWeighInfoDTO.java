package cn.pinming.microservice.material.management.resposity.dto;



import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MaterialWeighInfoDTO {

    @ApiModelProperty(value = "明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收料类型")
    private String receiveType;

    @ApiModelProperty(value = "收货项目id")
    private Integer receiveProject;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称(无材料id时使用)")
    private String materialName;

    @ApiModelProperty(value = "面单应收数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称(收料)/收货单位(发料)")
    private String supplierName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货人")
    private String receiver;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty("毛重")
    private BigDecimal gWeight;

    @ApiModelProperty("皮重")
    private BigDecimal tWeight;

    @ApiModelProperty("扣重")
    private BigDecimal bWeight;

    @ApiModelProperty("净重")
    private BigDecimal nWeight;

    @ApiModelProperty(value = "实重(吨)")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "类型，1：收货；2：发货")
    private Byte type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "创建者id")
    private String createId;

    @ApiModelProperty(value = "推送状态 0.未推送\n" +
            "1.已推送\n" +
            "2.推送失败")
    private Byte isPushed;

    @ApiModelProperty(value = "采购单id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "对账id")
    private String reconciliationId;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "偏差量")
    private BigDecimal deviationCount;

    @ApiModelProperty(value = "进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "收料确认方式 1 扫码组装 2 司机自助确认")
    private Integer confirmType;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Integer isAddition;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "终端称重记录id")
    private String weighId;
}
