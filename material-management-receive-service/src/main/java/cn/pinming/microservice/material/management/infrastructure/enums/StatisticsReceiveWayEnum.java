package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
public enum StatisticsReceiveWayEnum {

    POUNDBILL_TEMPORARY(ReceiveModeEnum.TEMPORARY.value(), "地磅收料-临时收料"),
    POUNDBILL_REPORT(ReceiveModeEnum.REPORT.value(), "地磅收料-报备收料"),
    POUNDBILL_UNBELOGN(ReceiveModeEnum.UNBELOGN.value(), "地磅收料-无归属收料"),
    MOBILE_CONTRACT(ReceiveTypeEnum.CONTRACT.value(), "移动收料-按合同"),
    MOBILE_PURCHASE(ReceiveTypeEnum.PURCHASE.value(), "移动收料-按采购单"),
    MOBILE_NONE(ReceiveTypeEnum.NONE.value(), "移动收料-无合同");

    private final byte code;

    private final String desc;

    StatisticsReceiveWayEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public byte getCode() {
        return code;
    }
}
