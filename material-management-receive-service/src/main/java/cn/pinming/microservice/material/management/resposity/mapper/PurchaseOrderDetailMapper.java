package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.dto.PurchaseOrderDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.PurchaseOrderDetail;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.GoodsForReviseVO;
import cn.pinming.microservice.material.management.resposity.vo.GoodsSimpleVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface PurchaseOrderDetailMapper extends BaseMapper<PurchaseOrderDetail> {

    List<GoodsSimpleVO> selectMaterialInfo(@Param("orderId") String orderId);

    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseOrderDetailDTO> selectOrderDetailById(@Param("id") String id);

    BigDecimal selectPurchaseAmountByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailPageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailMobilePageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailUnionPageVO(SupplierAnalysisQuery query);

    IPage<SupplierAnalysisDetailVO> selectSupplierPageVO(SupplierAnalysisQuery query);

    List<GoodsForReviseVO> listForRevise(@Param("purchaseOrderId") String purchaseOrderId);

    List<String> queryHistoryUsePartByProjectId(@Param("projectId") Integer projectId, @Param("remark") String remark);
}
