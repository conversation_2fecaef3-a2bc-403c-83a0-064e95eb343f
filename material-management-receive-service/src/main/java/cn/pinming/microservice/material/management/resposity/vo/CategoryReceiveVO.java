package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/9 9:47 下午
 */
@Data
public class CategoryReceiveVO {

    @ApiModelProperty("二级分类id")
    private Integer categoryId;

    @ApiModelProperty("二级分类名称")
    private String categoryName;

    @ApiModelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty("面单应收数量")
    private BigDecimal weightSend;
}
