package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialVerifyStatisticsVO extends WeightBaseVO{
    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "二级分类名称")
    private String categoryName;

    @ApiModelProperty(value = "品种及规格型号")
    private String materialName;

    @ApiModelProperty(value = "采购量")
    private BigDecimal purchaseTotal;
}
