package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.weaponx.wrapper.dto.SimpleProjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地磅信息VO
 *
 * <AUTHOR>
 */
@Data
public class MaterialWeighbridgeVO extends SimpleProjectDTO implements Serializable{

    @ApiModelProperty(value = "所属组织")
    private Integer departmentId;

    @ApiModelProperty(value = "所属组织名称")
    private String departmentName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "地磅状态/项目状态")
    private Byte status;

    @ApiModelProperty(value = "地磅数量")
    private Integer weighbridgeCount;

    @ApiModelProperty(value = "项目外部代码")
    private String extCode;

    @ApiModelProperty(value = "地磅id")
    private String id;

    @ApiModelProperty(value = "地磅系统名称")
    private String weighSystemName;

    @ApiModelProperty(value = "地磅系统编码")
    private String weighSystemNo;

    @ApiModelProperty(value = "地磅供应商id")
    private Integer weighSupplier;

    @ApiModelProperty(value = "地磅供应商")
    private String weighSupplierName;

    @ApiModelProperty(value = "数据上传方式, 1 IOT上传 2 接口上传 ")
    private Byte uploadType;

    @ApiModelProperty(value = "扣重是否使用绝对值(1 是 2 否) (不使用则是百分比)")
    private Byte deductSet;

    @ApiModelProperty(value = "磅点数")
    private Integer weighCount;

    @ApiModelProperty(value = "是否在线, 该项目下有未在线的终端时显示为 否 ")
    private Boolean isOnline;

    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;



}
