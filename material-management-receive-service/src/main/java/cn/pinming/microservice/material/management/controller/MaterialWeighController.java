package cn.pinming.microservice.material.management.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.common.response.SingleResponse;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.WeighDataConfirmDetailDTO;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.management.dto.SdkConfigDTO;
import cn.pinming.microservice.base.management.service.ISdkConfService;
import cn.pinming.microservice.material.management.infrastructure.annotation.Log;
import cn.pinming.microservice.material.management.infrastructure.annotation.enums.BusinessType;
import cn.pinming.microservice.material.management.infrastructure.enums.UserConfigEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.listener.WeighDataListener;
import cn.pinming.microservice.material.management.infrastructure.util.ExcelUtils;
import cn.pinming.microservice.material.management.infrastructure.util.WeighDuplicationUtil;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.entity.UserConfig;
import cn.pinming.microservice.material.management.resposity.form.MobileMaterialBatchForm;
import cn.pinming.microservice.material.management.resposity.form.SDKStandardMaterialForm;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.service.IUserConfigService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 过磅数据 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "地磅收货-controller", value = "zh")
@RestController
@RequestMapping("/api/weight")
@Slf4j
public class MaterialWeighController {
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private UserUtil userUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private IUserConfigService userConfigService;
    @DubboReference
    private ISdkConfService sdkConfService;
    @Resource
    private WeighDuplicationUtil weighDuplicationUtil;
    @Resource
    private WeighDataListener weighDataListener;

    @ApiOperation(value = "数据重组")
    @PostMapping("/sdk")
    public ResponseEntity<Response> sdk(@RequestBody WeighDataAssembleForm form) {
        weighDuplicationUtil.judge(Arrays.asList(form.getFirst(),form.getSecond()));
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(userUtil.getCompanyId(), userUtil.getProjectId());
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        WeighDataAssemble weighDataAssemble = materialClient.weighDataAssemble(form);
        return ResponseEntity.ok(new SuccessResponse(weighDataAssemble));
    }

    @Log(title = "SDK保存收料记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "SDK保存收料记录")
    @PostMapping("/sdkSave")
    public ResponseEntity<Response> sdkSave(@RequestBody SDKStandardMaterialForm form) {
        String id = materialDataService.saveSdk(form);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "SDK收料记录历史")
    @GetMapping("/sdkHistory/{type}")
    public ResponseEntity<Response> sdkHistory(@PathVariable("type")Byte type) {
        SDKHistoryVO vo = materialDataService.sdkHistory(type);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "地磅收货列表")
    @PostMapping("/showInfo")
    public ResponseEntity<Response> showWeightInfo(@RequestBody MaterialWeighQuery query) {
        IPage<MaterialWeighBaseVO> page = materialSendReceiveService.showWeightInfo(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "地磅收货明细", response = WeighReceiveVO.class)
    @PostMapping("/showDetail")
    public ResponseEntity<Response> showWeightDetail(@RequestBody MaterialWeighQuery query) {
        WeighReceiveVO weighReceiveVO = materialDataService.showWeightDetail(query);
        return ResponseEntity.ok(new SuccessResponse(weighReceiveVO));
    }

    @ApiOperation(value = "确认单详情")
    @GetMapping("/confirmDetail/{id}")
    public SingleResponse<WeighDataConfirmDetailDTO> confirmDetail(@PathVariable("id")String id) {
        WeighDataConfirmDetailDTO vo = materialDataService.confirmDetail(id);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "导出收料记录")
    @PostMapping("/export/receive")
    public void receiveExport(HttpServletResponse response, @RequestBody MaterialWeighQuery query){
        query.setPages(0);
        query.setSize(Integer.MAX_VALUE);
        AuthUser user = userUtil.getUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<MaterialWeighBaseVO> page = materialSendReceiveService.showWeightInfo(query);
        LocalDate now = LocalDate.now();
        String date = LocalDateTimeUtil.format(now, "yyyyMMdd");

        Set<String> includeColumnFiledNames = new HashSet<>();
        UserConfig userConfig = null;
        if (ObjectUtil.isNotNull(user.getCurrentProjectId())) {
            userConfig = userConfigService.lambdaQuery()
                    .eq(UserConfig::getCompanyId, user.getCurrentCompanyId())
                    .eq(UserConfig::getProjectId, user.getCurrentProjectId())
                    .eq(UserConfig::getCreateId, user.getId())
                    .eq(UserConfig::getType, UserConfigEnum.ONE.value())
                    .one();
        }else {
            userConfig = userConfigService.lambdaQuery()
                    .eq(UserConfig::getCompanyId, user.getCurrentCompanyId())
                    .eq(UserConfig::getCreateId, user.getId())
                    .isNull(UserConfig::getProjectId)
                    .eq(UserConfig::getType, UserConfigEnum.ONE.value())
                    .one();
        }

        if (ObjectUtil.isNotNull(userConfig)) {
            includeColumnFiledNames = StrUtil.split(userConfig.getCollection(),",").stream().collect(Collectors.toCollection(LinkedHashSet::new));
        }
        ExcelUtils.export(response, page.getRecords(), MaterialWeighBaseVO.class, "收料记录导出" + date,includeColumnFiledNames);
    }

    @ApiOperation(value = "导出发料记录")
    @PostMapping("/export/send")
    public void sendExport(HttpServletResponse response, @RequestBody MaterialWeighQuery query){
        query.setPages(0);
        query.setSize(Integer.MAX_VALUE);
        Integer companyId = userUtil.getCompanyId();
        query.setCompanyId(companyId);
        if (userUtil.getDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(userUtil.getCompanyId(), userUtil.getDepartmentId()));
        }
        query.setProjectId(userUtil.getProjectId() == null ? query.getProjectId() : userUtil.getProjectId());
        IPage<MaterialWeighBaseVO> page = materialSendReceiveService.showWeightInfo(query);
        List<MaterialWeighReceiveExportVO> receiveResult = new ArrayList<>();
        List<MaterialWeighSendExportVO> sendResult = new ArrayList<>();
        if (CollUtil.isNotEmpty(page.getRecords()) && query.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            receiveResult = page.getRecords().stream().map(e -> {
                MaterialWeighReceiveExportVO vo = new MaterialWeighReceiveExportVO();
                BeanUtils.copyProperties(e, vo);
                return vo;
            }).collect(Collectors.toList());
        }else if (CollUtil.isNotEmpty(page.getRecords()) && query.getType().equals(WeighTypeEnum.DELIVERY.value())) {
            sendResult = page.getRecords().stream().map(e -> {
                MaterialWeighSendExportVO vo = new MaterialWeighSendExportVO();
                BeanUtils.copyProperties(e, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        LocalDate now = LocalDate.now();
        String date = LocalDateTimeUtil.format(now, "yyyyMMdd");
        if (query.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            ExcelUtils.export(response, receiveResult, MaterialWeighReceiveExportVO.class, "收料记录导出" + date);
        }else  {
            ExcelUtils.export(response, sendResult, MaterialWeighSendExportVO.class, "发料记录导出" + date);
        }
    }

    @ApiOperation(value = "刷新过磅照片")
    @GetMapping("/refreshPic/{id}")
    public ResponseEntity<Response> refreshPic(@PathVariable("id")String id) {
        // TODO: 2024/8/15 等接入了项目设置再接基石的鉴权
        materialDataService.refreshPic(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "批量收料提交/修订")
    @PostMapping("/batch")
    public ResponseEntity<Response> batch(@RequestBody @NotEmpty(message = "数据上传列表为空") List<MobileMaterialBatchForm> list) {
        if (list.size() > 200) {throw new BOException(BOExceptionEnum.OUTSIZE);}
        SuccessOrFailVO vo = materialDataService.batch(list);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "删除过磅记录")
    @GetMapping("/delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable("id")String id) {
        materialDataService.lambdaUpdate()
                .eq(MaterialData::getId,id)
                .set(MaterialData::getIsDeleted,1)
                .update();

        // 内部推送
        weighDataListener.AfterCompletion(id,null,1);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "批量修改外部系统单号")
    @PostMapping("/batchUpdate")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Response> batchUpdate(@RequestBody Map<String,String> map) {
        List<MaterialSendReceive> list = new ArrayList<>();
        List<String> receiveIdList = new ArrayList<>();
        map.forEach((k,v) -> {
            MaterialSendReceive materialSendReceive = new MaterialSendReceive();
            materialSendReceive.setId(k);
            materialSendReceive.setExtNo(v);
            list.add(materialSendReceive);
            receiveIdList.add(k);
        });
        materialSendReceiveService.updateBatchById(list);
        materialDataService.lambdaUpdate()
                .in(MaterialData::getReceiveId,receiveIdList)
                .set(MaterialData::getPushState,0)
                .update();

        // 内部推送
        receiveIdList.forEach(e -> {
            weighDataListener.AfterCompletion(null,e,1);
        });
        return ResponseEntity.ok(new SuccessResponse());
    }
}
