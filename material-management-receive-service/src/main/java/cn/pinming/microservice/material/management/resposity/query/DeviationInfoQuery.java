package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class DeviationInfoQuery extends BaseQuery{
    @ApiModelProperty("组织顶点")
    @NotNull(message = "组织顶点不能为空")
    private Integer point;

    @ApiModelProperty("项目范围")
    private List<Integer> projectIdList;

    @ApiModelProperty(value = "收货开始时间")
    private LocalDate startDate;

    @ApiModelProperty(value = "收货结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "计量单位")
    @NotBlank(message = "请选择单位")
    private String unit;

    @ApiModelProperty(value = "材料二级id列表")
    private List<Integer> categoryIds;

    @ApiModelProperty(value = "收料方式")
    private Byte receiveType;

    @ApiModelProperty(value = "要货开始时间")
    private LocalDate receiveStartDate;

    @ApiModelProperty(value = "要货结束时间")
    private LocalDate receiveEndDate;
}
