package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ReceiveCardMaterialDetailVO {
    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "totalId")
    private String totalId;

    @ApiModelProperty(value = "材料全称")
    private String materialName;

    @ApiModelProperty(value = "二级材料名称/物资名称")
    private String materialCategoryName;

    @ApiModelProperty(value = "品种名称")
    private String materialType;

    @ApiModelProperty(value = "规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "采购单品牌")
    private String purchaseBrand;

    @ApiModelProperty(value = "采购单备注")
    private String remark;

    @ApiModelProperty(value = "到场品牌,多个以,分隔")
    private String brand;

    @ApiModelProperty(value = "实际点验总件数")
    private Integer actualNumber;

    @ApiModelProperty(value = "实际点验总含量")
    private BigDecimal actualContent;

    @ApiModelProperty(value = "含量单位")
    private String unit;

    @ApiModelProperty(value = "实际点验结算含量")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "结算单位")
    private String settlementUnit;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty(value = "面单应收总件数")
    private Integer sendNumber;

    @ApiModelProperty(value = "面单应收总含量")
    private BigDecimal sendContent;

    @ApiModelProperty(value = "面单应收结算含量")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "面单应收总含量单位")
    private String sendUnit;

    @ApiModelProperty(value = "面单应收结算含量单位")
    private String sendSettlementUnit;

    @ApiModelProperty(value = "实重合计")
    private BigDecimal actualWeightTotal;

    @ApiModelProperty(value = "理重合计")
    private BigDecimal theoreticalWeightTotal;

    @ApiModelProperty(value = "实理偏差值")
    private BigDecimal deviationValueTotal;

    @ApiModelProperty(value = "实理偏差率")
    private BigDecimal deviationRateTotal;

    @ApiModelProperty(value = "下单量")
    private BigDecimal count;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "状态快照 1,实重 2,理重 null,无理重展示")
    private Byte kind;
}
