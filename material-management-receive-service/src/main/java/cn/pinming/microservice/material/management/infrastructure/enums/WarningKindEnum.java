package cn.pinming.microservice.material.management.infrastructure.enums;

public enum WarningKindEnum {
    ISINTEGRITY((byte)1,"数据完整性"),
    ISNORMATIVE((byte)2 ,"业务规范性");


    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WarningKindEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (WarningKindEnum statusEnum : WarningKindEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
