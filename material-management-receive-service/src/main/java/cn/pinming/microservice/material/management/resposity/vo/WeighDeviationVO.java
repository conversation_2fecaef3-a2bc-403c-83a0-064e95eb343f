package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 地磅收货偏差VO
 *
 * <AUTHOR>
 */
@Data
public class WeighDeviationVO extends SimpleSupplierDTO {

    @ApiModelProperty(value = "累计采购下单量")
    private BigDecimal purchaseSum;

    @ApiModelProperty(value = "累计面单应收量")
    private BigDecimal sendSum;

    @ApiModelProperty(value = "累计实际量")
    private BigDecimal receiveSum;

    @ApiModelProperty(value = "累计偏差量")
    private BigDecimal deviationSum;

    @ApiModelProperty(value = "统计量单位")
    private Byte unit;

    @ApiModelProperty(value = "累计偏差比例")
    private BigDecimal deviationRateSum;

    @ApiModelProperty(value = "累计实收数量")
    private BigDecimal actualReceive;


}
