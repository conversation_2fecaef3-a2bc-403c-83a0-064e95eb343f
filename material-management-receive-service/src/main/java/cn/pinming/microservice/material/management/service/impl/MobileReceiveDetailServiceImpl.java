package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.common.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.base.common.wrapper.SupplierWrapper;
import cn.pinming.microservice.material.management.infrastructure.enums.ApproachStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.MaterialReviseEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.MobileReceiveTypeEnum;
import cn.pinming.microservice.material.management.resposity.dto.MobileGoodsCardDTO;
import cn.pinming.microservice.material.management.resposity.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveDetail;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveDetailMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.resposity.query.MobileCardQuery;
import cn.pinming.microservice.material.management.resposity.vo.MobileGoodsCardVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveCardVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVerifyVO;
import cn.pinming.microservice.material.management.service.IMobileReceiveDetailService;
import cn.pinming.microservice.material.management.service.IMobileReceiveTotalService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 移动收料细节表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:23
 */
@Service
public class MobileReceiveDetailServiceImpl extends ServiceImpl<MobileReceiveDetailMapper, MobileReceiveDetail> implements IMobileReceiveDetailService {
    @Resource
    private IMobileReceiveTotalService totalService;
    @Resource
    private MobileReceiveMapper receiveMapper;
    @Resource
    private MobileReceiveDetailMapper detailMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private UserUtil userUtil;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private ProjectNameWrapper projectTitleWrapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    /**
     * 各材料收货记录录入
     *
     * @param list, receiveId, type
     */
    @Override
    public void add(List<MobileReceiveMaterialForm> list, String receiveId, SimpleTransformDTO simpleTransformDTO) {
        list.forEach(e -> {
            String totalUuid = IdUtil.simpleUUID();
            BeanUtils.copyProperties(e, simpleTransformDTO);
            simpleTransformDTO.setReceiveId(receiveId);
            simpleTransformDTO.setTotalId(totalUuid);
            Byte type = simpleTransformDTO.getType();

            if (type != null && type == 1) {
                // 简易版本
                MobileReceiveDetail detail = new MobileReceiveDetail();
                simpleTransform(detail, simpleTransformDTO);
                this.save(detail);
            } else {
                e.getDetailList().forEach(m -> {
                    MobileReceiveDetail detail = new MobileReceiveDetail();

                    BeanUtils.copyProperties(m, detail);
                    if (CollUtil.isNotEmpty(m.getPointsPic())) {
                        fileServiceProxy.confirmFiles(m.getPointsPic());
                        detail.setPointsPic(String.join(",", m.getPointsPic()));
                    }
                    detail.setMaterialId(e.getMaterialId());
                    detail.setReceiveId(receiveId);
                    // 结算合计 = 件数 * 每件含量 * 换算系数
                    double d = m.getNumber() * (m.getContent().multiply(m.getRatio()).doubleValue());
                    detail.setSettlementTotal(BigDecimal.valueOf(d));
                    detail.setTotalId(totalUuid);
                    //  各材料收料信息细节录入
                    this.save(detail);
                });
            }

            totalService.add(e, simpleTransformDTO);
        });
    }

    /**
     * 简易对象处理
     *
     * @param detail
     * @param dto
     */
    private void simpleTransform(MobileReceiveDetail detail, SimpleTransformDTO dto) {
        detail.setReceiveId(dto.getReceiveId());
        detail.setMaterialId(dto.getMaterialId());
        detail.setTotalId(dto.getTotalId());
        detail.setIsStandard((byte) 1);
        detail.setNumber(1);
        detail.setContent(dto.getActualSettlementTotal());
        detail.setUnit(dto.getSettlementUnit());
        detail.setRatio(BigDecimal.ONE);
        detail.setSettlementUnit(dto.getSettlementUnit());
        detail.setSettlementTotal(dto.getActualSettlementTotal());
    }

    /**
     * 移动收料单卡片列表页
     *
     * @param query
     * @return
     */
    @Override
    public IPage<MobileReceiveCardVO> selectPage(MobileCardQuery query) {
        IPage<MobileReceiveCardVO> page = new Page<>();

        // 根据条件找出所有收货单
        IPage<MobileReceiveCardVO> totalPage = receiveMapper.selectReceivePage(query);
        if (CollectionUtil.isNotEmpty(totalPage.getRecords())) {
            List<MobileReceiveCardVO> totalList = totalPage.getRecords();

            List<MobileReceiveCardVO> listWithSupplierId = totalList.stream().filter(e -> e.getSupplierId() != null).collect(Collectors.toList());
            supplierWrapper.wrap(listWithSupplierId, userUtil.getCompanyId());
            projectTitleWrapper.wrap(totalList);

            List<String> receiveIdList = totalList.stream().map(MobileReceiveCardVO::getId).collect(Collectors.toList());
            List<MobileGoodsCardDTO> materialList = detailMapper.selectReceiveDetail(receiveIdList);
            if (CollectionUtil.isEmpty(materialList)) {
                return null;
            }

//          收货单id -> 收货单材料收货信息
            Map<String, List<MobileGoodsCardDTO>> map = materialList.stream().collect(Collectors.groupingBy(MobileGoodsCardDTO::getReceiveId));

            totalList.forEach(m -> {
                List<MobileGoodsCardVO> goodsCardVOS = new ArrayList<>();

//              某一个收货单下的材料收货信息, 因为无采购收货，有些材料的偏差状态可能为空
                List<MobileGoodsCardDTO> goodsCardDTOList = map.get(m.getId());
                if (CollectionUtil.isNotEmpty(goodsCardDTOList)) {
                    List<Integer> materialIds = goodsCardDTOList.stream().map(MobileGoodsCardDTO::getMaterialId).distinct().collect(Collectors.toList());
                    List<MaterialDto> materialsInfo = materialServiceProxy.listMaterialByIds(materialIds);
                    Map<Integer, MaterialDto> materialDtoMap = materialsInfo.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));

                    if (CollUtil.isNotEmpty(materialsInfo)) {
                        List<String> category = materialsInfo.stream().map(x -> StrUtil.format("{}", x.getMaterialCategoryName())).collect(Collectors.toList());
                        String result = String.join(",", category);
                        m.setCategory(result);
                    }

                    goodsCardDTOList.forEach(n -> {
                        MobileGoodsCardVO goodsCardVO = new MobileGoodsCardVO();
                        BeanUtils.copyProperties(n, goodsCardVO);
                        MaterialDto materialDto = materialDtoMap.get(n.getMaterialId());
                        if (ObjectUtil.isNotEmpty(materialDto)) {
                            String str = StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec());
                            goodsCardVO.setMaterialName(str);
                        }
                        goodsCardVOS.add(goodsCardVO);
                    });
                    m.setList(goodsCardVOS);
                }

                if (m.getDeviationStatus() != null) {
                    m.setDeviationStatusStr(DeviationStatusEnum.desc(m.getDeviationStatus()));
                }
                if (m.getReceiveType() != null) {
                    m.setReceiveTypeStr(MobileReceiveTypeEnum.desc(m.getReceiveType()));
                }
                m.setReceiveStatusStr(ApproachStatusEnum.desc(m.getReceiveStatus()));
                m.setIsReviseStr(MaterialReviseEnum.desc(m.getIsRevise()));
                m.setProjectName(m.getProjectTitle());
            });

            BeanUtils.copyProperties(totalPage, page);
            page.setRecords(totalList);
        }
        return page;
    }

    /**
     * 移动收料单卡片列表页
     *
     * @param query
     * @return
     */
    @Override
    public List<MobileReceiveVerifyVO> selectPageForVerify(MobileCardQuery query) {
        query.setPages(0);
        query.setSize(Long.MAX_VALUE);
        Integer companyId = userUtil.getCompanyId();
        query.setCompanyId(companyId);
        if (userUtil.getDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(userUtil.getCompanyId(), userUtil.getDepartmentId()));
        }
        query.setProjectId(userUtil.getProjectId() == null ? query.getProjectId() : userUtil.getProjectId());
        List<MobileReceiveVerifyVO> list = receiveMapper.selectPageForVerify(query);
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> materialIdList = list.stream().map(MobileReceiveVerifyVO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);

            Map<Integer, MaterialDto> materialMap = new HashMap<>();
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }
            Map<Integer, MaterialDto> finalMaterialMap = materialMap;
            list.forEach(e -> {
                        e.setDeviationStatusStr(DeviationStatusEnum.chooseDeviationStatus(e.getDeviationStatus()));
                        if (CollUtil.isNotEmpty(finalMaterialMap) && ObjectUtil.isNotNull(finalMaterialMap.get(e.getMaterialId()))) {
                            MaterialDto materialDto = finalMaterialMap.get(e.getMaterialId());
                            e.setCategoryName(materialDto.getMaterialCategoryName());
                            e.setMaterialName(materialDto.getMaterialName() + '/' + materialDto.getMaterialSpec());
                        }
                    }
            );
        }
        return list;
    }
}
