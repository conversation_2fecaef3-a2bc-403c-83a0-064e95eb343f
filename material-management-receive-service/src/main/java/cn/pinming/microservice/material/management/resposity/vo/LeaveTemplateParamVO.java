package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模板参数列表VO
 * <AUTHOR>
 * @since 2022/6/30 17:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeaveTemplateParamVO {

    @ApiModelProperty(value = "模板参数")
    private String param;

    @ApiModelProperty(value = "介绍")
    private String description;
}
