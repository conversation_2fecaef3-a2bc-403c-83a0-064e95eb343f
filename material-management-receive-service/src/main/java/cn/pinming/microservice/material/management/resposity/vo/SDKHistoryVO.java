package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SDKHistoryVO {
    @ApiModelProperty(value = "原记录-合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "原记录-材料名称")
    private String materialName;

    @ApiModelProperty(value = "原记录-材料规格名称")
    private String materialSpec;

    @ApiModelProperty(value = "原记录-材料二级分类名称")
    private String categoryName;

    @ApiModelProperty(value = "原记录-材料id")
    private Integer materialId;

    @ApiModelProperty(value = "原记录-材料二级分类id")
    private Integer categoryId;

    @ApiModelProperty(value = "原记录-结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "原记录-换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "原记录-供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "原记录-供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同-约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "合同-约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "合同-结算单位")
    private String contractUnit;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同-换算系数")
    private BigDecimal contractRatio;

    @ApiModelProperty(value = "是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    private Long wbsId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty("领用单位")
    private String receiveUnit;

    @ApiModelProperty(value = "卸料点")
    private String dischargePoint;
}
