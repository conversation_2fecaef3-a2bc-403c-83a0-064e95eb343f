package cn.pinming.microservice.material.management.infrastructure.config;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
@Slf4j
public class AuditMetaObjectHandler implements MetaObjectHandler {

    @Resource
    private AuthUserHolder siteContextHolder;

    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String CREATE_ID = "createId";
    private static final String CREATE_BY = "createBy";
    private static final String UPDATE_ID = "updateId";
    private static final String COMPANY_ID = "companyId";
    private static final String DEPARTMENT_ID = "departmentId";
    private static final String PROJECT_ID = "projectId";

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        if (metaObject.hasGetter(CREATE_TIME)) {
            this.setFieldValByName(CREATE_TIME, localDateNow, metaObject);
        }
        if (metaObject.hasGetter(UPDATE_TIME)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        AuthUser currentUser = siteContextHolder.getCurrentUser();
        if (currentUser != null) {
            String id = currentUser.getId();
            String memberName = currentUser.getMemberName();
            Integer companyId = currentUser.getCurrentCompanyId();
            Integer departmentId = currentUser.getCurrentDepartmentId();
            Integer projectId = currentUser.getCurrentProjectId();
            if (metaObject.hasGetter(CREATE_ID)) {
                this.setFieldValByName(CREATE_ID, id, metaObject);
            }
            if (metaObject.hasGetter(CREATE_BY)) {
                this.setFieldValByName(CREATE_BY, memberName, metaObject);
            }
            if (metaObject.hasGetter(UPDATE_ID)) {
                this.setFieldValByName(UPDATE_ID, id, metaObject);
            }
            if (metaObject.hasGetter(COMPANY_ID)) {
                this.setFieldValByName(COMPANY_ID, companyId, metaObject);
            }
            if (metaObject.hasGetter(DEPARTMENT_ID)) {
                this.setFieldValByName(DEPARTMENT_ID, departmentId, metaObject);
            }
            if (metaObject.hasGetter(PROJECT_ID) && !ObjectUtils.isEmpty(projectId)) {
                this.setFieldValByName(PROJECT_ID, projectId, metaObject);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        if (metaObject.hasGetter(UPDATE_TIME)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        AuthUser currentUser = siteContextHolder.getCurrentUser();
        if (metaObject.hasGetter(UPDATE_ID) && currentUser != null) {
            String id = currentUser.getId();
            this.setFieldValByName(UPDATE_ID, id, metaObject);
        }
    }
}
