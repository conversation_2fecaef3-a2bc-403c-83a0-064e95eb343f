package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2021/9/6 11:00 上午
 */
@Data
public class SummaryAnalysisVO {

    @ApiModelProperty("本月收料-车数")
    private Integer monthReceiveCarsNum;
    @ApiModelProperty("累计收料-车数")
    private Integer countReceiveCarsNum;
    @ApiModelProperty("本月发料-车数")
    private Integer monthSendCarsNum;
    @ApiModelProperty("累计发料-车数")
    private Integer countSendCarsNum;
    @ApiModelProperty("本月超负差-车次")
    private Integer monthMinusCarsNum;
    @ApiModelProperty("累计超负差-车次")
    private Integer countMinusCarsNum;
    @ApiModelProperty("预警未处理数量")
    private Integer warningUnHandleCount;
    @ApiModelProperty("累计预警数量")
    private Integer warningTotal;
    @ApiModelProperty("本月预警数量")
    private Integer monthlyCount;
}
