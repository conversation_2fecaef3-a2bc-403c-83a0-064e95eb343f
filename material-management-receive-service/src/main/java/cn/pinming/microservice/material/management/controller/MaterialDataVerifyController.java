package cn.pinming.microservice.material.management.controller;

import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.resposity.form.MaterialVerifyForm;
import cn.pinming.microservice.material.management.resposity.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.IMaterialVerifyService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 物料对账表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
@Slf4j
@Api(tags = "数据对账")
@RestController
@RequestMapping("/api/biz/material")
public class MaterialDataVerifyController {
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IMaterialVerifyService verifyService;

    @ApiOperation(value = "新增对账记录")
    @PostMapping("/verify")
    public ResponseEntity<SuccessResponse> addVerifyRecord(@RequestBody MaterialVerifyForm form) {
        String id = verifyService.saveVerify(form);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "对账列表", response = MaterialVerifyVO.class)
    @PostMapping("/verifyList")
    public ResponseEntity<SuccessResponse> list(@RequestBody MaterialVerifyQuery materialVerifyQuery) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Optional.ofNullable(currentUser).ifPresent(curUser -> materialVerifyQuery.setCompanyId(currentUser.getCurrentCompanyId()));
        IPage<MaterialVerifyVO> materialVerifyVOIPage = verifyService.listMaterialVerify(materialVerifyQuery);
        return ResponseEntity.ok(new SuccessResponse(materialVerifyVOIPage));
    }

    @ApiOperation(value = "对账详情", response = MaterialVerifyVO.class)
    @GetMapping("/verifyDetail/{id}")
    public ResponseEntity<SuccessResponse> verifyDetail(@PathVariable("id") String id) {
        MaterialVerifyVO vo = verifyService.detail(id);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "归档")
    @GetMapping("/verify/file/{id}")
    public ResponseEntity file(@PathVariable("id") String id) {
        verifyService.materialFile(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "删除对账")
    @DeleteMapping("/verifyDelete/{id}")
    public ResponseEntity delete(@PathVariable("id") String id) {
        verifyService.removeMaterialVerifyById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "对账人列表")
    @GetMapping("/verify/person")
    public ResponseEntity<SuccessResponse> verifyPerson() {
        List<VerifyPersonVO> verifyPersonVOList = verifyService.listVerifyPerson();
        return ResponseEntity.ok(new SuccessResponse(verifyPersonVOList));
    }

    @ApiOperation(value = "地磅收料/移动收料列表")
    @PostMapping("/dataList/{verifyType}")
    public ResponseEntity<SuccessResponse> dataList(@RequestBody MaterialWeighQuery query,@PathVariable("verifyType") Byte  verifyType) {
        MaterialVerifyDataListVO vo = verifyService.dataList(query,verifyType);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "移除收料记录")
    @DeleteMapping("/remove/{verifyId}/{id}")
    public ResponseEntity<SuccessResponse> remove(@PathVariable("verifyId")String verifyId,@PathVariable("id")String id) {
       verifyService.delete(verifyId,id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "导出对账单")
    @GetMapping("/verify/export/{id}")
    public void export(@PathVariable String id, HttpServletResponse response) {
        InputStream is = null;
        try {
            is = this.getClass().getClassLoader().getResourceAsStream("templates/verify-export.xlsx");
            if (is != null) {
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode("对账单", "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx");
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build();
                WriteSheet verifySheet = EasyExcel.writerSheet("对账明细表").build();
                WriteSheet totalSheet = EasyExcel.writerSheet("对账数据透视表").build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 基础信息
                VerifyExcelVO verifyExcelVO = new VerifyExcelVO();
                MaterialVerifyVO vo = verifyService.detail(id);
                List<MaterialVerifyStatisticsVO> materialList = vo.getStatisticsVOList();
                List<MaterialWeighBaseVO> verifyExcelDataVOList = vo.getReceiveDataVOList();
                BeanUtils.copyProperties(vo,verifyExcelVO);
                verifyExcelVO.setExportTime(LocalDateTime.now().toString());
                excelWriter.fill(verifyExcelVO, fillConfig, verifySheet);
                excelWriter.fill(verifyExcelDataVOList, fillConfig, verifySheet);
                excelWriter.fill(materialList, fillConfig, totalSheet);
                excelWriter.finish();
            }
        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<>(4);
            map.put("status", "failure");
            map.put("message", "导出文件失败" + e.getMessage());
            try {
                response.getWriter().println(JSONUtil.toJsonStr(map));
            } catch (IOException ioException) {
                log.error("response write io error.", ioException.getMessage());
            }
            if (is != null) {
                try {
                    is.close();
                } catch (Exception ioCloseException) {
                    log.error("file read io close error.", ioCloseException.getMessage());
                }
            }
        }
    }

}
