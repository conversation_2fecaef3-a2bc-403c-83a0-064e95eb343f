package cn.pinming.microservice.material.management.service;

import cn.pinming.microservice.material.management.resposity.dto.HandleAdviceDTO;
import cn.pinming.microservice.material.management.resposity.entity.WarningHandlerAdvice;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 预警处理人建议表 服务类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 13:21:53
 */
public interface IWarningHandlerAdviceService extends IService<WarningHandlerAdvice> {

    /**
     * 获取最新的处理建议
     * @param warningIds 预警ids
     * @return 建议
     */
    @InterceptorIgnore(tenantLine = "true")
    List<HandleAdviceDTO> getLastHandlerAdvice(List<String> warningIds);
}
