package cn.pinming.microservice.material.management.resposity.mapper;


import cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDataDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialReviseDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialRevise;
import cn.pinming.microservice.material.management.resposity.query.ReviseInfoQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 预警处理人信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
public interface MaterialReviseMapper extends BaseMapper<MaterialRevise> {

    List<MaterialReviseDataDTO> selectMaterialRevise(ReviseInfoQuery reviseInfoQuery);

    MaterialReviseDetailDTO selectMaterialReviseDetail(ReviseInfoQuery reviseInfoQuery);
}
