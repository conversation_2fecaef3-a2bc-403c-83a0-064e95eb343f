package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class GoodsSimpleVO {

    @ApiModelProperty(value = "采购单明细id")
    private String purchaseOrderDetailId;

    @ApiModelProperty(value = "到场品牌")
    private String purchaseBrand;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "分类id")
    private Integer categoryId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "材料全称")
    private String type;

    @ApiModelProperty(value = "二级分类")
    private String categoryName;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "采购结算单位")
    private String unit;

    @ApiModelProperty(value = "下单数量")
    private BigDecimal count;

    @ApiModelProperty(value = "合同约定过磅换算值")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "材料理重vo")
    private TheoreticalInfoVO theoreticalInfoVO;

}
