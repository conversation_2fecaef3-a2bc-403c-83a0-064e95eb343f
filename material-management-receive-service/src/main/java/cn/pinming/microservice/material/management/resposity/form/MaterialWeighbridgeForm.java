package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;


/**
 * 地磅信息Form
 *
 * <AUTHOR>
 */
@Data
public class MaterialWeighbridgeForm extends BaseForm {

    @ApiModelProperty(value = "地磅系统id")
    private String weighSystemId;

    @ApiModelProperty(value = "地磅系统名称")
    @NotBlank
    private String weighSystemName;

    @ApiModelProperty(value = "地磅系统编码")
    private String weighSystemNo;

    @ApiModelProperty(value = "地磅供应商")
//    @NotNull
    private Integer weighSupplier;

    @ApiModelProperty(value = "数据上传方式, 1 IOT上传 2 接口上传 ")
    @NotNull
    private Byte uploadType;

    @ApiModelProperty(value = "磅点名称")
    @NotBlank
    private String weighPointName;
}
