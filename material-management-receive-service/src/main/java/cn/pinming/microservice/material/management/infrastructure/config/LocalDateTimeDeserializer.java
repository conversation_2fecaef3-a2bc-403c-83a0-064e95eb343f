package cn.pinming.microservice.material.management.infrastructure.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * yyyy-MM-dd 转 localDateTime
 * <AUTHOR> hao
 */
public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    @Override
    public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String value = jsonParser.getValueAsString();
        if (NumberUtil.isNumber(value)) {
            long valueAsLong = jsonParser.getValueAsLong();
            return Instant.ofEpochMilli(valueAsLong).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        } else if (StrUtil.isNotBlank(value)) {
            return LocalDateTime.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            return null;
        }
    }
}
