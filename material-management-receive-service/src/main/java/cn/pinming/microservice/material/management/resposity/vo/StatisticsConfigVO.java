package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsConfigVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "选中的二级分类id列表")
    private String[] categoryIds;

    @ApiModelProperty(value = "单位")
    private List<String> units;

}
