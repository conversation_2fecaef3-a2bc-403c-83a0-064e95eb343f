package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ReceiveCardMaterialDetailDTO {
    @ApiModelProperty(value = "totalId")
    private String totalId;

    @ApiModelProperty(value = "到场品牌,多个以,分隔")
    private String brand;

    @ApiModelProperty(value = "实际点验总件数")
    private Integer actualNumber;

    @ApiModelProperty(value = "实际点验总含量")
    private BigDecimal actualContent;

    @ApiModelProperty(value = "含量单位")
    private String unit;

    @ApiModelProperty(value = "实际点验结算含量")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "结算单位")
    private String settlementUnit;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty(value = "面单应收总件数")
    private Integer sendNumber;

    @ApiModelProperty(value = "面单应收总含量")
    private BigDecimal sendContent;

    @ApiModelProperty(value = "面单应收结算含量")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "面单应收总含量单位")
    private String sendUnit;

    @ApiModelProperty(value = "面单应收结算含量单位")
    private String sendSettlementUnit;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "采购单材料备注")
    private String purchaseRemark;

    @ApiModelProperty(value = "采购单材料品牌")
    private String purchaseBrand;

    @ApiModelProperty(value = "实重合计")
    private BigDecimal actualWeightTotal;

    @ApiModelProperty(value = "理重合计")
    private BigDecimal theoreticalWeightTotal;

    @ApiModelProperty(value = "实理偏差值")
    private BigDecimal deviationValueTotal;

    @ApiModelProperty(value = "实理偏差率")
    private BigDecimal deviationRateTotal;

    @ApiModelProperty(value = "状态快照 1,实重 2,理重 null,无理重展示")
    private Byte kind;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;
}
