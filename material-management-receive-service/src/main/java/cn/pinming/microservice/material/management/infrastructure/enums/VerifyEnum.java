package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 * @since 2022/3/21 10:56
 */
public enum VerifyEnum {
    VERIFY((byte) 0, "已归档"),
    VERIFYING((byte) 1, "对账中");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    VerifyEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (VerifyEnum statusEnum : VerifyEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
