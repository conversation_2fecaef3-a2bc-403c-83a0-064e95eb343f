package cn.pinming.microservice.material.management.service.impl;

import cn.pinming.microservice.material.management.resposity.entity.UserConfig;
import cn.pinming.microservice.material.management.resposity.mapper.UserConfigMapper;
import cn.pinming.microservice.material.management.service.IUserConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户配置表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2023-04-17 15:41:33
 */
@Service
public class UserConfigServiceImpl extends ServiceImpl<UserConfigMapper, UserConfig> implements IUserConfigService {

}
