package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 移动收料-简易版本 数据传输对象
 */
@Data
public class SimpleTransformDTO {
    @ApiModelProperty(value = "面单应收结算合计")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "实际收料结算合计")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "结算单位")
    private String settlementUnit;

    @ApiModelProperty(value = "传数据方式 null 移动收料原始版本 1 移动收料简易版")
    private Byte type;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "total数据id")
    private String totalId;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "收料类型 1有合同收料-按合同，2有合同收料-按采购单，3无合同收料")
    private Byte receiveType;
}
