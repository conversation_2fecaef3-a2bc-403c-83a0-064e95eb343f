package cn.pinming.microservice.material.management.controller;

import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.infrastructure.util.WarningUtil;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarning;
import cn.pinming.microservice.material.management.resposity.form.HandleAdviceForm;
import cn.pinming.microservice.material.management.resposity.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.resposity.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.resposity.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.IMaterialWarningService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 预警信息 前端控制器
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Api(tags = "物料预警", value = "ly")
@RestController
@RequestMapping("/api/warning")
@AllArgsConstructor
public class MaterialWarningController {
    @Resource
    private IMaterialWarningService materialWarningService;
    @Resource
    private WarningUtil warningUtil;

    @ApiOperation(value = "创建预警")
    @PostMapping("/add")
    public ResponseEntity<Response> addWarning(@RequestBody MaterialWarningForm form) {
        warningUtil.saveWarning(form);
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "预警列表", response = WarningInfoVO.class)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody WarningInfoQuery warningInfoQuery) {
        IPage<WarningInfoVO> page = materialWarningService.pageListByQuery(warningInfoQuery);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "预警处理")
    @PostMapping("/handle")
    public ResponseEntity<Response> handle(@RequestBody @Valid @Validated HandleAdviceForm handleAdviceForm) {
        materialWarningService.warningHandle(handleAdviceForm);
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "预警状态列表", response = WarningStatusVO.class)
    @GetMapping("/status/list")
    public ResponseEntity<Response> warningStatusList() {
        List<WarningStatusVO> warningStatusVOList = materialWarningService.listWarningStatus();
        return ResponseEntity.ok(new SuccessResponse(warningStatusVOList));
    }


    @ApiOperation(value = "预警类型列表", response = WarningTypeVO.class)
    @GetMapping("/type/list")
    public ResponseEntity<Response> warningTypeList() {
        List<WarningTypeVO> warningTypeVOList = materialWarningService.listWarningType();
        return ResponseEntity.ok(new SuccessResponse(warningTypeVOList));
    }

    @ApiOperation(value = "预警来源列表", response = WarningSourceVO.class)
    @GetMapping("/source/list")
    public ResponseEntity<Response> warningSourceList() {
        List<WarningSourceVO> warningSourceVOList = materialWarningService.listWarningSource();
        return ResponseEntity.ok(new SuccessResponse(warningSourceVOList));
    }

    @ApiOperation(value = "预警详情")
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody WarningDetailQuery warningDetailQuery) {
        List<WarningDetailVO> warningDetailVOList = materialWarningService.getWarningDetail(warningDetailQuery);
        return ResponseEntity.ok(new SuccessResponse(warningDetailVOList));
    }
}
