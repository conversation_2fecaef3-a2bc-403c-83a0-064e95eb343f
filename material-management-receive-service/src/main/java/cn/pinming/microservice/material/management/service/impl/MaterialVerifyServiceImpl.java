package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.management.infrastructure.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.VerifyEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.util.IfBranchUtil;
import cn.pinming.microservice.material.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataVerifyDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialVerify;
import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.resposity.form.MaterialVerifyForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialVerifyMapper;
import cn.pinming.microservice.material.management.resposity.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.query.MobileCardQuery;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.*;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.purchase.management.service.IMaterialPurchaseService;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料对账表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
@Service
public class MaterialVerifyServiceImpl extends ServiceImpl<MaterialVerifyMapper, MaterialVerify> implements IMaterialVerifyService {
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private UserUtil userUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private MaterialVerifyMapper materialVerifyMapper;
    @Resource
    private NoUtil noUtil;
    @Resource
    private SupplierServiceProxy supplierServiceProxy;
    @DubboReference
    private IMaterialPurchaseService materialPurchaseService;
    @Resource
    private IMobileReceiveDetailService mobileReceiveDetailService;
    @Resource
    private IMobileReceiveTotalService mobileReceiveTotalService;

    @Override
    public String saveVerify(MaterialVerifyForm form) {
        AuthUser user = userUtil.getUser();
        String verifyId = form.getVerifyId();
        MaterialVerify verify = new MaterialVerify();
        if (StrUtil.isNotBlank(verifyId)) {
            verify = this.lambdaQuery()
                    .eq(MaterialVerify::getId, verifyId)
                    .one();
            List<MaterialData> receiveList = materialDataService.lambdaQuery()
                    .eq(MaterialData::getReconciliationId, verifyId)
                    .list();
            List<MobileReceiveTotal> mobileList = mobileReceiveTotalService.lambdaQuery()
                    .eq(MobileReceiveTotal::getReconciliationId, verifyId)
                    .list();
            if (ObjectUtil.isNull(verify)) {
                throw new BOException(BOExceptionEnum.VERIFY_DELETED);
            }
            if (verify.getStatus().equals(VerifyEnum.VERIFY.value())) {
                throw new BOException(BOExceptionEnum.VERIFY_COMPELETE);
            }
            if (CollUtil.isNotEmpty(receiveList) || CollUtil.isNotEmpty(mobileList)) {
                if (ObjectUtil.isNotNull(verify.getSupplierId()) && !verify.getSupplierId().equals(form.getSupplierId())) {
                    throw new BOException(BOExceptionEnum.VERIFY_SUPPLIER_CANNOT_CHANGE);
                }
                if (ObjectUtil.isNotNull(verify.getVerifyType()) && !verify.getVerifyType().equals(form.getVerifyType())) {
                    throw new BOException(BOExceptionEnum.VERIFY_TYPE_CANNOT_CHANGE);
                }
            }

            if (ObjectUtil.isNull(verify.getSupplierId()) || ObjectUtil.isNull(verify.getVerifyType())) {
                verify.setSupplierId(form.getSupplierId());
                verify.setVerifyType(form.getVerifyType());
                this.updateById(verify);
            }

            if (form.getVerifyType() == 1) {
                materialDataService.lambdaUpdate()
                        .eq(MaterialData::getReconciliationId, verifyId)
                        .set(MaterialData::getReconciliationId, null)
                        .update();
                if (CollUtil.isNotEmpty(form.getDataList())) {
                    materialDataService.lambdaUpdate()
                            .in(MaterialData::getId, form.getDataList())
                            .set(MaterialData::getReconciliationId, verifyId)
                            .update();
                }
            }
            if (form.getVerifyType() == 2) {
                mobileReceiveTotalService.lambdaUpdate()
                        .eq(MobileReceiveTotal::getReconciliationId, verifyId)
                        .set(MobileReceiveTotal::getReconciliationId, null)
                        .update();
                if (CollUtil.isNotEmpty(form.getDataList())) {
                    mobileReceiveTotalService.lambdaUpdate()
                            .in(MobileReceiveTotal::getId, form.getDataList())
                            .set(MobileReceiveTotal::getReconciliationId, verifyId)
                            .update();
                }
            }
        } else {
            verify.setVerifyNo(noUtil.getBizNo(NoUtil.DZ_KEY_PREFIX, user.getCurrentProjectId()));
            verify.setVerifyProjectId(user.getCurrentProjectId());
            this.save(verify);
            return verify.getId();
        }

        return null;
    }

    @Override
    public MaterialVerifyVO detail(String verifyId) {
        MaterialVerifyVO vo = new MaterialVerifyVO();

        MaterialVerify materialVerify = this.lambdaQuery()
                .eq(MaterialVerify::getId, verifyId)
                .one();
        if (ObjectUtil.isNotNull(materialVerify)) {
            AuthUser user = userUtil.getUser();
            vo.setSupplierId(materialVerify.getSupplierId());
            vo.setVerifyType(materialVerify.getVerifyType());
            vo.setVerifyNo(materialVerify.getVerifyNo());
            vo.setStatus(materialVerify.getStatus());
            ProjectDTO projectDTO = projectServiceProxy.getProjectById(materialVerify.getVerifyProjectId());
            vo.setVerifyProject(ObjectUtil.isNotEmpty(projectDTO) ? projectDTO.getProjectTitle() : null);
            EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(materialVerify.getCompanyId(), materialVerify.getCreateId());
            vo.setVerifyPerson(ObjectUtil.isNotEmpty(employee) ? employee.getMemberName() : null);
            if (StrUtil.isNotBlank(materialVerify.getSupplierId())) {
                List<SupplierDTO> listByIds = supplierServiceProxy.findListByIds(materialVerify.getCompanyId(), Arrays.asList(Integer.valueOf(materialVerify.getSupplierId())));
                vo.setVerifySupplier(CollUtil.isNotEmpty(listByIds) ? listByIds.get(0).getName() : null);
            }

            if (ObjectUtil.isNotNull(materialVerify.getVerifyType())) {
                if (materialVerify.getVerifyType() == 1) {
                    MaterialWeighQuery query = new MaterialWeighQuery();
                    query.setCompanyId(user.getCurrentCompanyId());
                    query.setReconciliationId(verifyId);
                    query.setSize(Integer.MAX_VALUE);
                    query.setPages(0);
                    query.setType((byte) 1);
                    IPage<MaterialWeighBaseVO> materialWeighBaseVOIPage = materialSendReceiveService.showWeightInfo(query);
                    if (CollUtil.isNotEmpty(materialWeighBaseVOIPage.getRecords())) {
                        vo.setReceiveDataVOList(materialWeighBaseVOIPage.getRecords());
                        List<String> materialDataIdList = materialWeighBaseVOIPage.getRecords().stream().map(MaterialWeighBaseVO::getId).collect(Collectors.toList());
                        List<MaterialVerifyStatisticsVO> materialVerifyStatisticsVOS = this.queryViewCal(materialDataIdList);
                        vo.setStatisticsVOList(materialVerifyStatisticsVOS);
                    }
                } else if (materialVerify.getVerifyType() == 2) {
                    MobileCardQuery query = new MobileCardQuery();
                    query.setReconciliationId(verifyId);
                    query.setCompanyId(user.getCurrentCompanyId());
                    List<MobileReceiveVerifyVO> mobileReceiveVerifyVOS = mobileReceiveDetailService.selectPageForVerify(query);
                    vo.setMobileDataVOList(mobileReceiveVerifyVOS);
                    if (CollUtil.isNotEmpty(mobileReceiveVerifyVOS)) {
                        vo.setStatisticsVOList(queryViewCalMobile(mobileReceiveVerifyVOS));
                    }
                }
            }

            // 对账中且为创建者
            IfBranchUtil.isTureOrFalse(materialVerify.getStatus() == 1 && user != null && user.getId().equals(materialVerify.getCreateId())).trueOrFalseHandle(
                    () -> {
                        vo.setAddReceive(true);
                        vo.setScanAddReceive(true);
                        vo.setVerifyFile(true);
                    },
                    () -> {
                        vo.setAddReceive(false);
                        vo.setScanAddReceive(false);
                        vo.setVerifyFile(false);
                    });
        }

        return vo;
    }

    @Override
    public IPage<MaterialVerifyVO> listMaterialVerify(MaterialVerifyQuery materialVerifyQuery) {
        AuthUser user = userUtil.getUser();
        // 查询对账列表
        IPage<MaterialDataVerifyDTO> page = materialVerifyMapper.selectMaterialVerify(materialVerifyQuery);
        List<MaterialDataVerifyDTO> materialDataVerifyDTOList = page.getRecords();
        // 对账人map
        List<String> personIdList = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifyPersonId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<EmployeeSimpleDTO> employeeDetailDtoList = employeeServiceProxy.employeeList(user.getCurrentCompanyId(), personIdList);
        Map<String, EmployeeSimpleDTO> personMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(employeeDetailDtoList)) {
            personMap = employeeDetailDtoList.stream().collect(Collectors.toMap(EmployeeSimpleDTO::getMemberId, e -> e));
        }

        // 供应商map
        List<Integer> suppliers = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifySupplierId).filter(Objects::nonNull).map(Integer::valueOf).collect(Collectors.toList());
        List<SupplierDTO> cooperateVOList = supplierServiceProxy.findListByIds(user.getCurrentCompanyId(), suppliers);
        Map<Integer, SupplierDTO> supplierMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(cooperateVOList)) {
            supplierMap = cooperateVOList.stream().collect(Collectors.toMap(SupplierDTO::getId, e -> e));
        }

        // 项目map
        List<Integer> projects = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifyProjectId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ProjectDTO> simpleProjectList = projectServiceProxy.getSimpleProjects(projects);
        Map<Integer, ProjectDTO> projectMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(simpleProjectList)) {
            projectMap = simpleProjectList.stream().collect(Collectors.toMap(ProjectDTO::getProjectId, e -> e));
        }

        Map<String, EmployeeSimpleDTO> finalPersonMap = personMap;
        Map<Integer, SupplierDTO> finalSupplierMap = supplierMap;
        Map<Integer, ProjectDTO> finalProjectMap = projectMap;
        List<MaterialVerifyVO> materialVerifyVOList = materialDataVerifyDTOList.stream().map(materialDataVerifyDTO -> {
            MaterialVerifyVO materialDataVerifyVO = new MaterialVerifyVO();
            BeanUtils.copyProperties(materialDataVerifyDTO, materialDataVerifyVO);
            // 对账人
            String verifyPersonId = materialDataVerifyDTO.getVerifyPersonId();
            EmployeeSimpleDTO verifyPersonDTO = finalPersonMap.get(verifyPersonId);
            Optional.ofNullable(verifyPersonDTO).ifPresent(verifyPerson -> materialDataVerifyVO.setVerifyPerson(verifyPerson.getMemberName()));

            // 默认为false
            materialDataVerifyVO.setIsCreate(Boolean.FALSE);
            // 当前用户是否是创建者
            AuthUser currentUser = userUtil.getUser();
            Optional.ofNullable(currentUser).ifPresent(cur ->
                    IfBranchUtil.isTrue(cur.getId().equals(verifyPersonId)).trueHandle(() -> materialDataVerifyVO.setIsCreate(Boolean.TRUE))
            );
            // 项目
            Integer verifyProjectId = materialDataVerifyDTO.getVerifyProjectId();
            ProjectDTO projectVO = finalProjectMap.get(verifyProjectId);
            Optional.ofNullable(projectVO).ifPresent(project -> materialDataVerifyVO.setVerifyProject(project.getProjectTitle()));
            // 供应商
            Integer verifySupplierId = materialDataVerifyDTO.getVerifySupplierId();
            SupplierDTO cooperateVO = finalSupplierMap.get(verifySupplierId);
            Optional.ofNullable(cooperateVO).ifPresent(cooperate -> materialDataVerifyVO.setVerifySupplier(cooperate.getName()));

            return materialDataVerifyVO;
        }).collect(Collectors.toList());
        IPage<MaterialVerifyVO> list = new Page<>();
        BeanUtils.copyProperties(page, list);
        list.setRecords(materialVerifyVOList);
        return list;
    }


    @Override
    public void materialFile(String id) {
        Optional.ofNullable(id).orElseThrow(() -> new BOException(BOExceptionEnum.VERIFY_ID_NOT_NULL));
        if (!havePermission()) {
            throw new BOException(BOExceptionEnum.NO_PERMISSION);
        }
        materialVerifyMapper.updateFileStatusById(id);
    }

    @Override
    public void removeMaterialVerifyById(String id) {
        MaterialVerify one = this.lambdaQuery()
                .eq(MaterialVerify::getId, id)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            if (one.getStatus().equals(VerifyEnum.VERIFY.value())) {
                throw new BOException(BOExceptionEnum.VERIFY_COMPELETE);
            }
            Byte verifyType = one.getVerifyType();
            if (ObjectUtil.isNotNull(verifyType)) {
                if (verifyType == 1) {
                    materialDataService.lambdaUpdate()
                            .eq(MaterialData::getReconciliationId, id)
                            .set(MaterialData::getReconciliationId, null)
                            .update();
                }
                if (verifyType == 2) {
                    mobileReceiveTotalService.lambdaUpdate()
                            .eq(MobileReceiveTotal::getReconciliationId, id)
                            .set(MobileReceiveTotal::getReconciliationId, null)
                            .update();
                }
            }
            this.lambdaUpdate()
                    .eq(MaterialVerify::getId, id)
                    .set(MaterialVerify::getIsDeleted, 1)
                    .update();
        }
    }

    @Override
    public List<VerifyPersonVO> listVerifyPerson() {
        AuthUser currentUser = userUtil.getUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        List<String> personIdList = materialVerifyMapper.selectVerifyPerson(companyId, projectId);
        List<VerifyPersonVO> result = Lists.newArrayList();
        if (CollUtil.isNotEmpty(personIdList)) {
            List<EmployeeSimpleDTO> employeeDetailDtoList = employeeServiceProxy.employeeList(companyId, personIdList);
            if (CollUtil.isNotEmpty(employeeDetailDtoList)) {
                Map<String, EmployeeSimpleDTO> dtoMap = employeeDetailDtoList.stream().collect(Collectors.toMap(EmployeeSimpleDTO::getMemberId, e -> e));
                result = personIdList.stream().map(personId -> {
                    VerifyPersonVO verifyPersonVO = new VerifyPersonVO();
                    verifyPersonVO.setVerifyPerson(personId);
                    EmployeeSimpleDTO employeeDetailDto = dtoMap.get(personId);
                    Optional.ofNullable(employeeDetailDto).ifPresent(emp -> verifyPersonVO.setVerifyPersonName(emp.getMemberName()));
                    return verifyPersonVO;
                }).collect(Collectors.toList());
            }
        }
        return result;
    }

    @Override
    public MaterialVerifyDataListVO dataList(MaterialWeighQuery query, Byte verifyType) {
        MaterialVerifyDataListVO vo = new MaterialVerifyDataListVO();
        if (verifyType == 1) {
            // 地磅收料列表
            query.setSize(Integer.MAX_VALUE);
            query.setPages(0);
            IPage<MaterialWeighBaseVO> list = materialSendReceiveService.showWeightInfo(query);
            if (CollUtil.isNotEmpty(list.getRecords())) {
                List<MaterialWeighBaseVO> records = list.getRecords();
                records.forEach(e -> {
                    if (StrUtil.isNotBlank(e.getReconciliationId()) && e.getReconciliationId().equals(query.getVerifyId())) {
                        e.setIsChoose(true);
                    } else {
                        e.setIsChoose(false);
                    }
                });
                List<MaterialWeighBaseVO> purchaseList = records.stream().filter(e -> StrUtil.isNotBlank(e.getPurchaseOrderId())).collect(Collectors.toList());
                List<MaterialWeighBaseVO> contractList = records.stream().filter(e -> StrUtil.isNotBlank(e.getContractDetailId()) && StrUtil.isBlank(e.getPurchaseOrderId())).collect(Collectors.toList());
                List<MaterialWeighBaseVO> supplierList = records.stream().filter(e -> StrUtil.isBlank(e.getPurchaseOrderId()) && StrUtil.isBlank(e.getContractDetailId())).collect(Collectors.toList());
                vo.setPurchaseOrders(purchaseList);
                vo.setContractOrders(contractList);
                vo.setSupplierOrders(supplierList);
            }
        }
        if (verifyType == 2) {
            // 移动收料列表
            MobileCardQuery mobileCardQuery = new MobileCardQuery();
            mobileCardQuery.setCompanyId(query.getCompanyId());
            mobileCardQuery.setSupplierId(String.valueOf(query.getSupplierId()));
            mobileCardQuery.setStartTime(query.getStartTime());
            mobileCardQuery.setEndTime(query.getEndTime());
            mobileCardQuery.setProjectId(query.getProjectId());
            List<MobileReceiveVerifyVO> verifyVOS = mobileReceiveDetailService.selectPageForVerify(mobileCardQuery);
            if (CollUtil.isNotEmpty(verifyVOS)) {
                List<MaterialWeighBaseVO> collect = verifyVOS.stream().map(e -> {
                    MaterialWeighBaseVO baseVO = new MaterialWeighBaseVO();
                    BeanUtils.copyProperties(e, baseVO);
                    baseVO.setWeightSend(e.getSendSettlementTotal());
                    baseVO.setActualCount(e.getActualSettlementTotal());
                    if (StrUtil.isNotBlank(e.getReconciliationId()) && e.getReconciliationId().equals(query.getVerifyId())) {
                        baseVO.setIsChoose(true);
                    } else {
                        baseVO.setIsChoose(false);
                    }
                    return baseVO;
                }).collect(Collectors.toList());
                List<MaterialWeighBaseVO> purchaseList = collect.stream().filter(e -> e.getReceiveType().equals("2")).collect(Collectors.toList());
                List<MaterialWeighBaseVO> contractList = collect.stream().filter(e -> e.getReceiveType().equals("1")).collect(Collectors.toList());
                List<MaterialWeighBaseVO> supplierList = collect.stream().filter(e -> e.getReceiveType().equals("3")).collect(Collectors.toList());
                vo.setPurchaseOrders(purchaseList);
                vo.setContractOrders(contractList);
                vo.setSupplierOrders(supplierList);
            }

        }
        return vo;

    }

    @Override
    public void delete(String verifyId, String id) {
        MaterialVerify verify = this.lambdaQuery()
                .eq(MaterialVerify::getId, verifyId)
                .one();
        if (ObjectUtil.isNull(verify)) {
            throw new BOException(BOExceptionEnum.VERIFY_DELETED);
        }
        if (verify.getStatus().equals(VerifyEnum.VERIFY.value())) {
            throw new BOException(BOExceptionEnum.VERIFY_COMPELETE);
        }
        if (verify.getVerifyType().equals(WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value())) {
            materialDataService.lambdaUpdate()
                    .eq(MaterialData::getId, id)
                    .eq(MaterialData::getReconciliationId, verifyId)
                    .set(MaterialData::getReconciliationId, null)
                    .update();
        }
        if (verify.getVerifyType().equals(WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value())) {
            mobileReceiveTotalService.lambdaUpdate()
                    .eq(MobileReceiveTotal::getReconciliationId, verifyId)
                    .eq(MobileReceiveTotal::getId, id)
                    .set(MobileReceiveTotal::getIsDeleted, 1)
                    .update();
        }
    }

    private MaterialVerifyStatisticsVO cal(List<MaterialData> list) {
        MaterialVerifyStatisticsVO materialData = new MaterialVerifyStatisticsVO();

        BigDecimal weightDeduct = BigDecimal.ZERO;
        BigDecimal weightActual = BigDecimal.ZERO;
        BigDecimal weightSend = BigDecimal.ZERO;
        BigDecimal actualCount = BigDecimal.ZERO;
        BigDecimal actualReceive = BigDecimal.ZERO;
        BigDecimal deviation = BigDecimal.ZERO;
        BigDecimal deviationRate = BigDecimal.ZERO;
        BigDecimal weightGross = list.stream().map(MaterialData::getWeightGross).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weightTare = list.stream().map(MaterialData::getWeightTare).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weightNet = list.stream().map(MaterialData::getWeightNet).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<MaterialData> listWithWeightDeduct = list.stream().filter(e -> e.getWeightDeduct() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithWeightDeduct)) {
            weightDeduct = listWithWeightDeduct.stream().map(MaterialData::getWeightDeduct).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<MaterialData> listWithWeightActual = list.stream().filter(e -> e.getWeightActual() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithWeightActual)) {
            weightActual = list.stream().map(MaterialData::getWeightActual).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<MaterialData> listWithPurchase = list.stream().filter(e -> StrUtil.isNotBlank(e.getPurchaseOrderId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithPurchase)) {
            List<BigDecimal> collect = listWithPurchase.stream().map(e -> {
                return materialPurchaseService.getPurchaseOrderCount(e.getPurchaseOrderId(), e.getMaterialId(), e.getWeightUnit());
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                materialData.setPurchaseTotal(collect.get(0));
            }
        }
        List<MaterialData> listWithWeightSend = list.stream().filter(e -> e.getWeightSend() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithWeightSend)) {
            weightSend = list.stream().map(MaterialData::getWeightSend).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<MaterialData> listWithActualCount = list.stream().filter(e -> e.getActualCount() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithActualCount)) {
            actualCount = list.stream().map(MaterialData::getActualCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<MaterialData> listWithActualReceive = list.stream().filter(e -> e.getActualReceive() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithActualReceive)) {
            actualReceive = list.stream().map(MaterialData::getActualReceive).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        deviation = NumberUtil.sub(actualCount, weightSend);
        if (!weightSend.equals(BigDecimal.ZERO)) {
            deviationRate = NumberUtil.div(deviation, weightSend, 4, RoundingMode.HALF_UP);
        }

        materialData.setWeightGross(weightGross);
        materialData.setWeightTare(weightTare);
        materialData.setWeightNet(weightNet);
        materialData.setWeightDeduct(weightDeduct);
        materialData.setWeightActual(weightActual);
        materialData.setWeightSend(weightSend);
        materialData.setActualCount(actualCount);
        materialData.setActualReceive(actualReceive);
        materialData.setDeviation(deviation);
        materialData.setDeviationRate(deviationRate);
        materialData.setUnit(list.get(0).getWeightUnit());

        return materialData;
    }

    private List<MaterialVerifyStatisticsVO> queryViewCalMobile(List<MobileReceiveVerifyVO> list) {
        List<MaterialVerifyStatisticsVO> result = new ArrayList<>();

        Map<Integer, Map<String, List<MobileReceiveVerifyVO>>> map = list.stream().collect(Collectors.groupingBy(MobileReceiveVerifyVO::getMaterialId,
                Collectors.groupingBy(MobileReceiveVerifyVO::getUnit)));
        map.forEach((k, v) -> {
            v.forEach((m, n) -> {
                MaterialVerifyStatisticsVO materialVerifyStatisticsVO = this.calMobile(n);
                result.add(materialVerifyStatisticsVO);
            });
        });
        return result;
    }


    private MaterialVerifyStatisticsVO calMobile(List<MobileReceiveVerifyVO> list) {
        MaterialVerifyStatisticsVO vo = new MaterialVerifyStatisticsVO();

        String materialCategoryName = list.get(0).getCategoryName();
        String materialName = list.get(0).getMaterialName();
        List<MobileReceiveVerifyVO> listWithPurchase = list.stream().filter(e -> StrUtil.isNotBlank(e.getPurchaseOrderId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithPurchase)) {
            BigDecimal purchaseCount = listWithPurchase.stream().map(e -> {
                return materialPurchaseService.getPurchaseOrderCount(e.getPurchaseOrderId(), e.getMaterialId(), e.getUnit());
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setPurchaseTotal(purchaseCount);
        }
        BigDecimal sendSettlementTotal = list.stream().map(MobileReceiveVerifyVO::getSendSettlementTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualSettlementTotal = list.stream().map(MobileReceiveVerifyVO::getActualSettlementTotal).reduce(BigDecimal.ZERO, BigDecimal::add);

        vo.setCategoryName(materialCategoryName);
        vo.setMaterialName(materialName);
        BigDecimal deviation = NumberUtil.sub(actualSettlementTotal, sendSettlementTotal);
        vo.setDeviation(deviation);
        vo.setDeviationRate(NumberUtil.div(deviation, sendSettlementTotal, 4, RoundingMode.HALF_UP));
        return vo;
    }

    private List<MaterialVerifyStatisticsVO> queryViewCal(List<String> dataIdList) {
        List<MaterialVerifyStatisticsVO> result = new ArrayList<>();

        List<MaterialData> list = materialDataService.lambdaQuery()
                .in(MaterialData::getId, dataIdList)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            List<MaterialData> listWithMaterialId = list.stream().filter(e -> e.getMaterialId() != null).collect(Collectors.toList());
            List<MaterialData> listWithMaterialName = list.stream().filter(e -> e.getMaterialId() == null && e.getMaterialName() != null).collect(Collectors.toList());
            List<MaterialData> listWithoutMaterial = list.stream().filter(e -> e.getMaterialId() == null && e.getMaterialName() == null).collect(Collectors.toList());
            List<Integer> materialIdList = list.stream().map(MaterialData::getMaterialId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Integer, MaterialDto> materialMap = new HashMap<>();

            if (CollUtil.isNotEmpty(materialIdList)) {
                List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
                if (CollUtil.isNotEmpty(materialDtos)) {
                    materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
                }
            }
            if (CollUtil.isNotEmpty(listWithMaterialId)) {
                Map<Integer, Map<String, List<MaterialData>>> mapWithMaterialId = listWithMaterialId.stream().collect(Collectors.groupingBy(MaterialData::getMaterialId,
                        Collectors.groupingBy(MaterialData::getWeightUnit)));
                Map<Integer, MaterialDto> finalMaterialMap = materialMap;
                mapWithMaterialId.forEach((k, v) -> {
                    String categoryName;
                    String materialName;
                    if (CollUtil.isNotEmpty(finalMaterialMap) && ObjectUtil.isNotNull(finalMaterialMap.get(k))) {
                        MaterialDto materialDto = finalMaterialMap.get(k);
                        categoryName = materialDto.getMaterialCategoryName();
                        materialName = materialDto.getMaterialName() + '/' + materialDto.getMaterialSpec();
                    } else {
                        materialName = null;
                        categoryName = null;
                    }
                    v.forEach((m, n) -> {
                        MaterialVerifyStatisticsVO vo = cal(n);
                        vo.setCategoryName(categoryName);
                        vo.setMaterialName(materialName);
                        result.add(vo);
                    });
                });
            }
            if (CollUtil.isNotEmpty(listWithMaterialName)) {
                Map<String, Map<String, List<MaterialData>>> mapWithMaterialName = listWithMaterialName.stream().collect(Collectors.groupingBy(MaterialData::getMaterialName,
                        Collectors.groupingBy(MaterialData::getWeightUnit)));
                mapWithMaterialName.forEach((k, v) -> {
                    v.forEach((m, n) -> {
                        MaterialVerifyStatisticsVO vo = cal(n);
                        vo.setMaterialName(k);
                        result.add(vo);
                    });
                });
            }
            if (CollUtil.isNotEmpty(listWithoutMaterial)) {
                Map<String, List<MaterialData>> mapWithoutMaterial = listWithoutMaterial.stream().collect(Collectors.groupingBy(MaterialData::getWeightUnit));
                mapWithoutMaterial.forEach((k, v) -> {
                    MaterialVerifyStatisticsVO vo = cal(v);
                    result.add(vo);
                });
            }
        }

        return result;
    }

    /**
     * 当前用户是否有权限处理
     *
     * @return Boolean
     */
    private Boolean havePermission() {
        AuthUser currentUser = userUtil.getUser();
        if (currentUser != null) {
            return materialHandlerService.enableHandle(currentUser.getId(), HandleTypeEnum.CHECK_HANDLE.value());
        }
        return false;
    }
}
