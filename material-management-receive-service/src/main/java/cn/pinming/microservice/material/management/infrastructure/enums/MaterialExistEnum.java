package cn.pinming.microservice.material.management.infrastructure.enums;

public enum MaterialExistEnum {
    YES((byte)1, "存在"),
    NO((byte)2, "不存在"),
    OTHER((byte)3, "按合同扫码收料-存在于合同");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    MaterialExistEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (MaterialExistEnum statusEnum : MaterialExistEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }

}
