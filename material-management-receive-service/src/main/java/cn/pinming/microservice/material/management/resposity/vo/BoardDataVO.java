package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/1/19 15:12
 */
@Data
public class BoardDataVO {

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("数据列表")
    private List<BoardDataItemVO> list;

}
