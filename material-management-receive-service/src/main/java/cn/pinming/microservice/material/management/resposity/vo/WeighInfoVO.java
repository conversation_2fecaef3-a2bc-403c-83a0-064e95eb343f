package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.material.management.infrastructure.config.LocalDateTimeConverter;
import cn.pinming.microservice.material.management.resposity.dto.WeighTruckChangeDTO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * 过磅情况VO
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class WeighInfoVO implements Serializable {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("项目名称")
    @ExcelProperty("项目名称")
    private String projectTitle;

    @ApiModelProperty("地磅第一次使用时间")
    @ExcelProperty(value = "地磅第一次使用时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime firstUsedTime;

    @ApiModelProperty("地磅最后一次使用时间")
    @ExcelProperty(value = "地磅最后一次使用时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime lastUsedTime;

    @ApiModelProperty("过磅总车次")
    @ExcelProperty("过磅总车次")
    private Integer weighingCarCount;

    @ApiModelProperty("收料过磅车次")
    @ExcelProperty("收料过磅车次")
    private Integer weighingCarNumber;

    @ApiModelProperty("发料过磅车次")
    @ExcelProperty("发料过磅车次")
    private Integer sendingCarNumber;

    @ApiModelProperty("过磅重量（吨）")
    @ExcelProperty("过磅重量（吨）")
    private BigDecimal weighWeight;

    @ApiModelProperty("过磅车次变化")
    private List<WeighTruckChangeDTO> list;
}
