package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/5/26
 */
@Data
public class MaterialVerifyMaterialDTO {

    @ApiModelProperty(value = "物质id")
    private String materialId;

    @ApiModelProperty(value = "是否关联采购单")
    private Boolean isPurchase;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "品种及规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "总毛重")
    private Float weightGrossTotal;

    @ApiModelProperty(value = "总皮重")
    private Float weightTareTotal;

    @ApiModelProperty(value = "总净重")
    private Float weightNetTotal;

    @ApiModelProperty(value = "总扣重")
    private Float weightDeductTotal;

    @ApiModelProperty(value = "总实重")
    private Float weightActualTotal;

    @ApiModelProperty(value = "采购量")
    private Float purchaseTotal;

    @ApiModelProperty(value = "面单数量总计")
    private Float weightSendTotal;

    @ApiModelProperty(value = "实际数量总计")
    private Float actualCountTotal;

    @ApiModelProperty(value = "实收数量总计")
    private Float actualReceiveTotal;

    @ApiModelProperty(value = "偏差量总计(实际数量-面单数量)")
    private Float deviationTotal;

    @ApiModelProperty(value = "偏差率总计")
    private Float deviationRateTotal;

    @ApiModelProperty(value = "结算单位")
    private String unit;

}
