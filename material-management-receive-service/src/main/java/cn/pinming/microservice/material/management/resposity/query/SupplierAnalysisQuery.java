package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商分析.
 *
 * <AUTHOR>
 * @version 2021/9/2 5:05 下午
 */
@Data
public class SupplierAnalysisQuery extends BaseQuery {

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplier;

    @ApiModelProperty(value = "供应商id")
    private List<Integer> supplierIds;

    @ApiModelProperty(value = "项目id列表")
    private List<Integer> projectIds;

    @ApiModelProperty(value = "材料类别")
    private Integer categoryId;

    @ApiModelProperty(value = "材料类别列表")
    private List<Integer> categoryIds;

    @ApiModelProperty(value = "材料id")
    private List<Integer> materialIds;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "要货开始时间")
    private LocalDate startDate;

    @ApiModelProperty(value = "要货结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "收货开始时间")
    private LocalDate receiveStartDate;

    @ApiModelProperty(value = "收货结束时间")
    private LocalDate receiveEndDate;

    @ApiModelProperty(value = "移动收料类型")
    private Byte receiveType;

    @ApiModelProperty(value = "收料方式 1全部收料 2地磅收料 3移动收料(有合同) " +
            "11 报备收料 12 临时收料 " +
            "21 有合同-按合同 22 有合同-按采购单 23 无合同")
    private Byte type;

    @ApiModelProperty(value = "收料方式")
    private String receiveWay;

    @ApiModelProperty(value = "组织id")
    private Integer departmentId;
}
