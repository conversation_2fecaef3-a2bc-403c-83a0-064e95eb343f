package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/14
 * @description
 */
@Data
public class ReceiveOverviewModalQuery {

    @ApiModelProperty(value = "二级分类ID(其他物料柱状图下钻)")
    private Integer categoryId;

    @ApiModelProperty(value = "材料ID(大宗材料柱状图下钻)")
    private Set<Integer> materialIds;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "收料方式")
    private String receiveWay;

    @ApiModelProperty(value = "开始时间(yyyy/MM/dd)")
    private String start;

    @ApiModelProperty(value = "结束时间(yyyy/MM/dd)")
    private String end;

}
