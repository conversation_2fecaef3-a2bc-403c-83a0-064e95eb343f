package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class MobileGoodsCardDTO {

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "材料结算合计")
    private BigDecimal settlementTotal;

    @ApiModelProperty(value = "结算单位")
    private String settlementUnit;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;
}
