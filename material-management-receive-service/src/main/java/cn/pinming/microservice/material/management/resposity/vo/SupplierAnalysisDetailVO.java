package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/8 5:47 下午
 */
@Data
public class SupplierAnalysisDetailVO extends SimpleSupplierDTO {

    @ApiModelProperty("采购下单量")
    private BigDecimal purchaseAmount;

    @ApiModelProperty("面单应收量")
    private BigDecimal weightSendAmount;

    @ApiModelProperty("实际收料量")
    private BigDecimal weightActualAmount;

    @ApiModelProperty("实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty("偏差量")
    private BigDecimal deviationAmount;

    @ApiModelProperty("偏差比例")
    private BigDecimal deviationRateAmount;

}
