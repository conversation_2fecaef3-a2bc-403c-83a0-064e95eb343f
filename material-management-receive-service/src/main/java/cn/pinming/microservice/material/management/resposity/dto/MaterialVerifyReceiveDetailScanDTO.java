package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:58
 */
@Data
public class MaterialVerifyReceiveDetailScanDTO {

    @ApiModelProperty(value = "收料/发料")
    private Byte type;

    @ApiModelProperty(value = "收料ID")
    private String weighRecordId;

    @ApiModelProperty(value = "收料详情ID")
    private String receiveId;

    @ApiModelProperty(value = "收料详情ID")
    private String receiveDataId;

    @ApiModelProperty(value = "收料单号")
    private String receiveNo;

    @ApiModelProperty(value = "车牌/车号")
    private String truckNo;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "分类ID")
    private String categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "面单数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态(0 正常 1 负偏差 2 正偏差)")
    private Byte deviationStatus;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "合同详情id")
    private String contractDetailId;

    @ApiModelProperty(value = "对账批次ID")
    private String verifyId;

    @ApiModelProperty(value = "对账批次编号")
    private String verifyNo;

    @ApiModelProperty(value = "是否对账")
    private Boolean isVerify;

    @ApiModelProperty(value = "是否有效")
    private Boolean validity;

    @ApiModelProperty(value = "是否无归属")
    private Boolean affiliation;

    @ApiModelProperty(value = "是否存在预警且未处理完毕")
    private Boolean warning;

    @ApiModelProperty(value = "是否超负差")
    private Boolean deviation;

    @ApiModelProperty(value = "偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "偏差阈值下限")
    private BigDecimal deviationFloor;

}
