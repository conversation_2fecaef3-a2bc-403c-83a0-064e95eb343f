package cn.pinming.microservice.material.management;

import cn.pinming.core.actuator.probe.ProbeServlet;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"cn.pinming.microservice.material.management","cn.pinming.microservice.base.common","cn.pinming.material.v2"})
@MapperScan(basePackages = {"cn.pinming.microservice.material.management.resposity.mapper"})
@ServletComponentScan(basePackageClasses = ProbeServlet.class)
public class Main {
    public static void main(String[] args) {
        SpringApplication.run(Main.class,args);
    }
}