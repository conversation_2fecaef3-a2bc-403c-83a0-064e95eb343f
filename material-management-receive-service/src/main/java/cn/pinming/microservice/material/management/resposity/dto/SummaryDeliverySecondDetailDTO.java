package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SummaryDeliverySecondDetailDTO {
    @ApiModelProperty("累计发料量")
    private BigDecimal accumulationCount;

    @ApiModelProperty("本月新增量")
    private BigDecimal monthlyCount;

    @ApiModelProperty("二级分类id")
    private Integer categoryId;

    @ApiModelProperty("二级分类名称")
    private String categoryName;
}
