package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveOverviewTable {

    @ApiModelProperty(value = "时间")
    private String time;

    @ApiModelProperty(value = "面单量")
    private Double y1;

    @ApiModelProperty(value = "实际量")
    private Double y2;

    @ApiModelProperty(value = "实际量-面单量")
    private Double y3;

    @ApiModelProperty(value = "实收量")
    private Double y4;

}
