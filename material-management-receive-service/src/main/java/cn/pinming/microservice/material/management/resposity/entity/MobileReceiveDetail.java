package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 移动收料细节表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mobile_receive_detail")
@ApiModel(value = "MobileReceiveDetail对象", description = "移动收料细节表")
public class MobileReceiveDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收货单id")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "材料id")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "total数据id")
    @TableField("total_id")
    private String totalId;

    @ApiModelProperty(value = "到场品牌 多个以，分隔")
    private String brand;

    @ApiModelProperty(value = "是否标准件 1，是；2，否")
    @TableField("is_standard")
    private Byte isStandard;

    @ApiModelProperty(value = "件数")
    private Integer number;

    @ApiModelProperty(value = "每件数含量")
    private BigDecimal content;

    @ApiModelProperty(value = "含量单位")
    private String unit;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "结算单位")
    @TableField("settlement_unit")
    private String settlementUnit;

    @ApiModelProperty(value = "结算合计")
    @TableField("settlement_total")
    private BigDecimal settlementTotal;

    @ApiModelProperty(value = "智能点数图片")
    @TableField("points_pic")
    private String pointsPic;

    @ApiModelProperty(value = "每\"含量单位\"定尺")
    @TableField("length_per_unit")
    private BigDecimal lengthPerUnit;

    @ApiModelProperty(value = "实际称重小计")
    @TableField("actual_weight")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "实理偏差值")
    @TableField("deviation_value")
    private BigDecimal deviationValue;

    @ApiModelProperty(value = "实理偏差率")
    @TableField("deviation_rate")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "理重小计")
    @TableField("theoretical_weight")
    private BigDecimal theoreticalWeight;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
