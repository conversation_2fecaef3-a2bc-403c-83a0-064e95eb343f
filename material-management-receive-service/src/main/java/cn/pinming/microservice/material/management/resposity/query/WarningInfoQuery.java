package cn.pinming.microservice.material.management.resposity.query;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 物料预警query
 * <AUTHOR>
 * @Date 2022/1/17 11:16
 */

@Data
public class WarningInfoQuery extends BaseQuery{

    @ApiModelProperty(value = "预警来源记录编号")
    private String warningSourceNo;

    @ApiModelProperty(value = "发生项目")
    private List<Integer> sourceProjectIds;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    private Byte warningStatus;

    @ApiModelProperty(value = "预警来源")
    private String warningSource;

    @ApiModelProperty(value = "预警类型")
    private Byte warningType;

    @ApiModelProperty(value = "预警编号")
    private String warningNo;

    @ApiModelProperty(value = "预警大类 1、数据完整性 2、业务规范性")
    private Byte warningKind;
}
