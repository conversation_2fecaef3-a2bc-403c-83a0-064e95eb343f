package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MaterialVerifyForm {
    @ApiModelProperty(value = "对账id")
    private String verifyId;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "收料方式 1 地磅收料 2 移动收料")
    private Byte verifyType;

    @ApiModelProperty(value = "收料列表")
    private List<String> dataList;
}
