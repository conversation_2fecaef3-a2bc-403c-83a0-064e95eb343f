package cn.pinming.microservice.material.management.service;


import cn.pinming.microservice.material.management.resposity.entity.MobileReceive;
import cn.pinming.microservice.material.management.resposity.form.*;
import cn.pinming.microservice.material.management.resposity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 移动收料表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
public interface IMobileReceiveService extends IService<MobileReceive> {

    /**
     * 新增收料记录
     *
     * @param form
     */
    void add(MobileReceiveForm form);

    /**
     * 收料单详情
     *
     * @param receiveId
     * @return
     */
    ReceiveCardDetailVO detail(String receiveId);

    void fresh(MobileReceiveUpdateForm form);

}
