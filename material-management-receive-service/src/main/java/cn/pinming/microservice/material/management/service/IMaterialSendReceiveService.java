package cn.pinming.microservice.material.management.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.resposity.dto.OperateCenterSendReceiveDTO;
import cn.pinming.microservice.material.management.resposity.dto.PicDTO;
import cn.pinming.microservice.material.management.resposity.dto.StandardMaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.query.*;
import cn.pinming.microservice.material.management.resposity.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收货/发货单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IMaterialSendReceiveService extends IService<MaterialSendReceive> {

    /**
     * 地磅收货列表
     *
     * @param query
     * @return
     */
    IPage<MaterialWeighBaseVO> showWeightInfo(MaterialWeighQuery query);
}
