package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 出场单模板图片Form
 * <AUTHOR>
 * @since 2022/6/28 14:11
 */
@Data
public class LeaveTemplateImageForm {

    @ApiModelProperty(value = "模板名")
    @NotBlank(message = "模板名不能为空")
    private String templateName;

    @ApiModelProperty(value = "模板图片uuid")
    @NotBlank(message = "uuid不能为空")
    private String templateImage;

    @ApiModelProperty(value = "模板类型 0 出场单 1 称重单")
    private Byte templateType;
}
