package cn.pinming.microservice.material.management.infrastructure.sdk;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.exception.BOExceptionEnum;
import cn.pinming.material.v2.model.dto.ConfirmResDTO;
import cn.pinming.material.v2.model.form.ConfirmBatchForm;
import cn.pinming.material.v2.model.form.PushConfirmForm;
import cn.pinming.material.v2.service.IDataConfirmService;
import cn.pinming.material.v2.util.SignUtil;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.management.dto.SdkConfigDTO;
import cn.pinming.microservice.base.management.service.ISdkConfService;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.util.ConfirmDataReceiveUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DataReceiveServiceImpl implements IDataConfirmService {
    @Resource
    private ConfirmDataReceiveUtil confirmDataReceiveUtil;
    @DubboReference
    private ISdkConfService sdkConfService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Override
    public List<ConfirmResDTO> receiveConfirmOrder(ConfirmBatchForm form) {
        Integer projectId = Integer.valueOf(form.getData().get(0).getProjectId());
        ProjectDTO projectDTO = projectServiceProxy.getProjectById(projectId);
        if (ObjectUtil.isNull(projectDTO)) {
            throw new BOException(BOExceptionEnum.BO_TASK_OP_FAIL_PROJECT_NO_EXIST.errorMsg());
        }
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(projectDTO.getCompanyId(),projectId);
        if (sdkConfig == null) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "appKey或appSecretKey不存在");
        }
        String sign = SignUtil.sign(sdkConfig.getAppKey(), sdkConfig.getAppSecretKey(), form.getTimestamp());
        if (!sign.equals(form.getSignature())) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "签名验证失败");
        }

        List<PushConfirmForm> data = form.getData();
        return confirmDataReceiveUtil.receiveConfirmData(data,projectDTO.getCompanyId(),projectId);
    }
}
