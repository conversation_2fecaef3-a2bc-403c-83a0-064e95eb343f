package cn.pinming.microservice.material.management.dubbo;

import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.service.IMaterialReceiveService;
import cn.pinming.microservice.material.management.vo.MaterialDataStandardVO;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@DubboService
class MaterialReceiveServiceImpl implements IMaterialReceiveService {
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private MaterialSendReceiveMapper materialSendReceiveMapper;

    @Override
    public List<MaterialDataStandardVO> getDataByExtNo(List<String> extNoList) {
        return materialDataMapper.getDataByExtNo(extNoList);
    }

    @Override
    public List<MaterialDataStandardVO> getDataByIds(List<String> ids) {
        return materialDataMapper.getDataByIds(ids);
    }

    @Override
    public List<MaterialDataStandardVO> getDataByPurchaseId(String purchaseOrderId) {
        return materialDataMapper.getDataByPurchaseId(purchaseOrderId);
    }

    @Override
    public List<String> getReceiveIdByExtNo(Integer projectId, String extNo) {
        return materialSendReceiveMapper.selectReceiveIdByExtNo(projectId, extNo);
    }
}