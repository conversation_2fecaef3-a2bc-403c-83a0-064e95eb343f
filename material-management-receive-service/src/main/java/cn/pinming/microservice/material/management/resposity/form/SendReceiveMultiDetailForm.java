package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class SendReceiveMultiDetailForm {
    @ApiModelProperty(value = "明细id")
    private String id;

    // 材料信息
    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Integer supplierId;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "材料ID")
    @NotNull(message = "材料ID不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "结算单位")
    @NotBlank(message = "结算单位不能为空")
    private String weightUnit;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    // 重量信息
    @ApiModelProperty(value = "随车面单数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "转换系数")
    @NotNull(message = "转换系数不能为空")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实称换算数量 实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "定尺长度")
    private BigDecimal length;

    @ApiModelProperty(value = "实点根数")
    private BigDecimal root;

    @ApiModelProperty("材料库理论值")
    private BigDecimal materialTheoreticalWeight;

    @ApiModelProperty(value = "理论重量")
    private BigDecimal theoreticalWeight;

    @ApiModelProperty(value = "实理偏差")
    private BigDecimal actualDeviation;

    @ApiModelProperty(value = "实理偏差率")
    private BigDecimal actualDeviationRate;

    @ApiModelProperty(value = "实称重量")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "面单偏差量")
    private BigDecimal sendDeviation;

    @ApiModelProperty(value = "面单偏差率")
    private BigDecimal sendDeviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    // 其他
    private Long wbsId;

    @ApiModelProperty("选择类型 1 钢筋 2 其他非理重")
    private Integer type;

    @ApiModelProperty(value = "品牌要求")
    private String brand;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("是否计算偏差  0 否 1 是")
    private Integer deviationCalculate;
}
