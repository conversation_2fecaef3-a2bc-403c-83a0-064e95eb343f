package cn.pinming.microservice.material.management.service.save;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.DistributionEnum;
import cn.pinming.microservice.material.management.infrastructure.listener.WeighDataListener;
import cn.pinming.microservice.material.management.infrastructure.util.*;
import cn.pinming.microservice.material.management.resposity.entity.MaterialDataMulti;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceiveMulti;
import cn.pinming.microservice.material.management.resposity.form.SendReceiveMultiDetailForm;
import cn.pinming.microservice.material.management.resposity.form.SendReceiveMultiForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialSendReceiveMultiMapper;
import cn.pinming.microservice.material.management.resposity.query.MultiQuery;
import cn.pinming.microservice.material.management.resposity.vo.MultiDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.MultiVO;
import cn.pinming.microservice.material.management.service.IMaterialDataMultiService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收货/发货单一车多料 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
public class MaterialSendReceiveMultiServiceImpl extends ServiceImpl<MaterialSendReceiveMultiMapper, MaterialSendReceiveMulti> implements IMaterialSendReceiveMultiService {
    @Resource
    private NoUtil noUtil;
    @Resource
    private UserUtil userUtil;
    @Resource
    private PicUtil picUtil;
    @Resource
    private IMaterialDataMultiService materialDataMultiService;
    @Resource
    private MaterialSendReceiveMultiMapper materialSendReceiveMultiMapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private SupplierServiceProxy supplierServiceProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private PicEchoUtil picEchoUtil;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private WeighDuplicationUtil weighDuplicationUtil;
    @DubboReference
    private IMaterialContractService materialContractService;
    @Resource
    private WeighDataListener weighDataListener;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(SendReceiveMultiForm form) {
        weighDuplicationUtil.judge(Arrays.asList(form.getRecordId1(),form.getRecordId2()));
        MaterialSendReceiveMulti sendReceive = new MaterialSendReceiveMulti();
        BeanUtils.copyProperties(form, sendReceive);
        sendReceive.setId(UUIDUtil.randomUUIDWithoutConnector());
        // 编号
        sendReceive.setReceiveNo(noUtil.getMulReceiveNo(userUtil.getProjectId()));
        // 净重
        sendReceive.setWeightNet(NumberUtil.sub(sendReceive.getWeightGross(), sendReceive.getWeightTare()));
        // 实重
        sendReceive.setWeightActual(NumberUtil.mul(NumberUtil.sub(1, NumberUtil.div(form.getMoistureContent(), 100)), NumberUtil.sub(form.getWeightNet(), form.getWeightDeduct())));
        // 照片
        sendReceive.setDocumentPic(picUtil.getUrlByBase64S(form.getDocumentPic()));
        sendReceive.setEnterPic(picUtil.getUrlByUrls(form.getEnterPic()));
        sendReceive.setLeavePic(picUtil.getUrlByUrls(form.getLeavePic()));
        // 处理明细
        List<MaterialDataMulti> dataMultiList = handle(sendReceive, form);
        long negative = dataMultiList.stream().filter(e -> e.getDeviationStatus() != null && e.getDeviationStatus() == 1).count();
        long positive = dataMultiList.stream().filter(e -> e.getDeviationStatus() != null && e.getDeviationStatus() == 2).count();
        if (negative > 0) {
            sendReceive.setDeviationStatus(1);
        }
        if (negative == 0 && positive > 0) {
            sendReceive.setDeviationStatus(2);
        }
        if (negative == 0 && positive == 0) {
            sendReceive.setDeviationStatus(0);
        }

        this.save(sendReceive);
        materialDataMultiService.saveBatch(dataMultiList);

        // 内部推送
        weighDataListener.AfterCompletion(null,sendReceive.getId(),2);

        return sendReceive.getId();
    }

    @Override
    public Page<MultiVO> pageByQuery(MultiQuery query) {
        query.setCompanyId(userUtil.getCompanyId());
        query.setProjectId(userUtil.getProjectId());
        if (userUtil.getDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(userUtil.getCompanyId(), userUtil.getDepartmentId()));
        }

        Page<MultiVO> page = materialSendReceiveMultiMapper.pageByQuery(query);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<MultiVO> list = page.getRecords();

            // 收料人
            Map<String, String> memberMap = new HashMap<>();
            List<String> createIdList = list.stream().map(MultiVO::getCreateId).distinct().collect(Collectors.toList());
            List<EmployeeSimpleDTO> employeeSimpleDTOS = employeeServiceProxy.employeeList(userUtil.getCompanyId(), createIdList);
            if (CollUtil.isNotEmpty(employeeSimpleDTOS)) {
                memberMap = employeeSimpleDTOS.stream().collect(Collectors.toMap(EmployeeSimpleDTO::getMemberId, EmployeeSimpleDTO::getMemberName));
            }
            // 收料明细
            Map<Integer, String> categoryMap = new HashMap<>();
            Map<Integer, String> supplierMap = new HashMap<>();
            List<String> receiveIdLIst = list.stream().map(MaterialSendReceiveMulti::getId).collect(Collectors.toList());
            List<MaterialDataMulti> dataList = materialDataMultiService.lambdaQuery()
                    .in(MaterialDataMulti::getReceiveId, receiveIdLIst)
                    .list();
            List<Integer> materialList = dataList.stream().map(MaterialDataMulti::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                categoryMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialCategoryName));
            }
            List<Integer> supplierList = dataList.stream().map(MaterialDataMulti::getSupplierId).distinct().collect(Collectors.toList());
            List<SupplierDTO> supplierDTOS = supplierServiceProxy.findListByIds(userUtil.getCompanyId(), supplierList);
            if (CollUtil.isNotEmpty(supplierDTOS)) {
                supplierMap = supplierDTOS.stream().collect(Collectors.toMap(SupplierDTO::getId, SupplierDTO::getName));
            }
            Map<String, List<MaterialDataMulti>> dataMap = dataList.stream().collect(Collectors.groupingBy(MaterialDataMulti::getReceiveId));

            Map<String, String> finalMemberMap = memberMap;
            Map<Integer, String> finalSupplierMap = supplierMap;
            Map<Integer, String> finalCategoryMap = categoryMap;
            list.forEach(e -> {
                if (CollUtil.isNotEmpty(finalMemberMap) && ObjectUtil.isNotNull(finalMemberMap.get(e.getCreateId()))) {
                    e.setReceiver(finalMemberMap.get(e.getCreateId()));
                }

                List<MaterialDataMulti> data = dataMap.get(e.getId());
                MaterialDataMulti materialDataMulti = data.get(0);
                if (CollUtil.isNotEmpty(finalSupplierMap) && ObjectUtil.isNotNull(finalSupplierMap.get(materialDataMulti.getSupplierId()))) {
                    e.setSupplierName(finalSupplierMap.get(materialDataMulti.getSupplierId()));
                }
                if (CollUtil.isNotEmpty(finalCategoryMap)) {
                    String categoryName = data.stream().map(item -> {
                        return finalCategoryMap.get(item.getMaterialId());
                    }).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
                    e.setCategoryName(categoryName);
                }
            });
        }

        return page;
    }

    @Override
    public MultiVO detail(String sendReceiveId) {
        MaterialSendReceiveMulti sendReceive = this.lambdaQuery()
                .eq(MaterialSendReceiveMulti::getId, sendReceiveId)
                .one();
        List<MaterialDataMulti> dataList = materialDataMultiService.lambdaQuery()
                .eq(MaterialDataMulti::getReceiveId, sendReceiveId)
                .list();

        MultiVO vo = new MultiVO();
        BeanUtils.copyProperties(sendReceive, vo);
        //处理图片
        vo.setEnterPics(picEchoUtil.echo(vo.getEnterPic(), 4));
        vo.setLeavePics(picEchoUtil.echo(vo.getLeavePic(), 4));
        vo.setDocumentPics(picEchoUtil.echo(vo.getDocumentPic(), -1));
        // 收料人
        EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(userUtil.getCompanyId(), vo.getCreateId());
        if (ObjectUtil.isNotNull(employee)) {
            vo.setReceiver(employee.getMemberName());
        }

        Map<Integer, MaterialDto> materialDtoMap = new HashMap<>();
        Map<Integer, String> supplierMap = new HashMap<>();
        Map<String, Integer> contractDetailMap = new HashMap<>();
        List<Integer> materialIdList = dataList.stream().map(MaterialDataMulti::getMaterialId).distinct().collect(Collectors.toList());
        List<Integer> supplierIdList = dataList.stream().map(MaterialDataMulti::getSupplierId).distinct().collect(Collectors.toList());
        List<String> contractDetailIdList = dataList.stream().map(MaterialDataMulti::getContractDetailId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(contractDetailIdList)) {
            List<SimpleContractDetailDTO> simpleContractDetailDTOS = materialContractService.querySimpleContractDetails(contractDetailIdList);
            if (CollUtil.isNotEmpty(simpleContractDetailDTOS)) {
                contractDetailMap = simpleContractDetailDTOS.stream().collect(Collectors.toMap(SimpleContractDetailDTO::getContractDetailId, SimpleContractDetailDTO::getDeviationCalculate));
            }
        }
        List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
        List<SupplierDTO> supplierDTOS = supplierServiceProxy.findListByIds(userUtil.getCompanyId(), supplierIdList);
        if (CollUtil.isNotEmpty(supplierDTOS)) {
            supplierMap = supplierDTOS.stream().collect(Collectors.toMap(SupplierDTO::getId, SupplierDTO::getName));
        }
        if (CollUtil.isNotEmpty(materialDtos)) {
            materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
        }

        Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
        Map<Integer, String> finalSupplierMap = supplierMap;
        Map<String, Integer> finalContractDetailMap = contractDetailMap;
        List<MultiDetailVO> detailVOList = dataList.stream().map(e -> {
            MultiDetailVO detailVO = new MultiDetailVO();
            BeanUtils.copyProperties(e, detailVO);
            detailVO.setPurchaseOrderId(detailVO.getPurchaseId());
            detailVO.setMaterialTheoreticalWeight(NumberUtil.mul(e.getMaterialTheoreticalWeight(),1000));
            if (CollUtil.isNotEmpty(finalMaterialDtoMap) && ObjectUtil.isNotNull(finalMaterialDtoMap.get(e.getMaterialId()))) {
                MaterialDto materialDto = finalMaterialDtoMap.get(e.getMaterialId());
                detailVO.setMaterialName(materialDto.getMaterialName());
                detailVO.setMaterialSpec(materialDto.getMaterialSpec());
            }
            if (CollUtil.isNotEmpty(finalSupplierMap) && ObjectUtil.isNotNull(finalSupplierMap.get(e.getSupplierId()))) {
                detailVO.setSupplierName(finalSupplierMap.get(e.getSupplierId()));
            }
            if (CollUtil.isNotEmpty(finalContractDetailMap) && ObjectUtil.isNotNull(finalContractDetailMap.get(e.getContractDetailId()))) {
                detailVO.setDeviationCalculate(finalContractDetailMap.get(e.getContractDetailId()));
            }
            return detailVO;
        }).collect(Collectors.toList());
        vo.setList(detailVOList);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void detailUpdate(SendReceiveMultiForm form) {
        MaterialSendReceiveMulti sendReceiveMulti = new MaterialSendReceiveMulti();
        BeanUtils.copyProperties(form, sendReceiveMulti);
        sendReceiveMulti.setPushState(0);

        List<String> updateId = form.getList().stream().map(SendReceiveMultiDetailForm::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<MaterialDataMulti> existId = materialDataMultiService.lambdaQuery()
                .select(MaterialDataMulti::getId)
                .eq(MaterialDataMulti::getReceiveId, sendReceiveMulti.getId())
                .list();
        if (CollUtil.isNotEmpty(updateId)) {
            List<MaterialDataMulti> exclude = existId.stream().filter(e -> !updateId.contains(e.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(exclude)) {
                materialDataMultiService.lambdaUpdate()
                        .in(MaterialDataMulti::getId, exclude.stream().map(MaterialDataMulti::getId).collect(Collectors.toList()))
                        .set(MaterialDataMulti::getIsDeleted, 1)
                        .update();
            }
        }else {
            materialDataMultiService.lambdaUpdate()
                    .eq(MaterialDataMulti::getReceiveId, sendReceiveMulti.getId())
                    .set(MaterialDataMulti::getIsDeleted, 1)
                    .update();
        }
        List<MaterialDataMulti> handle = handle(sendReceiveMulti, form);
        long negative = handle.stream().filter(e -> e.getDeviationStatus() != null && e.getDeviationStatus() == 1).count();
        long positive = handle.stream().filter(e -> e.getDeviationStatus() != null && e.getDeviationStatus() == 2).count();
        if (negative > 0) {
            sendReceiveMulti.setDeviationStatus(1);
        }
        if (negative == 0 && positive > 0) {
            sendReceiveMulti.setDeviationStatus(2);
        }
        if (negative == 0 && positive == 0) {
            sendReceiveMulti.setDeviationStatus(0);
        }
        this.updateById(sendReceiveMulti);
        materialDataMultiService.saveOrUpdateBatch(handle);

        // 内部推送
        weighDataListener.AfterCompletion(null,sendReceiveMulti.getId(),2);
    }

    private List<MaterialDataMulti> handle(MaterialSendReceiveMulti sendReceive,SendReceiveMultiForm form) {
        BigDecimal theoreticalWeightAll;
        List<SendReceiveMultiDetailForm> list = form.getList();
        Map<Integer, MaterialDto> materialMap = new HashMap<>();
        BigDecimal weightSendAll = list.stream().map(SendReceiveMultiDetailForm::getWeightSend).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<SendReceiveMultiDetailForm> listWithTheoretical = list.stream().filter(e -> ObjectUtil.isNotNull(e.getTheoreticalWeight())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithTheoretical)) {
            theoreticalWeightAll = listWithTheoretical.stream().map(SendReceiveMultiDetailForm::getTheoreticalWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            theoreticalWeightAll = BigDecimal.ZERO;
        }

        Set<Integer> materialIdList = list.stream().map(SendReceiveMultiDetailForm::getMaterialId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(materialIdList)) {
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }
        }
        Map<Integer, MaterialDto> finalMaterialMap = materialMap;
        List<MaterialDataMulti> dataMultiList = list.stream().map(e -> {
            MaterialDataMulti data = new MaterialDataMulti();
            BeanUtils.copyProperties(e, data);
            data.setPushState(0);
            data.setDeviationStatus(null);
            data.setPurchaseId(e.getPurchaseOrderId());
            data.setReceiveId(sendReceive.getId());
            // 理论重量 = 待定列 * 实点根数 * (材料库理重(kg)/1000)
            data.setMaterialTheoreticalWeight(NumberUtil.div(data.getMaterialTheoreticalWeight(), 1000));
            data.setTheoreticalWeight(NumberUtil.mul(data.getRoot(), NumberUtil.mul(data.getMaterialTheoreticalWeight(), data.getLength())));
            if (CollUtil.isNotEmpty(finalMaterialMap) && finalMaterialMap.get(e.getMaterialId()) != null) {
                data.setCategoryId(finalMaterialMap.get(e.getMaterialId()).getMaterialCategoryId());
                data.setCategoryName(finalMaterialMap.get(e.getMaterialId()).getMaterialCategoryName());
            }
            // 实称重量
            if (form.getDistribution().equals(DistributionEnum.ONE.value()) && weightSendAll.compareTo(BigDecimal.ZERO) > 0) {
                // 按随车面单量比例分配 总实重 * 对应物料随车面单量 / 总随车面单量
                data.setWeightActual(NumberUtil.mul(sendReceive.getWeightActual(), NumberUtil.div(data.getWeightSend(), weightSendAll)));
            } else if (form.getDistribution().equals(DistributionEnum.TWO.value()) && theoreticalWeightAll.compareTo(BigDecimal.ZERO) > 0) {
                // 按理论重量量比例分配 总实重 * 对应物料理论重量占所有理论重量总和的比例
                data.setWeightActual(NumberUtil.mul(sendReceive.getWeightActual(), NumberUtil.div(data.getTheoreticalWeight(), theoreticalWeightAll)));
            } else {
                data.setWeightActual(BigDecimal.ZERO);
            }
            // 实称换算数量 = 实称重量 / 转换系数
            if (data.getRatio().compareTo(BigDecimal.ZERO) > 0) {
                data.setActualCount(NumberUtil.div(data.getWeightActual(), data.getRatio()));
            }
            // 面单偏差量 = 实称换算数量 - 面单量
            data.setSendDeviation(NumberUtil.sub(data.getActualCount(), data.getWeightSend()));
            // 面单偏差率 = 面单偏差量 / 面单量 * 100
            if (data.getWeightSend().compareTo(BigDecimal.ZERO) > 0) {
                data.setSendDeviationRate(NumberUtil.mul(NumberUtil.div(data.getSendDeviation(), data.getWeightSend()), 100));
            }
            // 实理偏差 = 实称重量 - 理论重量
            data.setActualDeviation(NumberUtil.sub(data.getWeightActual(), data.getTheoreticalWeight()));
            // 实理偏差率 = 实理偏差 / 理论重量 * 100
            if (ObjectUtil.isNotNull(data.getTheoreticalWeight()) && data.getTheoreticalWeight().compareTo(BigDecimal.ZERO) > 0) {
                data.setActualDeviationRate(NumberUtil.mul(NumberUtil.div(data.getActualDeviation(), data.getTheoreticalWeight()), 100));
            }
            // 偏差状态 + 实收数量
            BigDecimal actualReceive = e.getActualReceive();
            if (ObjectUtil.isNotNull(actualReceive)) {
                data.setActualReceive(actualReceive);
            }else {
                data.setActualReceive(data.getActualCount());
            }
            if (form.getType() == 1) {
                if (ObjectUtil.isNotNull(data.getDeviationCeiling()) && ObjectUtil.isNotNull(data.getDeviationFloor()) && ObjectUtil.isNotNull(e.getDeviationCalculate()) && e.getDeviationCalculate() == 1) {
                    // 偏差状态
                    int flag;
                    int flag1;
                    flag = data.getSendDeviationRate().compareTo(data.getDeviationFloor());
                    flag1 = data.getSendDeviationRate().compareTo(data.getDeviationCeiling());
                    if (flag < 0) {
                        data.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                    }
                    if (flag1 > 0) {
                        data.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                        if (ObjectUtil.isNull(actualReceive)) {
                            data.setActualReceive(data.getWeightSend());
                        }
                    }
                    if (flag >= 0 && flag1 <= 0) {
                        data.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                        if (ObjectUtil.isNull(actualReceive)) {
                            data.setActualReceive(data.getWeightSend());
                        }
                    }
                }
            }
            return data;
        }).collect(Collectors.toList());

        BigDecimal actualCountAll = dataMultiList.stream().map(MaterialDataMulti::getActualCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal sendAll = dataMultiList.stream().map(MaterialDataMulti::getWeightSend).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weightActualAll = dataMultiList.stream().map(MaterialDataMulti::getWeightActual).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal theoreticalAll = dataMultiList.stream().map(MaterialDataMulti::getTheoreticalWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        sendReceive.setSendDeviationAll(NumberUtil.sub(actualCountAll, sendAll));
        if (sendAll.compareTo(BigDecimal.ZERO) > 0) {
            sendReceive.setSendDeviationRateAll(NumberUtil.mul(NumberUtil.div(sendReceive.getSendDeviationAll(), sendAll), 100));
        }
        if (theoreticalAll.compareTo(BigDecimal.ZERO) > 0) {
            sendReceive.setTheoreticalDeviationAll(NumberUtil.sub(weightActualAll, theoreticalAll));
            sendReceive.setTheoreticalDeviationRateAll(NumberUtil.mul(NumberUtil.div(sendReceive.getTheoreticalDeviationAll(), theoreticalAll), 100));
        }

        return dataMultiList;
    }
}
