package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveOverviewHistogram {

    @ApiModelProperty(value = "名称")
    private String x;

    @ApiModelProperty(value = "面单量")
    private Double y1;

    @ApiModelProperty(value = "实际量")
    private Double y2;

    @ApiModelProperty(value = "实收量")
    private Double y3;

    @ApiModelProperty(value = "二级分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "材料ID")
    private Set<Integer> materialIds;

}
