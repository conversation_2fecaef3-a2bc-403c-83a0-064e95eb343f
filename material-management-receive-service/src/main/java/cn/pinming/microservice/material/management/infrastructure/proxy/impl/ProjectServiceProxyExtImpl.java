package cn.pinming.microservice.material.management.infrastructure.proxy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.infrastructure.proxy.ProjectServiceExtProxy;
import cn.pinming.microservice.material.management.resposity.entity.StatisticsProjectConfig;
import cn.pinming.microservice.material.management.resposity.vo.ProjectVO;
import cn.pinming.microservice.material.management.service.IStatisticsProjectConfigService;
import cn.pinming.v2.common.api.service.PlugService;
import cn.pinming.v2.company.api.dto.department.DepartmentDto;
import cn.pinming.v2.company.api.dto.department.DepartmentQueryDto;
import cn.pinming.v2.company.api.service.DepartmentService;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import cn.pinming.v2.project.api.dto.ConstructionProjectQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.v2.project.api.service.ConstructionProjectService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:45 下午
 */
@Slf4j
@Component
public class ProjectServiceProxyExtImpl implements ProjectServiceExtProxy {

    @DubboReference
    private ConstructionProjectService constructionProjectService;

    @DubboReference(parameters = {
            "return null"
    })
    private DepartmentService departmentService;

    @DubboReference
    private PlugService plugService;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IStatisticsProjectConfigService projectConfigService;


    @Override
    public List<ProjectVO> getSimpleProjects(@NotEmpty List<Integer> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        projectIds = projectIds.stream().distinct().collect(Collectors.toList());
        List<SimpleConstructionProjectDto> list = constructionProjectService.findProjectsByProjectIds(projectIds);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, ProjectVO.class);
    }


    @Override
    public ProjectVO getProjectById(@NotNull Integer projectId) {
        ConstructionProjectDto constructionProjectDto = constructionProjectService.projectDetail(projectId);
        ProjectVO vo = new ProjectVO();
        if (constructionProjectDto != null) {
            BeanUtils.copyProperties(constructionProjectDto, vo);
        }
        return vo;
    }

    /**
     * 先查询所选企业组织节点下所有项目，无项目则无数据，有项目则与配置的项目范围取交集，有交集则返回交集项目，没交集则返回所选节点下所有项目
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return
     */
    @Override
    public List<Integer> statisticsProjectIds(@NonNull Integer compId, Integer deptId) {
        // 组织树根节点
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        // 获取当前组织节点下的所有项目
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(2);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(param::setParentDepartmentId);
        log.info("ProjectServiceProxyImpl statisticsProjectIds findDepartmentListByParam param:{}", JSONObject.toJSONString(param));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        log.info("ProjectServiceProxyImpl statisticsProjectIds findDepartmentListByParam project id:{}",
                CollectionUtil.isNotEmpty(projectList) ? JSONObject.toJSONString(projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
        if (CollectionUtil.isNotEmpty(projectList)) {
            List<Integer> pjds = projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
            AuthUser currentUser = authUserHolder.getCurrentUser();
            if (ObjectUtil.isNotNull(currentUser)) {
                String userid = currentUser.getId();
                QueryWrapper<StatisticsProjectConfig> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
                StatisticsProjectConfig config = projectConfigService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(config) && StringUtils.isNotBlank(config.getBelongProjectId())
                        && StringUtils.isNotBlank(config.getStatus())) {
                    String belongProjectId = config.getBelongProjectId();
                    String status = config.getStatus();
                    // 查询项目状态
                    ConstructionProjectQueryDto query = new ConstructionProjectQueryDto();
                    query.setDisableLogo(true);
                    query.setProjectIds(Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                    List<ConstructionProjectDto> dtoList = constructionProjectService.findProjectListWithOutPage(query);
                    // 过滤项目状态
                    List<String> statusList = Arrays.stream(status.split(",")).collect(Collectors.toList());
                    List<Integer> projectIds = dtoList.stream().filter(item ->
                                    ObjectUtil.isNotNull(item.getStatus()) && statusList.contains(item.getStatus().toString()))
                            .map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
                    // 交集
                    List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(projectIds, pjds);
                    return CollectionUtil.isNotEmpty(intersection) ? intersection : pjds;
                }
                return pjds;
            }
        }
        return Collections.singletonList(-1);
    }

    /**
     * 直属下级单位：
     * 直属下级包含项目和企业
     * 1. 项目：与配置的项目范围取交集，有交集则返回交集项目，没交集则pass
     * 2. 企业：先取企业下所有项目，与配置的项目范围取交集，有交集则返回交集项目，没交集则pass
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return
     */
    @Override
    public Map<String, List<Integer>> directlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId) {
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        Map<String, List<Integer>> resultMap = new HashMap<>();
        // 项目范围配置项目ID
        List<Integer> scopeProjectIds = Lists.newArrayList();
        // 项目范围配置
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsProjectConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
            StatisticsProjectConfig config = projectConfigService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(config) && StringUtils.isNotBlank(config.getBelongProjectId())
                    && StringUtils.isNotBlank(config.getStatus())) {
                String belongProjectId = config.getBelongProjectId();
                String status = config.getStatus();
                // 查询项目状态
                ConstructionProjectQueryDto query = new ConstructionProjectQueryDto();
                query.setDisableLogo(true);
                query.setProjectIds(Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                List<ConstructionProjectDto> dtoList = constructionProjectService.findProjectListWithOutPage(query);
                // 过滤项目状态
                if (CollectionUtil.isNotEmpty(dtoList)) {
                    List<String> statusList = Arrays.stream(status.split(",")).collect(Collectors.toList());
                    scopeProjectIds = dtoList.stream().filter(item ->
                                    ObjectUtil.isNotNull(item.getStatus()) && statusList.contains(item.getStatus().toString()))
                            .map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
                }
            }
        }
        // 获取当前组织节点下直属下级单位(分公司或项目)
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(1);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 1);
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(param::setParentDepartmentId);
        log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject param:{}", JSONObject.toJSONString(param));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject project id:{}",
                CollectionUtil.isNotEmpty(projectList) ? JSONObject.toJSONString(projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
        if (CollectionUtil.isNotEmpty(projectList)) {
            Map<Boolean, List<DepartmentDto>> deptMap = projectList.stream().collect(Collectors.partitioningBy(item -> 1 == item.getType()));
            // 过滤出分公司
            List<DepartmentDto> departmentDtos = deptMap.getOrDefault(true, Lists.newArrayList());
            // 过滤出项目
            List<DepartmentDto> projects = deptMap.getOrDefault(false, Lists.newArrayList());
            // 项目
            if (CollectionUtil.isNotEmpty(projects)) {
                List<Integer> projectIds = projects.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                // 交集
                List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(scopeProjectIds, projectIds);
                if (CollectionUtil.isNotEmpty(intersection)) {
                    List<DepartmentDto> dtoList = projects.stream().filter(project -> intersection.contains(project.getProjectId())).collect(Collectors.toList());
                    for (DepartmentDto departmentDto : dtoList) {
                        Integer projectId = departmentDto.getProjectId();
                        String key = projectId + "-" + departmentDto.getDepartmentName();
                        resultMap.put(key, Collections.singletonList(projectId));
                    }
                }
            }
            // 分公司
            if (CollectionUtil.isNotEmpty(departmentDtos)) {
                List<Pair<Integer, Integer>> compDeptIds = departmentDtos.stream().map(item -> Pair.of(item.getCompanyId(), item.getDepartmentId())).collect(Collectors.toList());
                Map<Integer, String> departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getDepartmentName));
                for (Pair<Integer, Integer> pair : compDeptIds) {
                    Integer cId = pair.getKey();
                    Integer dId = pair.getValue();
                    Map<String, List<Integer>> map = new HashMap<>();
                    String key = dId + "-" + departmentMap.get(dId);
                    map.put(key, Lists.newArrayList());
                    // 获取当前组织节点下的所有项目
                    DepartmentQueryDto queryDto = new DepartmentQueryDto();
                    queryDto.setCompanyId(cId);
                    queryDto.setDepartmentDepth(2);
                    List<Byte> queryTypes = Lists.newArrayList();
                    queryTypes.add((byte) 3);
                    queryDto.setTypeList(queryTypes);
                    // 部门ID
                    queryDto.setParentDepartmentId(dId);
                    log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject child company param:{}", JSONObject.toJSONString(queryDto));
                    List<DepartmentDto> dtoList = departmentService.findDepartmentListByParam(queryDto);
                    log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject child company project id:{}",
                            CollectionUtil.isNotEmpty(dtoList) ? JSONObject.toJSONString(dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
                    if (CollectionUtil.isNotEmpty(dtoList)) {
                        List<Integer> companyProjectIds = dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                        // 交集
                        List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(scopeProjectIds, companyProjectIds);
                        if (CollectionUtil.isNotEmpty(intersection)) {
                            resultMap.put(key, intersection);
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<Integer> statisticsDeptProjectIds(@NotNull Integer compId, Integer deptId) {
        // 组织树根节点
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        // 获取当前组织节点下的所有项目
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(2);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(param::setParentDepartmentId);
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        if (CollectionUtil.isNotEmpty(projectList)) {
            List<Integer> pjds = projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
            return pjds;
        }
        return Lists.newArrayList();
    }

    @Override
    public Map<String, List<Integer>> allDirectlyUnderDeptOrProject(@NotNull Integer compId, Integer deptId) {
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        Map<String, List<Integer>> resultMap = new HashMap<>();
        // 获取当前组织节点下直属下级单位(分公司或项目)
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(1);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 1);
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(param::setParentDepartmentId);
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        if (CollectionUtil.isNotEmpty(projectList)) {
            Map<Boolean, List<DepartmentDto>> deptMap = projectList.stream().collect(Collectors.partitioningBy(item -> 1 == item.getType()));
            // 过滤出分公司
            List<DepartmentDto> departmentDtos = deptMap.getOrDefault(true, Lists.newArrayList());
            // 过滤出项目
            List<DepartmentDto> projects = deptMap.getOrDefault(false, Lists.newArrayList());
            // 项目
            if (CollectionUtil.isNotEmpty(projects)) {
                for (DepartmentDto item : projects) {
                    Integer projectId = item.getProjectId();
                    List<Integer> integers = Collections.singletonList(projectId);
                    String key = projectId + "-" + item.getDepartmentName();
                    resultMap.put(key, integers);
                }
            }
            // 分公司
            if (CollectionUtil.isNotEmpty(departmentDtos)) {
                List<Pair<Integer, Integer>> compDeptIds = departmentDtos.stream().map(item -> Pair.of(item.getCompanyId(), item.getDepartmentId())).collect(Collectors.toList());
                Map<Integer, String> departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getDepartmentName));
                for (Pair<Integer, Integer> pair : compDeptIds) {
                    Integer cId = pair.getKey();
                    Integer dId = pair.getValue();
                    Map<String, List<Integer>> map = new HashMap<>();
                    String key = dId + "-" + departmentMap.get(dId);
                    map.put(key, Lists.newArrayList());
                    // 获取当前组织节点下的所有项目
                    DepartmentQueryDto queryDto = new DepartmentQueryDto();
                    queryDto.setCompanyId(cId);
                    queryDto.setDepartmentDepth(2);
                    List<Byte> queryTypes = Lists.newArrayList();
                    queryTypes.add((byte) 3);
                    queryDto.setTypeList(queryTypes);
                    // 部门ID
                    queryDto.setParentDepartmentId(dId);
                    List<DepartmentDto> dtoList = departmentService.findDepartmentListByParam(queryDto);
                    if (CollectionUtil.isNotEmpty(dtoList)) {
                        List<Integer> companyProjectIds = dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                        resultMap.put(key, companyProjectIds);
                    }
                }
            }
        }
        return resultMap;
    }
}
