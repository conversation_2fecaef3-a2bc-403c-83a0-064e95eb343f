package cn.pinming.microservice.material.management.resposity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/1/17 13:29
 */
@Data
public class WarningInfoDTO {

    @ApiModelProperty(value = "预警id")
    private String warningId;

    @ApiModelProperty(value = "预警来源记录编号")
    private String warningSourceNo;

    @ApiModelProperty(value = "预警来源")
    private String warningSource;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "预警时间")
    private LocalDateTime warningTime;

    @ApiModelProperty(value = "预警来源记录id")
    private String warningSourceId;

    @ApiModelProperty(value = "处理人姓名")
    private String handlerName;

    @ApiModelProperty(value = "处理意见")
    private String handlerAdvice;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handlerTime;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    private Byte warningStatus;

    @ApiModelProperty(value = "来源项目id")
    private Integer sourceProjectId;
}
