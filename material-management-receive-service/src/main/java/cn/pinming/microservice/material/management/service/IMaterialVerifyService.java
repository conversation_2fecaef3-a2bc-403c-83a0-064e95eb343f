package cn.pinming.microservice.material.management.service;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.infrastructure.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.resposity.entity.MaterialVerify;
import cn.pinming.microservice.material.management.resposity.form.MaterialVerifyForm;
import cn.pinming.microservice.material.management.resposity.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialVerifyDataListVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialVerifyStatisticsVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialVerifyVO;
import cn.pinming.microservice.material.management.resposity.vo.VerifyPersonVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 物料对账表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
public interface IMaterialVerifyService extends IService<MaterialVerify> {
    IPage<MaterialVerifyVO> listMaterialVerify(MaterialVerifyQuery materialVerifyQuery);

    String saveVerify(MaterialVerifyForm form);

    MaterialVerifyVO detail(String verifyId);

    void materialFile(String id);

    void removeMaterialVerifyById(String id);

    List<VerifyPersonVO> listVerifyPerson();

    MaterialVerifyDataListVO dataList(MaterialWeighQuery query,Byte  verifyType);

    void delete(String verifyId, String id);
}
