package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WeighDeviationDetailVO extends SimpleSupplierDTO {
    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "收货物料id")
    private Integer materialId;

    @ApiModelProperty(value = "收货物料")
    private String materialName;

    @ApiModelProperty(value = "面单应收数量")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "实际收料")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty("实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviation;

    @ApiModelProperty(value = "偏差量")
    private BigDecimal deviationCount;

    @ApiModelProperty(value = "收料方式")
    private Byte receiveType;

    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

}
