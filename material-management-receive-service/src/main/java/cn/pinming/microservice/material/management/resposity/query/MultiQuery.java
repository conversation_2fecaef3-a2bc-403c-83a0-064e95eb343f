package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MultiQuery extends BaseQuery{
    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "过磅单号")
    private String receiveNo;

    @ApiModelProperty(value = "类型，1：收货；2：发货")
    private Integer type;

    @ApiModelProperty("0.未推送 1.已推送 2.推送失败")
    private Integer pushState;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
}
