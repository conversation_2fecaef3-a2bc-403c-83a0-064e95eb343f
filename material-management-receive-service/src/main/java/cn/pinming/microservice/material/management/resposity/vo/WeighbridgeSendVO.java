package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighbridgeSendVO {

    @ApiModelProperty(value = "明细id")
    @ExcelIgnore
    private String id;

    @ApiModelProperty(value = "发货id")
    @ExcelIgnore
    private String sendId;

    @ApiModelProperty(value = "发货单号")
    @ExcelProperty("发货单号")
    private String sendNo;

    @ApiModelProperty(value = "车牌号")
    @ExcelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "发货单位")
    @ExcelProperty("发货单位")
    private String sendProject;

    @ApiModelProperty(value = "收货单位")
    @ExcelProperty("收货单位")
    private String receiveProject;

    @ApiModelProperty(value = "物资名称")
    @ExcelProperty("物资名称")
    private String materialName;

    @ApiModelProperty(value = "品种及规格")
    @ExcelProperty("品种及规格")
    private String materialSpec;

    @ApiModelProperty(value = "毛重")
    @ExcelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    @ExcelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    @ExcelProperty("扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "实重")
    @ExcelProperty("实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "单位")
    @ExcelProperty("单位")
    private String weightUnit;

    @ApiModelProperty(value = "发货人")
    @ExcelProperty("发货人")
    private String sender;

    @ApiModelProperty(value = "毛皮完整性")
    @ExcelIgnore
    private Byte isWeightIntegrality;

    @ApiModelProperty(value = "发货时间")
    @ExcelProperty("发货时间")
    @ColumnWidth(25)
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    @ExcelIgnore
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    @ExcelIgnore
    private String extNo;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    @ExcelIgnore
    private Byte isAddition;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    @ExcelProperty("是否为手工补单")
    private String isAdditionStr;

    @ExcelIgnore
    @ApiModelProperty("分类ID")
    private Integer categoryId;

    @ExcelIgnore
    @ApiModelProperty("分类名称")
    private String categoryName;
}




