package cn.pinming.microservice.material.management.infrastructure.exception;

public class BOException extends BaseRuntimeException {
    private static final long serialVersionUID = -8246635855921697781L;

    /**
     * BOExceptionEnum构造业务层异常
     */
    public BOException(BOExceptionEnum en) {
        super(en.errorCode(), en.errorMsg());
    }


    public BOException(String errorMsg) {
        super("", errorMsg);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码
     * @param errorMsg  错误信息
     */
    public BOException(String errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    /**
     * 抛出BOException异常
     *
     * @param en 异常枚举
     * @Description
     */
    public static void throwz(BOExceptionEnum en) {
        throw new BOException(en);
    }

    public boolean isSame(BOExceptionEnum en) {
        return this.getErrorCode().equals(en.errorCode()) &&
            this.getErrorMsg().equals(en.errorMsg());
    }
}
