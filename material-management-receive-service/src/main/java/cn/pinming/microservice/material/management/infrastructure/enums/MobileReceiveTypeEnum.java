package cn.pinming.microservice.material.management.infrastructure.enums;

public enum MobileReceiveTypeEnum {
    ONE((byte)1, "有合同收料-按合同"),
    TWO((byte)2, "有合同收料-按采购单"),
    THREE((byte)3,"无合同收料");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    MobileReceiveTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (MobileReceiveTypeEnum statusEnum : MobileReceiveTypeEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }

}
