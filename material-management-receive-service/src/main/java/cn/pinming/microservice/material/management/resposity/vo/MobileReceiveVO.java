package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MobileReceiveVO {
    @ApiModelProperty(value = "收货单id")
    private String id;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "收料时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "进场状态")
    private String receiveStatus;

}
