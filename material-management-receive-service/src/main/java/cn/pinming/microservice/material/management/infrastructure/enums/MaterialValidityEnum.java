package cn.pinming.microservice.material.management.infrastructure.enums;

public enum MaterialValidityEnum {
    VALID((byte)1, "有效"),
    INVALID((byte)2, "无效");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    MaterialValidityEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }


    public static boolean isMaterialValid(Byte value) {
        if (value == null) {
            return false;
        }
        if(VALID.value() == value.intValue()) {
            return true;
        }
        return false;
    }

}
