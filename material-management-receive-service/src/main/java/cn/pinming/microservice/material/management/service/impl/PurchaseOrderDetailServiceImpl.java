package cn.pinming.microservice.material.management.service.impl;

import cn.pinming.microservice.material.management.infrastructure.enums.ReceiveQueryTypeEnum;
import cn.pinming.microservice.material.management.resposity.dto.PurchaseOrderDetailDTO;
import cn.pinming.microservice.material.management.resposity.entity.PurchaseOrderDetail;
import cn.pinming.microservice.material.management.resposity.mapper.PurchaseOrderDetailMapper;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.GoodsForReviseVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.service.IPurchaseOrderDetailService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Service
public class PurchaseOrderDetailServiceImpl extends ServiceImpl<PurchaseOrderDetailMapper, PurchaseOrderDetail> implements IPurchaseOrderDetailService {

    @Override
    public List<PurchaseOrderDetailDTO> listOrderDetailById(String id) {
        return this.getBaseMapper().selectOrderDetailById(id);
    }

    @Override
    public BigDecimal getPurchaseAmountByQuery(SupplierAnalysisQuery query) {
        return this.getBaseMapper().selectPurchaseAmountByQuery(query);
    }

    @Override
    public List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query) {
        return this.getBaseMapper().selectSupplierAnalysisPageVO(query);
    }

    @Override
    public IPage<MaterialReceiveVO> selectSupplierAnalysisDetailPageVO(SupplierAnalysisQuery query) {
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            // 报备收料、临时收料
            return this.getBaseMapper().selectSupplierAnalysisDetailPageVO(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            // 移动收料 有合同
            return this.getBaseMapper().selectSupplierAnalysisDetailMobilePageVO(query);
        } else {
            // 全部
            return this.getBaseMapper().selectSupplierAnalysisDetailUnionPageVO(query);
        }

    }

    @Override
    public IPage<SupplierAnalysisDetailVO> selectSupplierPageVO(SupplierAnalysisQuery query) {
        return this.getBaseMapper().selectSupplierPageVO(query);
    }

    @Override
    public List<GoodsForReviseVO> listForRevise(String purchaseOrderId) {
        List<GoodsForReviseVO> list = this.getBaseMapper().listForRevise(purchaseOrderId);
        return list;
    }

    @Override
    public List<String> queryHistoryUsePartByProjectId(Integer projectId, String remark) {
        List<String> remakes = this.getBaseMapper().queryHistoryUsePartByProjectId(projectId, remark);
        return remakes;
    }
}
