package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据修订vo
 * <AUTHOR>
 * @Date 2022/3/8 16:18
 */
@Data
public class MaterialReviseDataVO {

    @ApiModelProperty(value = "修订id")
    private String reviseId;

    @ApiModelProperty(value = "收发货单id")
    private String receiveId;

    @ApiModelProperty(value = "修订人")
    private String reviseName;

    @ApiModelProperty(value = "修正项")
    private String reviseDetail;

    @ApiModelProperty(value = "修正说明")
    private String reviseRemark;

    @ApiModelProperty(value = "修正时间")
    private LocalDateTime reviseTime;
}
