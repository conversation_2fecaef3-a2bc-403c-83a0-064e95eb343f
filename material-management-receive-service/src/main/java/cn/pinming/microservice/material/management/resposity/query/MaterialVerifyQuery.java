package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialVerifyQuery extends BaseQuery {

    @ApiModelProperty(value = "批次编号")
    private String verifyNo;

    @ApiModelProperty(value = "报账项目")
    private Integer verifyProjectId;

    @ApiModelProperty(value = "对账人")
    private String verifyPerson;

    @ApiModelProperty(value = "对账状态 0 已归档 1 对账中")
    private Byte verifyStatus;
}
