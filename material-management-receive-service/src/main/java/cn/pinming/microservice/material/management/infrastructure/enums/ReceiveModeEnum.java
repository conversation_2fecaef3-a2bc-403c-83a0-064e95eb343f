package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 */

public enum ReceiveModeEnum {
    TEMPORARY((byte) 1, "临时收料"),
    REPORT((byte) 2, "报备收料"),
    UNBELOGN((byte) 3, "无归属收料"),
    INVALID((byte) 0, "无效收料"),
    ;

    /**
     * 状态值
     */
    private final byte   value;
    /**
     * 状态的描述
     */
    private final String description;

    ReceiveModeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ReceiveModeEnum getReceiveMode(Byte value, Byte materialValidity) {
        if (!MaterialValidityEnum.isMaterialValid(materialValidity)) {
            return INVALID;
        }
        if (value == null) {
            return INVALID;
        }
        for (ReceiveModeEnum receiveModeEnum : ReceiveModeEnum.values()) {
            if (receiveModeEnum.value() == value.intValue()) {
                return receiveModeEnum;
            }
        }
        return INVALID;
    }

    public static String desc(Byte value) {
        if (value == null) {
            return null;
        }
        for (ReceiveModeEnum statusEnum : ReceiveModeEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }
}
