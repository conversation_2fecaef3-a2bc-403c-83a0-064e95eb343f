package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MaterialWeighSendExportVO {
    @ApiModelProperty(value = "发货单号")
    @ExcelProperty("发货单号")
    private String receiveNo;

    @ApiModelProperty(value = "车牌号")
    @ExcelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "发货单位")
    @ExcelProperty("发货单位")
    private String projectTitle;

    @ApiModelProperty(value = "收货单位")
    @ExcelProperty("收货单位")
    private String supplierName;

    @ApiModelProperty(value = "品种及规格")
    @ExcelProperty("品种及规格")
    private String materialName;

    @ApiModelProperty(value = "物资名称")
    @ExcelProperty("物资名称")
    private String categoryName;

    @ApiModelProperty("毛重")
    @ExcelProperty("毛重")
    private BigDecimal gWeight;

    @ApiModelProperty("皮重")
    @ExcelProperty("皮重")
    private BigDecimal tWeight;;

    @ApiModelProperty(value = "实重")
    @ExcelProperty("实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "称重单位")
    @ExcelProperty("称重单位")
    private String unit = "吨";

    @ApiModelProperty(value = "称重确认人")
    @ExcelProperty("称重确认人")
    private String receiver;

    @ApiModelProperty(value = "发货时间")
    @ExcelProperty("发货时间")
    @ColumnWidth(25)
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "收料方式")
    @ExcelIgnore
    private String receiveType;

    @ApiModelProperty(value = "推送状态 0.未推送\\n\" +\n" +
            "            \"1.已推送\\n\" +\n" +
            "            \"2.推送失败")
    @ExcelProperty("推送状态")
    private String pushStateStr;
}
