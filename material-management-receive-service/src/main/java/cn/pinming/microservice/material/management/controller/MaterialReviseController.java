package cn.pinming.microservice.material.management.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.management.infrastructure.annotation.enums.BusinessType;
import cn.pinming.microservice.material.management.resposity.form.MaterialReviseForm;
import cn.pinming.microservice.material.management.resposity.form.WeighSendFixForm;
import cn.pinming.microservice.material.management.resposity.query.ReviseInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseVO;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.save.IMaterialReviseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 数据修订 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
@Api(tags = "数据修订-controller", value = "zh")
@RestController
@RequestMapping("api/biz/material-revise")
public class MaterialReviseController {
    @Resource
    private IMaterialReviseService materialReviseService;
    @Resource
    private UserUtil userUtil;
    @Resource
    private IMaterialDataService materialDataService;

    @ApiOperation(value = "修订数据")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@Validated @Valid @RequestBody MaterialReviseForm materialReviseForm) {
        materialReviseService.add(materialReviseForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "修订数据列表", response = MaterialReviseVO.class)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody ReviseInfoQuery ReviseInfoQuery) {
        MaterialReviseVO materialReviseVO =  materialReviseService.getMaterialRevise(ReviseInfoQuery);
        return ResponseEntity.ok(new SuccessResponse(materialReviseVO));
    }

    @ApiOperation(value = "数据修正详情页", response = MaterialReviseDetailVO.class)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody ReviseInfoQuery reviseInfoQuery) {
        reviseInfoQuery.setCompanyId(userUtil.getCompanyId());
        MaterialReviseDetailVO materialReviseDetailVO = materialReviseService.getMaterialReviseDetail(reviseInfoQuery);
        return ResponseEntity.ok(new SuccessResponse(materialReviseDetailVO));
    }

    @ApiOperation(value = "扫码历史-收发料", response = MaterialReviseDetailVO.class)
    @GetMapping("/mobile/history/{type}")
    public ResponseEntity<Response> mobileHistoryByType(@PathVariable("type")Byte type,@RequestParam(value = "receiveType",required = false)Byte receiveType) {
        MaterialReviseDetailVO vo = materialDataService.mobileHistoryByType(type,receiveType);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "发料修订")
    @PostMapping("/sendFix")
    public ResponseEntity<Response> sendFix(@RequestBody WeighSendFixForm form) {
        materialReviseService.sendFix(form);
        return ResponseEntity.ok(new SuccessResponse());
    }
}
