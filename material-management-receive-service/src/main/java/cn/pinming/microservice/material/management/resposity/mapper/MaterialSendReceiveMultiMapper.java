package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceiveMulti;
import cn.pinming.microservice.material.management.resposity.query.MultiQuery;
import cn.pinming.microservice.material.management.resposity.vo.MultiVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 收货/发货单一车多料 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface MaterialSendReceiveMultiMapper extends BaseMapper<MaterialSendReceiveMulti> {

    Page<MultiVO> pageByQuery(@Param("query") MultiQuery query);
}
