package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class WeighReceiveResultVO {

    @ApiModelProperty("文本结果")
    private String result;

    @ApiModelProperty("1提交成功 2修订成功 3修订失败 0跳转修订页面")
    private Byte type;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收货明细id")
    private String dataId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "面单应收量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;
//
//    @ApiModelProperty("实际数量")
//    private BigDecimal actualCount;
//
//    @ApiModelProperty(value = "偏差状态")
//    private Byte deviationStatus;
//
//    @ApiModelProperty(value = "实际偏差率")
//    private BigDecimal deviationRate;
//
//    @ApiModelProperty(value = "约定偏差阈值上限")
//    private BigDecimal deviationCeiling;
//
//    @ApiModelProperty(value = "约定偏差阈值下限")
//    private BigDecimal deviationFloor;
//
//    @ApiModelProperty(value = "单据照片")
//    private List<String> documentPics;

}
