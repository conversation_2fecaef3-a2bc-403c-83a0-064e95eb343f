package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.management.infrastructure.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WarningStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.management.resposity.dto.HandleAdviceDTO;
import cn.pinming.microservice.material.management.resposity.dto.WarningDetailDTO;
import cn.pinming.microservice.material.management.resposity.dto.WarningInfoDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarning;
import cn.pinming.microservice.material.management.resposity.entity.WarningHandlerAdvice;
import cn.pinming.microservice.material.management.resposity.form.HandleAdviceForm;
import cn.pinming.microservice.material.management.resposity.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialWarningMapper;
import cn.pinming.microservice.material.management.resposity.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.resposity.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.*;
import cn.pinming.microservice.material.management.service.*;
import cn.pinming.zhuang.api.company.service.IEmployeeService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警信息 服务实现类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Service
public class MaterialWarningServiceImpl extends ServiceImpl<MaterialWarningMapper, MaterialWarning> implements IMaterialWarningService {
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private IWarningHandlerAdviceService handlerAdviceService;
    @Resource
    private UserUtil userUtil;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    @Override
    public IPage<WarningInfoVO> pageListByQuery(WarningInfoQuery warningInfoQuery) {
        warningInfoQuery.setCompanyId(userUtil.getCompanyId());
        if (CollUtil.isEmpty(warningInfoQuery.getSourceProjectIds())) {
            warningInfoQuery.setSourceProjectIds(getCurProjectIdList());
        }

        IPage<WarningInfoDTO> warningInfoDTOPage = this.baseMapper.selectPageByQuery(warningInfoQuery);
        List<WarningInfoDTO> records = warningInfoDTOPage.getRecords();

        Boolean enable= materialHandlerService.enableHandle(userUtil.getMid(), HandleTypeEnum.WARNING_HANDLE.value());
        // 预警id列表
        List<String> warningIds = records.stream().map(WarningInfoDTO::getWarningId).collect(Collectors.toList());
        List<HandleAdviceDTO> lastHandlerAdvice = Optional.ofNullable(handlerAdviceService.getLastHandlerAdvice(warningIds)).orElse(Lists.newArrayList());
        Map<String, HandleAdviceDTO> adviceMap = lastHandlerAdvice.stream().collect(Collectors.toMap(HandleAdviceDTO::getWarningId, e -> e, (t1, t2) -> t2));

        // 预警来源项目列表
        List<Integer> projectIds = records.stream().map(WarningInfoDTO::getSourceProjectId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ProjectDTO> simpleProjects = projectServiceProxy.getSimpleProjects(projectIds);
        Map<Integer, ProjectDTO> projectVOMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(simpleProjects)) {
            projectVOMap = simpleProjects.stream().collect(Collectors.toMap(ProjectDTO::getProjectId, e -> e, (t1, t2) -> t1));
        }

        // 返回结果信息
        Map<Integer, ProjectDTO> finalProjectMap = projectVOMap;
        List<WarningInfoVO> result = records.stream().map(warningInfoDTO -> {
            WarningInfoVO warningInfoVO = new WarningInfoVO();
            BeanUtils.copyProperties(warningInfoDTO, warningInfoVO);

            warningInfoVO.setEnableHandle(enable);
            // 封装发生项目名称
            ProjectDTO projectVO = finalProjectMap.get(warningInfoDTO.getSourceProjectId());
            Optional.ofNullable(projectVO).ifPresent(project -> warningInfoVO.setSourceProject(project.getProjectTitle()));
            // 查询处理建议
            HandleAdviceDTO handleAdviceDTO = adviceMap.get(warningInfoDTO.getWarningId());
            Optional.ofNullable(handleAdviceDTO).ifPresent(handlerAdvice -> warningInfoVO.setHandlerAdvice(handlerAdvice.getAdvice()));
            // 预警类型
            String warningType = warningInfoDTO.getWarningType();
            if (StrUtil.isNotEmpty(warningType)) {
                warningInfoVO.setWarningType(Arrays.stream(warningType.split(StrUtil.COMMA)).map(Byte::new).collect(Collectors.toSet()));
            }

            String warningSourceId = warningInfoDTO.getWarningSourceId();
            WarningDetailQuery query = new WarningDetailQuery();
            query.setWarningSourceId(warningSourceId);
            List<WarningDetailVO> warningDetail = getWarningDetail(query);
            warningInfoVO.setWarningDetailList(warningDetail);
            return warningInfoVO;
        }).collect(Collectors.toList());

        IPage<WarningInfoVO> list = new Page<>();
        BeanUtils.copyProperties(warningInfoDTOPage, list);
        list.setRecords(result);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warningHandle(HandleAdviceForm handleAdviceForm) {
        List<String> warningSourceIdList = handleAdviceForm.getWarningSourceId();
        String handlerName;
        String handlerId = userUtil.getMid();
        EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(userUtil.getCompanyId(), userUtil.getMid());
        if (ObjectUtil.isNotNull(employee)) {
            handlerName = employee.getMemberName();
        } else {
            handlerName = null;
        }
        // 处理该编号下的所有预警
        List<MaterialWarning> materialWarningList = this.lambdaQuery().in(MaterialWarning::getWarningSourceId, warningSourceIdList).list();
        // 保存处理建议信息
        List<WarningHandlerAdvice> warningHandlerAdviceList = Lists.newArrayList();
        materialWarningList.forEach(materialWarning -> {
            WarningHandlerAdvice warningHandlerAdvice = new WarningHandlerAdvice();
            warningHandlerAdvice.setWarningId(materialWarning.getId());
            warningHandlerAdvice.setHandlerAdvice(handleAdviceForm.getHandlerAdvice());
            warningHandlerAdvice.setHandlerId(handlerId);
            warningHandlerAdvice.setHandlerName(handlerName);
            warningHandlerAdvice.setHandlerTime(LocalDateTime.now());
            warningHandlerAdvice.setWarningStatus(handleAdviceForm.getWarningStatus());
            warningHandlerAdviceList.add(warningHandlerAdvice);
        });
        handlerAdviceService.saveBatch(warningHandlerAdviceList);

        // 预警表更新建议信息
        List<MaterialWarning> updateMaterialWarningList = Lists.newArrayList();
        materialWarningList.forEach(materialWarning -> {
            materialWarning.setHandlerId(handlerId);
            materialWarning.setHandlerName(handlerName);
            materialWarning.setHandlerTime(LocalDateTime.now());
            materialWarning.setWarningStatus(handleAdviceForm.getWarningStatus());
            updateMaterialWarningList.add(materialWarning);
        });
        updateBatchById(updateMaterialWarningList);
    }

    @Override
    public void saveMaterialWarning(MaterialWarningForm materialWarningForm) {
        if (materialWarningForm.getWarningType() == null) {
            throw new BOException(BOExceptionEnum.WARNING_TYPE_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSource() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSourceId() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_ID_NOT_EMPTY);
        }

        if (materialWarningForm.getSourceProjectId() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_PROJECT_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSourceNo() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_NO_NOT_EMPTY);
        }
        MaterialWarning materialWarning = new MaterialWarning();
        BeanUtils.copyProperties(materialWarningForm, materialWarning);
        for (WarningSourceEnum v : WarningSourceEnum.values()) {
            if (materialWarningForm.getWarningSource().equals(v.value())) {
                materialWarning.setWarningSource(v.description());
            }
        }
        materialWarning.setWarningNo(noUtil.getWarningNo(materialWarningForm.getSourceProjectId()));
        materialWarning.setWarningStatus(WarningStatusEnum.UNHANDLED.value());
        save(materialWarning);
    }

    @Override
    public List<WarningStatusVO> listWarningStatus() {
        // 预警状态列表
        List<WarningStatusVO> warningStatusVOS = Arrays.stream(WarningStatusEnum.values()).map(v -> {
            WarningStatusVO warningStatusVO = new WarningStatusVO();
            warningStatusVO.setWarningStatus(v.value());
            warningStatusVO.setDescription(v.description());
            return warningStatusVO;
        }).collect(Collectors.toList());
        return warningStatusVOS;
    }

    @Override
    public List<WarningTypeVO> listWarningType() {
        // 预警类型列表
        List<WarningTypeVO> warningTypeVOList = Arrays.stream(WarningTypeEnum.values()).map(v -> {
            WarningTypeVO warningTypeVO = new WarningTypeVO();
            warningTypeVO.setWarningType(v.value());
            warningTypeVO.setDescription(v.description());
            return warningTypeVO;
        }).collect(Collectors.toList());
        return warningTypeVOList;
    }

    @Override
    public List<WarningSourceVO> listWarningSource() {
        List<WarningSourceVO> warningSourceVOS = Arrays.stream(WarningSourceEnum.values()).map(v -> {
            WarningSourceVO warningSourceVO = new WarningSourceVO();
            warningSourceVO.setWarningSource(v.description());
            return warningSourceVO;
        }).collect(Collectors.toList());
        return warningSourceVOS;
    }

    @Override
    public List<WarningDetailVO> getWarningDetail(WarningDetailQuery warningDetailQuery) {
        List<WarningDetailDTO> warningDetailDTOList = this.baseMapper.listWarningDetail(warningDetailQuery);
        List<WarningDetailVO> warningDetailVOList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(warningDetailDTOList)) {
            Map<Byte, List<String>> warningDetailMap = warningDetailDTOList.stream()
                    .collect(Collectors.groupingBy(WarningDetailDTO::getWarningType,
                            Collectors.mapping(WarningDetailDTO::getWarningInfo, Collectors.toList())));
            warningDetailMap.forEach((k, v) -> {
                WarningDetailVO warningDetailVO = new WarningDetailVO();
                warningDetailVO.setType(k);
                warningDetailVO.setInfo(v);
                warningDetailVOList.add(warningDetailVO);
            });
        }
        return warningDetailVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWarning(MaterialWarning warning) {
        Integer projectId = userUtil.getProjectId();
        noUtil.getWarningNo(projectId);
        this.save(warning);

    }


    private List<Integer> getCurProjectIdList() {
        AuthUser currentUser = userUtil.getUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        Integer currentDepartmentId = currentUser.getCurrentDepartmentId();
        Integer projectId = currentUser.getCurrentProjectId();

        List<Integer> projectIds;
        if (ObjectUtil.isNotNull(projectId)) {
            projectIds = Collections.singletonList(projectId);
        } else {
            List<ProjectDTO> projectVOList = projectServiceProxy.getAllProjectsByCompanyDeptId(currentCompanyId, currentDepartmentId);
            if (CollUtil.isEmpty(projectVOList)) {
                return Collections.emptyList();
            }
            projectIds = projectVOList.stream().map(ProjectDTO::getProjectId).collect(Collectors.toList());
        }
        return projectIds;
    }

}