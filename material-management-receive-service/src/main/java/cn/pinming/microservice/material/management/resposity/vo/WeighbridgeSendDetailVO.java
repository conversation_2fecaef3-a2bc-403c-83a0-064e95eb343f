package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/24 13:56
 */
@Data
public class WeighbridgeSendDetailVO {
    @ApiModelProperty(value = "发货单id")
    private String sendId;

    @ApiModelProperty(value = "发货单号")
    private String sendNo;

    @ApiModelProperty(value = "发货单位")
    private String sendProject;

    @ApiModelProperty(value = "收货单位")
    private String receiveProject;

    @ApiModelProperty(value = "发货人")
    private String sender;

    @ApiModelProperty(value = "发货时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "签收单据照片")
    private List<String> documentPics;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "毛重时间")
    private LocalDateTime weightGrossTime;

    @ApiModelProperty(value = "皮重时间")
    private LocalDateTime weightTareTime;

    @ApiModelProperty(value = "地磅发料物资明细列表")
    private List<MaterialSendDetailVO> materialSendDetailList;

    @ApiModelProperty(value = "毛重图片")
    private String weightGrossPic;

    @ApiModelProperty(value = "毛重图片")
    private List<String> weightGrossPics;

    @ApiModelProperty(value = "皮重图片")
    private String weightTarePic;

    @ApiModelProperty(value = "皮重图片")
    private List<String> weightTarePics;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;
}
