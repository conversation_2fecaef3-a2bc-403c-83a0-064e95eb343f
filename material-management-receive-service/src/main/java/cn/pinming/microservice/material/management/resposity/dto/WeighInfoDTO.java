package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WeighInfoDTO {
    @ApiModelProperty("过磅总车次")
    private Integer weighingCarCount;

    @ApiModelProperty("收料过磅车次")
    private Integer weighingCarNumber;

    @ApiModelProperty("发料过磅车次")
    private Integer sendingCarNumber;

    @ApiModelProperty("过磅重量（吨）")
    private BigDecimal weighWeight;
}
