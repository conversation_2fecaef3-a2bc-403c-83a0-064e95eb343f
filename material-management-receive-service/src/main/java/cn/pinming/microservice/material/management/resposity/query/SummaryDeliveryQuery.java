package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class SummaryDeliveryQuery {
    @ApiModelProperty("公司id")
    private Integer companyId;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("部门id")
    private Integer departmentId;

    @ApiModelProperty("项目范围")
    List<Integer> projectIdList;
}
