package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 地磅收货明细
 *
 * <AUTHOR>
 */
@Data
public class MaterialDatasVO {
    @ApiModelProperty(value = "收料明细id")
    private String id;

    @ApiModelProperty(value = "材料id")
    private Integer materialId;

    @ApiModelProperty(value = "材料二级名称")
    private String materialCategoryName;

    @ApiModelProperty(value = "品种")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "材料全称")
    private String type;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "下单数量")
    private BigDecimal count;

    @ApiModelProperty(value = "面单应收数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量：土方数量，按体积计量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "实际偏差")
    private BigDecimal deviation;

    @ApiModelProperty(value = "偏差状态（Byte）")
    private Byte deviationStatusByte;

    @ApiModelProperty(value = "偏差状态")
    private String deviationStatus;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;
}
