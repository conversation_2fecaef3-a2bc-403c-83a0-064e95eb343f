package cn.pinming.microservice.material.management.service.save;

import cn.pinming.microservice.material.management.resposity.dto.ReceiptRecyclePushDTO;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.management.resposity.form.SDKStandardMaterialForm;

import java.util.List;

/**
 * 数据落库入口  用于后续统一管理数据落库
 * <AUTHOR>
 */
public interface IMaterialDataSaveService {
    String SDKSendReceiveSave(SDKStandardMaterialForm form);

    String SDKDataSave(SDKStandardMaterialForm form,String sendReceiveId );

    void OCRSync(ReceiptRecyclePushDTO dto);

    void innerSync(List<PushRangeForm> range);

    void warning(List<String> ids);
}
