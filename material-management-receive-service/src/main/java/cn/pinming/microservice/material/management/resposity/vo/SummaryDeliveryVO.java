package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SummaryDeliveryVO implements Serializable {
    @ApiModelProperty("二级分类id")
    private Integer categoryId;

    @ApiModelProperty("二级分类名称")
    private String categoryName;

    @ApiModelProperty("累计发料量")
    private BigDecimal accumulationCount;

    @ApiModelProperty("本月新增量")
    private BigDecimal monthlyCount;

    @ApiModelProperty("百分比")
    private BigDecimal percentage;

    @ApiModelProperty("单位")
    private String unit;
}
