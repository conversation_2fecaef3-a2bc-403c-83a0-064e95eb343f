package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 收货/发货单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-12-28 14:20:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_material_send_receive")
@ApiModel(value = "MaterialSendReceive对象", description = "收货/发货单")
public class MaterialSendReceive implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收货/发货单ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收货单号")
    @TableField("receive_no")
    private String receiveNo;

    @ApiModelProperty(value = "类型，1：收货；2：发货")
    private Byte type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采  15 直入直出     发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他 16 退料出库 ")
    private Byte typeDetail;

    @ApiModelProperty(value = "车牌号")
    @TableField("truck_no")
    private String truckNo;

    @ApiModelProperty(value = "司机")
    private String driver;

    @ApiModelProperty(value = "司机电话")
    private String driverNumber;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "确认单外部系统单号")
    private String confirmExtNo;

    @ApiModelProperty("领用单位")
    private String receiveUnit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "外部运单发货时间")
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;

    @ApiModelProperty(value = "是否为手工补单 1 否，2 是")
    @TableField("is_addition")
    private Byte isAddition;

    @ApiModelProperty(value = "收货人")
    private String receiver;

//    @ApiModelProperty("扫码收料次数")
//    @TableField("scan_receive_count")
//    private Integer scanReceiveCount;

//    @ApiModelProperty(value = "1 临时收料，2 报备收料，3 无归属收料")
//    @TableField("receive_mode")
//    private Byte receiveMode;
//
//    @ApiModelProperty(value = "1 有效，2 无效")
//    @TableField("material_validity")
//    private Byte materialValidity;
//
//    @ApiModelProperty(value = "采购单ID")
//    @TableField(value = "purchase_id",updateStrategy = FieldStrategy.IGNORED)
//    private String purchaseId;
//
//    @ApiModelProperty(value = "采购单编号")
//    @TableField(value = "order_no",updateStrategy = FieldStrategy.IGNORED)
//    private String orderNo;

//    @ApiModelProperty(value = "供应商id")
//    @TableField("supplier_id")
//    private Integer supplierId;

//
//    @ApiModelProperty("车型")
//    @TableField("car_type")
//    private Integer carType;

//    @ApiModelProperty(value = "卡车ID")
//    @TableField("truck_id")
//    private String truckId;

//    @ApiModelProperty(value = "进场时间")
//    @TableField("enter_time")
//    private LocalDateTime enterTime;
//
//    @ApiModelProperty(value = "进场图片")
//    @TableField("enter_pic")
//    private String enterPic;
//
//    @ApiModelProperty(value = "出场时间")
//    @TableField("leave_time")
//    private LocalDateTime leaveTime;
//
//    @ApiModelProperty(value = "出场图片")
//    @TableField("leave_pic")
//    private String leavePic;
//
//    @ApiModelProperty(value = "收货/发货时间")
//    @TableField("receive_time")
//    private LocalDateTime receiveTime;
//
//    @ApiModelProperty(value = "司磅员")
//    private String operator;
//

//
//    @ApiModelProperty(value = "材料主管")
//    @TableField("material_manager")
//    private String materialManager;
}
