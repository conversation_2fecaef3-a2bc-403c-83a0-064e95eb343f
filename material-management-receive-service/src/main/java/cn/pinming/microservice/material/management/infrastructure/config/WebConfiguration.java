package cn.pinming.microservice.material.management.infrastructure.config;

import cn.pinming.core.cookie.AuthUserHelper;
import cn.pinming.core.cookie.CorsFilter;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.cookie.support.spring.AuthUserInterceptor;
import cn.pinming.core.web.exception.UnauthorizedException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.TimeZone;


/**
 * Created by tcz on 2020/02/19.
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Autowired
    private AuthUserInterceptor authUserInterceptor;

    @Configuration
    static class WebComponentConfiguration {
        @Bean
        public AuthUserHelper authUserHelper(@Value("${cookie.domain}") String domain) {
            AuthUserHelper helper = new AuthUserHelper();
            helper.setDomain(domain);
            return helper;
        }

        @Bean
        public FilterRegistrationBean<CorsFilter> corsFilter() {
            FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter());
            bean.setOrder(0);
            return bean;
        }


        @Bean
        public AuthUserInterceptor authUserInterceptor(
                @Qualifier("siteContextHolder") AuthUserHolder holder,
                AuthUserHelper authUserHelper) {
            return new AuthUserInterceptor(holder, authUserHelper, errorMessage -> {
                if (errorMessage == null) {
                    throw new UnauthorizedException();
                } else {
                    throw new UnauthorizedException(errorMessage);
                }
            });
        }
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        Objects.requireNonNull(authUserInterceptor, "AuthUserInterceptor can not be null");
        registry.addInterceptor(authUserInterceptor)
                .addPathPatterns("/**")
                // swagger相关
                .excludePathPatterns("/swagger-ui.html")
                .excludePathPatterns("/doc.html")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/error")
                .excludePathPatterns("/favicon.ico")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/js/**")
                .excludePathPatterns("/null/swagger-resources/**")
                .excludePathPatterns("/api/common/**")
                .excludePathPatterns("/api/js/openapi/**");
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer init() {
        return builder -> builder.timeZone(TimeZone.getDefault());
    }

    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        ObjectMapper objectMapper = Jackson2ObjectMapperBuilder.json().build();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
        objectMapper.registerModules(new Jdk8Module(), new JavaTimeModule(), simpleModule);
        return new MappingJackson2HttpMessageConverter(objectMapper);
    }
}
