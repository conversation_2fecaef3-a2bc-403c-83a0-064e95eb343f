package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
@Data
public class ReceiveOverviewCardQuery {

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "收料方式")
    private String receiveWay;

    @ApiModelProperty(value = "开始时间(yyyy-MM-dd)")
    private String start;

    @ApiModelProperty(value = "结束时间(yyyy-MM-dd)")
    private String end;

}
