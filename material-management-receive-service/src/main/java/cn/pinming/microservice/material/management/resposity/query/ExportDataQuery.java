package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据报表导出query
 */
@Data
public class ExportDataQuery {
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "项目列表")
    private List<Integer> projectIdList;

    @ApiModelProperty(value = "企业id")
    @NotNull
    private Integer companyId;

    @ApiModelProperty(value = "供应商id列表")
    private List<Integer> supplierIdList;

    @ApiModelProperty(value = "汇总表类型 1 项目汇总表 2 企业汇总表 3 供应商汇总表")
    @NotNull(message = "汇总表类型不能为空")
    private Integer type;
}
