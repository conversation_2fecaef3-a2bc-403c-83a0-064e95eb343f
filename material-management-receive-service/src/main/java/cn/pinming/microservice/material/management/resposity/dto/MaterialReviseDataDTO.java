package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 修订数据DTO
 * <AUTHOR>
 * @Date 2022/3/8 16:39
 */
@Data
public class MaterialReviseDataDTO {

    @ApiModelProperty(value = "修订id")
    private String reviseId;

    @ApiModelProperty(value = "收发货单id")
    private String receiveId;

    @ApiModelProperty(value = "收发货单明细id")
    private String materialDataId;

    @ApiModelProperty(value = "修订项")
    private String reviseDetail;

    @ApiModelProperty(value = "修订说明")
    private String reviseRemark;

    @ApiModelProperty(value = "修订人id")
    private String reviserId;

    @ApiModelProperty(value = "companyId")
    private Integer companyId;

    @ApiModelProperty(value = "修订时间")
    private LocalDateTime reviseTime;


}
