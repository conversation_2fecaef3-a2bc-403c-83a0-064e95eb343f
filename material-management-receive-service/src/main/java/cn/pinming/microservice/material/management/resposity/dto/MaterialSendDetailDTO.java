package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 发料物资明细
 * <AUTHOR>
 * @Date 2022/2/25 11:12
 */
@Data
public class MaterialSendDetailDTO {

    @ApiModelProperty(value = "物资名称id")
    private String materialId;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "品种及规格")
    private String materialSpec;

    @ApiModelProperty(value = "面单应收量")
    private String weightSend;

    @ApiModelProperty(value = "面单计量单位")
    private String weightUnit;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "签收单据照片")
    private String documentPic;

    @ApiModelProperty(value = "是否为自研终端数据 1 否 2 是")
    private Byte isDevice;
}
