package cn.pinming.microservice.material.management.infrastructure.proxy;

import cn.pinming.microservice.material.management.resposity.vo.ProjectVO;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * 项目相关dubbo.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:44 下午
 */
public interface ProjectServiceExtProxy {

    ProjectVO getProjectById(@NonNull Integer projectId);

    List<ProjectVO> getSimpleProjects(@NotNull List<Integer> projectIds);

    /**
     * 总览-统计设置-项目范围
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return 组织树节点下所有项目 和 项目范围配置 的交集项目列表
     */
    List<Integer> statisticsProjectIds(@NonNull Integer compId, Integer deptId);

    /**
     * 总览-统计设置-直属下级单位(分公司&项目) 和 项目范围配置 的交集项目列表
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return k: 单位或项目ID-单位或项目名称（例如：1-杭州西湖）名称截取第一个-后面的内容
     * v: 项目ID列表
     */
    Map<String, List<Integer>> directlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId);

    /**
     * 总览-统计设置-项目范围
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return 组织树节点下所有项目的项目列表
     */
    List<Integer> statisticsDeptProjectIds(@NonNull Integer compId, Integer deptId);


    /**
     * 总览-统计设置-直属下级单位(分公司&项目)集项目列表
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return k: 单位或项目ID-单位或项目名称（例如：1-杭州西湖）名称截取第一个-后面的内容
     * v: 项目ID列表
     */
    Map<String, List<Integer>> allDirectlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId);

}
