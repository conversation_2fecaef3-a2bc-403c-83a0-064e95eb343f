package cn.pinming.microservice.material.management.infrastructure.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description 总览统计设置默认分组
 */
public enum StatisticsDefaultGroupEnum {

    CONCRETE("混凝土"),
    REBAR("钢筋"),
    TEMPLATE("模版");

    private final String name;

    StatisticsDefaultGroupEnum(String name) {
        this.name = name;
    }

    public static boolean isDefaultGroup(String name) {
        for (StatisticsDefaultGroupEnum value : StatisticsDefaultGroupEnum.values()) {
            if (value.val().equals(name)) {
                return true;
            }
        }
        return false;
    }

    public static List<String> vals() {
        List<String> names = Arrays.stream(StatisticsDefaultGroupEnum.values()).map(StatisticsDefaultGroupEnum::val).collect(Collectors.toList());
        return names;
    }

    public static Map<String, String> keyTransition(Map<String, String> map) {
        Map<String, String> result = new HashMap<>();
        map.forEach((k, v) -> result.put(getValByCode(k), v));
        return result;
    }

    private static String getValByCode(String key) {
        return StatisticsDefaultGroupEnum.valueOf(key).val();
    }

    public String val() {
        return this.name;
    }

}
