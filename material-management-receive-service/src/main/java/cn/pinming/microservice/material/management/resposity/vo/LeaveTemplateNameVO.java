package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模板列表VO
 * <AUTHOR>
 * @since 2022/6/30 17:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeaveTemplateNameVO {

    @ApiModelProperty(value = "模板ID")
    private String id;

    @ApiModelProperty(value = "模板名")
    private String templateName;

}
