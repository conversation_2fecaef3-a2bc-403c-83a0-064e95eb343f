package cn.pinming.microservice.material.management.service;

import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 2021/9/6 11:03 上午
 */
public interface ISupplierAnalysisService {

    SupplierAnalysisVO getSummaryByQuery(SupplierAnalysisQuery query);

    IPage<?> pageListByQuery(SupplierAnalysisQuery query);

    IPage<?> pageListOrderByQuery(SupplierAnalysisQuery query);

    List<SupplierRankVO> negativeFrequencyRankList(SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyProportion(SupplierRankQuery query);

    List<SupplierRankVO> deductRank(SupplierRankQuery query);

    List<SupplierRankVO> deductProportion(SupplierRankQuery query);

    List<SupplierRankVO> deductTotalRank(SupplierRankQuery query);

    List<SupplierRankVO> deductTotalProportion(SupplierRankQuery query);

    List<SupplierRankVO> negativeTotalRank(SupplierRankQuery query);

    List<SupplierRankVO> negativeTotalProportion(SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyTop(SupplierRankQuery query);

    List<SupplierRankDiffVO> negativeFrequencyRateTop(SupplierRankQuery query);
}
