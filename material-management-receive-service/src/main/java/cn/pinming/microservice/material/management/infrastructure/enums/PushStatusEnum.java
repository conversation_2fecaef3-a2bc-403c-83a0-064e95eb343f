package cn.pinming.microservice.material.management.infrastructure.enums;

public enum PushStatusEnum {
    NONE((byte)0,"未推送"),
    SUCCESS((byte)1,"推送成功"),
    FAIL((byte)2,"推送失败");

    /**
     * 状态值
     */
    private final byte value;

    /**
     * 状态的描述
     */
    private final String description;

    PushStatusEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }
    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (PushStatusEnum statusEnum : PushStatusEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
