package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 处理人form
 * <AUTHOR>
 * @Date 2022/1/19 13:49
 */
@Data
public class HandlerForm {

    @ApiModelProperty(value = "处理人id")
    @NotBlank
    private String handlerId;

    @ApiModelProperty(value = "处理人姓名")
    @NotBlank
    private String handlerName;
}
