package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MobileReceiveHistoryVO {
    @ApiModelProperty(value = "面单应收总件数")
    private Integer sendNumber;

    @ApiModelProperty(value = "面单应收总含量")
    private BigDecimal sendContent;

    @ApiModelProperty(value = "面单应收结算合计")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "含量单位")
    private String unit;

    @ApiModelProperty(value = "结算单位")
    private String settlementUnit;

    @ApiModelProperty(value = "到场品牌")
    private String brand;

    @ApiModelProperty(value = "点验（非）标准历史")
    private List<MobileReceiveDetailHistoryVO> list;
}
