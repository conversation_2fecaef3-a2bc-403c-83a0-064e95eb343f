package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class MobileReceiveUpdateDetailForm {
    @ApiModelProperty(value = "totalId")
    @NotBlank(message = "totalId不能为空")
    private String totalId;

    @ApiModelProperty(value = "偏差状态")
    private Byte deviationStatus;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;
}
