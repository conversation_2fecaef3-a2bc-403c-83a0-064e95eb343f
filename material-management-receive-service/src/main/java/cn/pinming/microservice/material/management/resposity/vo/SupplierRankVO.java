package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商排行VO.
 *
 * <AUTHOR>
 * @version 2022/4/11 11:10
 */
@Data
public class SupplierRankVO extends SimpleSupplierDTO {

    @ApiModelProperty("车次/占比")
    private BigDecimal num;


    @ApiModelProperty("偏差状态")
    private Byte deviationStatus;
}
