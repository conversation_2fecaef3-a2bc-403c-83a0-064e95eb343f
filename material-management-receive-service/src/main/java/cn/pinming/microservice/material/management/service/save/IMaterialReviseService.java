package cn.pinming.microservice.material.management.service.save;


import cn.pinming.microservice.material.management.resposity.entity.MaterialRevise;
import cn.pinming.microservice.material.management.resposity.form.MaterialReviseForm;
import cn.pinming.microservice.material.management.resposity.form.WeighSendFixForm;
import cn.pinming.microservice.material.management.resposity.query.ReviseInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 预警处理人信息 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
public interface IMaterialReviseService extends IService<MaterialRevise> {

    void add(MaterialReviseForm materialReviseForm);

    /**
     * 根据receiveId查询修正数据列表
     *
     * @param
     * @return
     */
    MaterialReviseVO getMaterialRevise(ReviseInfoQuery reviseInfoQuery);

    /**
     * 修正数据详情
     *
     * @param reviseInfoQuery
     * @return
     */
    MaterialReviseDetailVO getMaterialReviseDetail(ReviseInfoQuery reviseInfoQuery);

    void sendFix(WeighSendFixForm form);
}
