package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StatisticsDataDTO {
    @ApiModelProperty(value = "累计面单应收量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "累计面单实收量")
    private BigDecimal receiveAmount;

    @ApiModelProperty(value = "累计实收数量")
    private BigDecimal actualReceive;
}
