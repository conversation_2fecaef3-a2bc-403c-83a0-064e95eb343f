package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 地磅收料明细vo
 * <AUTHOR>
 * @Date 2022/2/25 11:02
 */
@Data
public class MaterialSendDetailVO {
    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "品种及规格")
    private String materialSpec;

    @ApiModelProperty(value = "面单应收量")
    private String weightSend;

    @ApiModelProperty(value = "面单计量单位")
    private String weightUnit;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

}
