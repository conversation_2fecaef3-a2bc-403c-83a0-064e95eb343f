package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * 出场单枚举
 * <AUTHOR>
 * @since 2022/7/22 10:34
 */
public enum LeaveParamEnum {

    PRE_TRUCK_NO("preTruckNo", "上一车车牌号"),
    TICKET_NO("ticketNo", "出场单编号"),
    EXIT_DATE("exitDate", "出场日期"),
    EXIT_TIME("exitTime", "出场时间"),
    RECEIVER_PROJECT("receiverProject", "客户(项目)名称"),
    PROJECT_ADDRESS("projectAddress", "客户(项目)名称地址"),
    SUB_COMPANY_NAME("subCompanyName", "客户公司"),
    RECEIVER("receiver", "客户联系人"),
    RECEIVER_TEL("receiverTel", "客户联系人电话"),
    POSITION("position", "物料使用部位"),
    TRUCK_NO("truckNo", "车牌号"),
    COUNT("count", "本订单订货量"),
    SEND_COUNT("sendCount", "本车次发货量"),
    SEND_TOTAL_COUNT("sendTotalCount", "本订单累计发货量"),
    UNIT("unit", "单位"),
    SPEC("spec", "品种及规格"),
    PARAMETER("parameter", "质量或生产参数"),
    SENDER("sender", "发货人"),
    BARCODE("barCode", "条形码"),
    MACHINE_NAME("machineName", "生产机组"),
    ;

    /**
     * 状态值
     */
    private final String value;
    /**
     * 状态的描述
     */
    private final String description;

    LeaveParamEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String value() {
        return value;
    }
    public String description() {
        return description;
    }
}
