package cn.pinming.microservice.material.management.infrastructure.config;


import cn.pinming.microservice.material.management.infrastructure.util.SpringUtils;
import cn.pinming.microservice.material.management.resposity.entity.MaterialApiLog;
import cn.pinming.microservice.material.management.service.IMaterialApiLogService;

import java.util.TimerTask;

public class AsyncFactory {

    /**
     * 操作日志记录
     *
     * @param apiLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final MaterialApiLog apiLog) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(IMaterialApiLogService.class).save(apiLog);
            }
        };
    }
}
