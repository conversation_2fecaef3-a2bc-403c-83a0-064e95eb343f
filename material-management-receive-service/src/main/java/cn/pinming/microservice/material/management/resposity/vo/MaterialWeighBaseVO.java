package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地磅收货数据VO
 *
 * <AUTHOR>
 */
@Data
public class MaterialWeighBaseVO implements Serializable {
    @ApiModelProperty(value = "物料明细id")
    @ExcelIgnore
    private String id;

    @ApiModelProperty(value = "收货单id")
    @ExcelIgnore
    private String receiveId;

    @ApiModelProperty(value = "实收数量")
    @ExcelProperty("实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "有无业务规范性预警")
    @ExcelIgnore
    private String isWarning;

    @ApiModelProperty(value = "预警是否处理完毕")
    @ExcelIgnore
    private String isWarningHanded;

    @ApiModelProperty(value = "收料类型")
    @ExcelProperty("收料类型")
    private String receiveType;

    @ApiModelProperty(value = "收货项目(收料)/发货单位(发料)")
    @ExcelProperty("收货项目")
    private String projectTitle;

    @ApiModelProperty(value = "品种及规格")
    @ExcelIgnore
    private String materialName;

    @ApiModelProperty(value = "二级分类名称")
    @ExcelIgnore
    private String categoryName;

    @ApiModelProperty(value = "面单应收数量")
    @ExcelProperty("面单应收数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "实际数量")
    @ExcelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "供应商名称(收料)/收货单位(发料)")
    @ExcelProperty("供应商")
    private String supplierName;

    @ApiModelProperty(value = "结算单位")
    @ExcelProperty("结算单位")
    private String unit;

    @ApiModelProperty(value = "收货单号")
    @ExcelProperty("收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "车牌号")
    @ExcelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "称重确认人")
    @ExcelProperty("收料人")
    private String receiver;

    @ApiModelProperty(value = "收料时间")
    @ExcelProperty("收料时间")
    @ColumnWidth(25)
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "偏差率")
    @ExcelProperty("偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态")
    @ExcelIgnore
    private String deviationStatusStr;

    @ApiModelProperty("毛重")
    @ExcelProperty("毛重")
    private BigDecimal gWeight;

    @ApiModelProperty("皮重")
    @ExcelProperty("皮重")
    private BigDecimal tWeight;

    @ApiModelProperty("净重")
    @ExcelProperty("净重")
    private BigDecimal nWeight;

    @ApiModelProperty("扣重")
    @ExcelProperty("扣重")
    private BigDecimal bWeight;

    @ApiModelProperty(value = "实重(吨)")
    @ExcelProperty("实重(吨)")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "含水率")
    @ExcelIgnore
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "推送状态 0.未推送\n" +
            "1.已推送\n" +
            "2.推送失败")
    @ExcelProperty("推送状态")
    private String pushStateStr;

    @ApiModelProperty(value = "是否扣量")
    @ExcelIgnore
    private String isDeductStr;

    @ApiModelProperty(value = "是否有完整性预警")
    @ExcelIgnore
    private boolean integrity;

    @ApiModelProperty(value = "是否有完整性预警")
    @ExcelIgnore
    private String integrityStr;

    @ApiModelProperty(value = "是否有规范性预警")
    @ExcelIgnore
    private boolean normative;

    @ApiModelProperty(value = "是否有规范性预警")
    @ExcelIgnore
    private String normativeStr;

    @ApiModelProperty(value = "是否已处理完毕")
    @ExcelIgnore
    private boolean handle;

    @ApiModelProperty(value = "是否已处理完毕")
    @ExcelIgnore
    private String handleStr;

    @ApiModelProperty(value = "采购单id")
    @ExcelIgnore
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    @ExcelIgnore
    private String contractDetailId;

    @ApiModelProperty(value = "是否被该对账选择")
    @ExcelIgnore
    private Boolean isChoose;

    @ApiModelProperty(value = "对账id")
    @ExcelIgnore
    private String reconciliationId;

    @ApiModelProperty(value = "物资名称-导")
    @ExcelProperty("物资名称")
    private String materialCategoryName;

    @ApiModelProperty(value = "计划使用部位")
    @ExcelProperty("计划使用部位")
    private String position;

    @ApiModelProperty(value = "约定偏差阈值上限-导")
    @ExcelProperty("约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限-导")
    @ExcelProperty("约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "偏差状态-导")
    @ExcelProperty("偏差状态")
    private String deviationStatus;

    @ApiModelProperty(value = "偏差量-导")
    @ExcelProperty("偏差量")
    private BigDecimal deviationCount;

    @ApiModelProperty(value = "换算系数")
    @ExcelProperty("换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "进场时间")
    @ExcelIgnore
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    @ExcelIgnore
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "采购单编号")
    @ExcelIgnore
    private String orderNo;

    @ApiModelProperty(value = "收料确认方式 1 扫码组装 2 司机自助确认")
    @ExcelIgnore
    private Integer confirmType;

    @ApiModelProperty(value = "是否手工补录")
    @ExcelProperty("是否手工补录")
    private String isAdditionStr;

    @ApiModelProperty(value = "外部系统单号")
    @ExcelProperty("外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "终端称重记录id")
    private String weighId;
}
