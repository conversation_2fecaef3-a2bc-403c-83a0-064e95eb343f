package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.entity.MaterialDataMulti;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 收货/发货明细一车多料 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface MaterialDataMultiMapper extends BaseMapper<MaterialDataMulti> {

    List<MaterialDataResourceDTO> innerSyncById(@Param("id") String id,@Param("receiveId") String receiveId,@Param("list") List<PushRangeForm> list);
}
