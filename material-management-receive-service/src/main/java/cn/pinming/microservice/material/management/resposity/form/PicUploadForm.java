package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 图片上传form.
 *
 * <AUTHOR> hao
 * @version 2022/4/26 14:33
 */
@Data
public class PicUploadForm {

    @ApiModelProperty(value = "称重id",required = true)
    private String weighId;

    @ApiModelProperty("企业id")
    private Integer companyId;

    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty(value = "图片类型  1进场照片 2出场照片 3高拍仪照片 4车牌照片",required = true)
    private Integer type;

    @ApiModelProperty("图片uuid")
    private String uuid;

}
