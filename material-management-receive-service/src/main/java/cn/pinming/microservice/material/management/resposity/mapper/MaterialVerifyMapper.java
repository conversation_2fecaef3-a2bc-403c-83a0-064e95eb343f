package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.dto.*;
import cn.pinming.microservice.material.management.resposity.entity.MaterialVerify;
import cn.pinming.microservice.material.management.resposity.query.MaterialVerifyQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 物料对账表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
public interface MaterialVerifyMapper extends BaseMapper<MaterialVerify> {
    IPage<MaterialDataVerifyDTO> selectMaterialVerify(MaterialVerifyQuery materialVerifyQuery);

    void updateFileStatusById(@Param("id") String id);

    List<String> selectVerifyPerson(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);
}
