package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SummaryWarningVO implements Serializable {
    @ApiModelProperty("预警分类")
    private Byte type;

    @ApiModelProperty("预警描述")
    private String warningType;

    @ApiModelProperty("已处理量")
    private Integer handleCount;

    @ApiModelProperty("未处理量")
    private Integer unHandleCount;

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("百分比")
    private BigDecimal percentage;
}
