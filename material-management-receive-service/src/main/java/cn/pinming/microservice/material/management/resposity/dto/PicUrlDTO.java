package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PicUrlDTO implements Serializable {

    @ApiModelProperty("文件ID")
    private String fileUUid;

    @ApiModelProperty("图片预览地址")
    private String previewUrl;

    @ApiModelProperty("图片下载地址")
    private String downloadUrl;
}
