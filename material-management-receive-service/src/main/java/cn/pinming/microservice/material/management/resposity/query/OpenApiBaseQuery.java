package cn.pinming.microservice.material.management.resposity.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OpenApiBaseQuery extends Page {

    private Integer companyId;

    private Integer projectId;

    private Integer departmentId;

    private List<Integer> projectIds;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
