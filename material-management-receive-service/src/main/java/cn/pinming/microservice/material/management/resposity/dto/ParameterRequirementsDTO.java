package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Data
public class ParameterRequirementsDTO {

    private String paramCode;
    private String paramType;
    private String paramValue;

    @ApiModelProperty(value = "参数名称 兼容李鹏参数回显跟张翚参数回显")
    private String paramName;
    @ApiModelProperty(value = "1 BOM 2 材料字典")
    private Byte   kind;

}
