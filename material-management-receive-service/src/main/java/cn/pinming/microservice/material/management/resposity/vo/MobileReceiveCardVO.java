package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MobileReceiveCardVO extends SimpleSupplierDTO {
    @ApiModelProperty(value = "收货单id")
    @ExcelIgnore
    private String id;

    @ApiModelProperty(value = "收货单编号")
    @ExcelProperty("收货单编号")
    private String receiveNo;

    @ApiModelProperty(value = "采购单id")
    @ExcelIgnore
    private String purchaseId;

    @ApiModelProperty(value = "合同id")
    @ExcelIgnore
    private String contractId;

    @ApiModelProperty(value = "进场状态")
    @ExcelIgnore
    private Byte receiveStatus;

    @ApiModelProperty(value = "进场状态")
    @ExcelProperty("进场状态")
    private String receiveStatusStr;

    @ApiModelProperty(value = "收货项目id")
    @ExcelIgnore
    private Integer projectId;

    @ApiModelProperty(value = "收货项目")
    @ExcelIgnore
    private String projectTitle;

    @ApiModelProperty(value = "收货项目")
    @ExcelProperty("收货项目")
    private String projectName;

    @ApiModelProperty(value = "实际收货人")
    @ExcelProperty("实际收货人")
    private String receiver;

    @ApiModelProperty(value = "偏差状态")
    @ExcelIgnore
    private Byte deviationStatus;

    @ApiModelProperty(value = "偏差状态")
    @ExcelProperty("偏差状态")
    private String deviationStatusStr;

    @ApiModelProperty(value = "收料状态")
    @ExcelIgnore
    private Byte receiveType;

    @ApiModelProperty(value = "收料状态")
    @ExcelProperty("收料状态")
    private String receiveTypeStr;

    @ApiModelProperty(value = "车牌号")
    @ExcelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "材料类目")
    @ExcelProperty("材料类目")
    private String category;

    @ApiModelProperty(value = "收料时间")
    @ExcelProperty("收料时间")
    @ColumnWidth(25)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "材料卡片列表")
    @ExcelIgnore
    private List<MobileGoodsCardVO> list;

    @ApiModelProperty(value = "合同供应商id")
    @ExcelIgnore
    private Integer supplierId;

//    @ApiModelProperty(value = "供应商id")
//    private Integer supplierIdFromPurchase;

    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty("供应商名称")
    private String supplierTitle;

    @ApiModelProperty(value = "是否修订过 1 是 2 否")
    @ExcelIgnore
    private Byte isRevise;

    @ApiModelProperty(value = "是否修订过 1 是 2 否")
    @ExcelProperty("是否修订过")
    private String isReviseStr;
}
