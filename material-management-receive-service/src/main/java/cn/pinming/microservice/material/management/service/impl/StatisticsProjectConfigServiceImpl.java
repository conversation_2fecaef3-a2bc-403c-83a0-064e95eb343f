package cn.pinming.microservice.material.management.service.impl;

import cn.pinming.microservice.material.management.resposity.entity.StatisticsProjectConfig;
import cn.pinming.microservice.material.management.resposity.mapper.StatisticsProjectConfigMapper;
import cn.pinming.microservice.material.management.service.IStatisticsProjectConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 统计配置表-项目 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-11 13:32:52
 */
@Service
public class StatisticsProjectConfigServiceImpl extends ServiceImpl<StatisticsProjectConfigMapper, StatisticsProjectConfig> implements IStatisticsProjectConfigService {

}
