package cn.pinming.microservice.material.management.infrastructure.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMultiMapper;
import cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO;
import cn.pinming.microservice.material.resource.service.IWeighDataSyncService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class WeighDataListener {
    @DubboReference
    private IWeighDataSyncService weighDataSyncService;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private MaterialDataMultiMapper materialDataMultiMapper;

    @NacosValue("${sync.range}")
    private String range;

    public void AfterCompletion(String id,String receiveId, Integer type) {
        if (StrUtil.isBlank(range)) {
            return;
        }

        List<PushRangeForm> range = com.alibaba.fastjson.JSONObject.parseArray(this.range, PushRangeForm.class);
        List<MaterialDataResourceDTO> list = new ArrayList<>();
        if (type == 1) {
            // 一车一料 id为data
            list = materialDataMapper.innerSyncById(id,receiveId,range);
        } else if (type == 2) {
            // 一车多料 id为receive
            list = materialDataMultiMapper.innerSyncById(id,receiveId,range);
        }

        if (CollUtil.isNotEmpty(list)) {
            try {
                weighDataSyncService.syncWeighData(list);
            } catch (Exception e) {
                log.info("内部推送失败,明细为{},类型为{},原因为{}",id,type == 1 ? "一车一料" : "一车多料",e.getMessage());
            }
        }
    }
}
