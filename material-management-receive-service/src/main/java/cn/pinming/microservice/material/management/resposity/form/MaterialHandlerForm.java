package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 处理人form
 * <AUTHOR>
 * @Date 2022/1/19 13:22
 */
@Data
public class MaterialHandlerForm {

    @ApiModelProperty(value = "处理人列表")
    @Size(min = 1, message = "处理人列表为空")
    @Valid
    private List<HandlerForm> handlerFormList;

    @ApiModelProperty(value = "处理类型")
    @NotNull
    private Byte handleType;

}
