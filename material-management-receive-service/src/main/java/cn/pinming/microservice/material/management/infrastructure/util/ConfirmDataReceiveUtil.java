package cn.pinming.microservice.material.management.infrastructure.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.material.v2.model.dto.ConfirmResDTO;
import cn.pinming.material.v2.model.form.*;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.resposity.dto.CargoDetailInfoDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarning;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.service.IMaterialWarningService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.mixing.plant.management.api.IMixingPlantOrder;
import cn.pinming.microservice.mixing.plant.management.dto.MixingPlantOrderDTO;
import cn.pinming.microservice.purchase.management.service.IMaterialPurchaseService;
import cn.pinming.microservice.purchase.management.vo.PurchaseOrderDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ConfirmDataReceiveUtil {
    @Resource
    private PicUtil picUtil;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private NoUtil noUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @DubboReference
    private IMaterialPurchaseService materialPurchaseService;
    @DubboReference
    private IMixingPlantOrder mixingPlantOrder;
    @DubboReference
    private IMaterialContractService materialContractService;
    @Resource
    private WeighDuplicationUtil weighDuplicationUtil;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private WarningUtil warningUtil;
    @Resource
    private IMaterialWarningService materialWarningService;

    @Transactional(rollbackFor = Exception.class)
    public List<ConfirmResDTO> receiveConfirmData(List<PushConfirmForm> pushConfirmDTOS, Integer companyId, Integer projectId) {
        if (CollUtil.isNotEmpty(pushConfirmDTOS)) {
            List<MaterialSendReceive> sendReceives = new ArrayList<>();
            List<MaterialData> datas = new ArrayList<>();
            List<ConfirmResDTO> res = new ArrayList<>();
            List<MaterialData> receives = new ArrayList<>();
            List<MaterialData> sends = new ArrayList<>();

            pushConfirmDTOS.forEach(e -> {
                // 幂等
                List<MaterialData> list = materialDataService.lambdaQuery()
                        .eq(MaterialData::getWeighId, e.getConfirmId())
                        .list();
                ConfirmResDTO confirmResDTO = new ConfirmResDTO();
                if (CollUtil.isNotEmpty(list)) {
                    confirmResDTO.setId(e.getId());
                    confirmResDTO.setLocalId(e.getConfirmId());
                    res.add(confirmResDTO);
                    return;
                }
                MaterialData data = new MaterialData();
                MaterialSendReceive materialSendReceive = new MaterialSendReceive();
                MaterialData materialData = new MaterialData();
                String grossPic = null;
                String tarePic = null;

                materialSendReceive.setCompanyId(companyId);
                materialSendReceive.setProjectId(projectId);
                materialData.setCompanyId(companyId);
                materialData.setProjectId(projectId);

                PushConfirmDeliveryForm jsDeliveryInfo = e.getJsDeliveryInfo();
                PushConfirmDataForm confirmData = e.getConfirmData();
                PushConfirmConvertResultForm convertResult = confirmData.getConvertResult();
                PushConfirmWeightForm weightResult = confirmData.getWeightResult();
                WeighDataGrossForm grossWeight = weightResult.getGrossWeight();
                WeighDataTareForm tareWeight = weightResult.getTareWeight();
                List<PushConfirmMaterialForm> cargoConfirmList = e.getCargoConfirmList();
                try {
                    weighDuplicationUtil.judge(Arrays.asList(grossWeight.getOriginalId(),tareWeight.getOriginalId()));
                } catch (Exception ex) {
                    log.info("confirmId为{}的确认单中的recordId已被使用",e.getConfirmId());
                    return;
                }

                // 暂支持一车一料 || 仅毛皮重
                if (CollUtil.isNotEmpty(cargoConfirmList) && cargoConfirmList.size() > 1) {
                    return;
                }

                materialSendReceive.setId(UUIDUtil.randomUUIDWithoutConnector());
                materialSendReceive.setConfirmExtNo(jsDeliveryInfo.getDeliveryNo());
                materialSendReceive.setDriver(jsDeliveryInfo.getDriverName());
                materialSendReceive.setDriverNumber(jsDeliveryInfo.getDriverPhoneNo());
                materialSendReceive.setDeliveryTime(jsDeliveryInfo.getDeliveryTime());
                materialSendReceive.setTruckNo(confirmData.getTruckNo());
                materialSendReceive.setType(Byte.valueOf(String.valueOf(confirmData.getWeighingType())));
                materialSendReceive.setExtNo(e.getExtCode());
                if (materialSendReceive.getType().equals(WeighTypeEnum.RECEIVE.value())) {
                    materialSendReceive.setReceiveNo(noUtil.getConfirmReceiveNo(projectId));
                }else {
                    materialSendReceive.setReceiveNo(noUtil.getConfirmSendNo(projectId));
                }

                materialData.setId(UUIDUtil.randomUUIDWithoutConnector());
                materialData.setReceiveId(materialSendReceive.getId());
                materialData.setWeighId(e.getConfirmId());
                try {
                    materialData.setSignerPic(picUtil.downloadAndUploadFile(confirmData.getSignerPhotoUrl(),UUIDUtil.randomUUID(),null));
                } catch (IOException ex) {
                    log.error("签名人图片下载出错,dataId:{}",materialData.getId());
                }
                try {
                    materialData.setSignaturePic(picUtil.downloadAndUploadFile(confirmData.getSignaturePicUrl(),UUIDUtil.randomUUID(),null));
                } catch (IOException ex) {
                    log.error("签名图片下载出错,dataId:{}",materialData.getId());
                }
                if (CollUtil.isNotEmpty(confirmData.getWaybillPhotoUrls())) {
                    String documentPic = confirmData.getWaybillPhotoUrls().stream().map(pic -> {
                        try {
                            return picUtil.downloadAndUploadFile(pic, UUIDUtil.randomUUID(), null);
                        } catch (IOException ex) {
                            log.error("单据图片下载出错,dataId:{}", materialData.getId());
                            return null;
                        }
                    }).collect(Collectors.joining(","));
                    materialData.setDocumentPic(documentPic);
                }
                materialData.setRatio(convertResult.getScaleFactor());
                materialData.setActualCount(convertResult.getConvertValue());
                materialData.setWeightUnit(convertResult.getUnitOfMeasurement());
                materialData.setWeightDeduct(weightResult.getDeductWeight());
                materialData.setMoistureContent(weightResult.getDeductRatio());
                materialData.setWeightNet(weightResult.getNetWeight());
                materialData.setWeightActual(weightResult.getActualWeight());
                materialData.setWeightGross(grossWeight.getWeightValue());
                materialData.setWeightTare(tareWeight.getWeightValue());
                if (CollUtil.isNotEmpty(grossWeight.getPhotoUrls())) {
                    grossPic = grossWeight.getPhotoUrls().stream().map(pic -> {
                        try {
                            return picUtil.downloadAndUploadFile(pic,UUIDUtil.randomUUID(),null);
                        } catch (IOException ex) {
                            log.error("过磅图片下载出错,dataId:{}", materialData.getId());
                            return null;
                        }
                    }).collect(Collectors.joining(","));
                }
                if (CollUtil.isNotEmpty(tareWeight.getPhotoUrls())) {
                    tarePic = tareWeight.getPhotoUrls().stream().map(pic -> {
                        try {
                            return picUtil.downloadAndUploadFile(pic,UUIDUtil.randomUUID(),null);
                        } catch (IOException ex) {
                            log.error("过磅图片下载出错,dataId:{}", materialData.getId());
                            return null;
                        }
                    }).collect(Collectors.joining(","));
                }
                if (materialSendReceive.getType() == 1) {
                    materialData.setEnterTime(grossWeight.getWeighTime());
                    materialData.setRecordId1(grossWeight.getOriginalId());
                    materialData.setEnterPic(grossPic);
                    materialData.setLeaveTime(tareWeight.getWeighTime());
                    materialData.setRecordId2(tareWeight.getOriginalId());
                    materialData.setLeavePic(tarePic);
                }else {
                    materialData.setEnterTime(tareWeight.getWeighTime());
                    materialData.setRecordId1(tareWeight.getOriginalId());
                    materialData.setEnterPic(tarePic);
                    materialData.setLeaveTime(grossWeight.getWeighTime());
                    materialData.setRecordId2(grossWeight.getOriginalId());
                    materialData.setLeavePic(grossPic);
                }

                if (CollUtil.isNotEmpty(cargoConfirmList)) {
                    PushConfirmMaterialForm materialDTO = cargoConfirmList.get(0);
                    log.info("cargoId为{}",materialDTO.getCargoId());
                    if (ObjectUtil.isNotNull(materialDTO.getSource())) {
                        if (materialDTO.getSource().equals("品茗拌合站")) {
                            MixingPlantOrderDTO simpleMixingPlantOrder = mixingPlantOrder.getSimpleMixingPlantOrder(materialDTO.getCargoId());
                            if (ObjectUtil.isNotNull(simpleMixingPlantOrder)) {
                                log.info("拌合站订单查询结果为{}",simpleMixingPlantOrder);
                                materialData.setPurchaseOrderId(simpleMixingPlantOrder.getPurchaseOrderId());
                                materialData.setContractDetailId(simpleMixingPlantOrder.getPurchaseOrderDetailId());
                                materialData.setMixingType(simpleMixingPlantOrder.getMixingType());
                            }
                        } else if (materialDTO.getSource().equals("品茗采购单")) {
                            List<PurchaseOrderDetailVO> purchaseOrderDetails = materialPurchaseService.getPurchaseOrderDetails(Collections.singletonList(materialDTO.getCargoId()));
                            log.info("采购单查询结果为{}",purchaseOrderDetails);
                            if (CollUtil.isNotEmpty(purchaseOrderDetails)) {
                                PurchaseOrderDetailVO purchaseOrderDetailVO = purchaseOrderDetails.get(0);
                                materialData.setPurchaseOrderId(purchaseOrderDetailVO.getOrderId());
                                materialData.setContractDetailId(purchaseOrderDetailVO.getContractDetailId());
                                materialSendReceive.setExtNo(purchaseOrderDetailVO.getOrderExtNo());
                            }
                        } else if (materialDTO.getSource().equals("品茗合同")) {
                            SimpleContractDetailDTO simpleContractDetailDTO = materialContractService.querySimpleContractDetail(materialDTO.getCargoId());
                            log.info("合同查询结果为{}",simpleContractDetailDTO);
                            if (ObjectUtil.isNotNull(simpleContractDetailDTO)) {
                                materialData.setContractDetailId(materialDTO.getCargoId());
                            }
                        }else {
                            log.info("没有查询到相关订单...");
                        }
                    }else {
                        // 兼容之前没source的数据，以前的基石订单只有桩桩采购单和拌合站同步过来的，可能存在一些还没同步过去的数据
                        MixingPlantOrderDTO simpleMixingPlantOrder = mixingPlantOrder.getSimpleMixingPlantOrder(materialDTO.getCargoId());
                        if (ObjectUtil.isNotNull(simpleMixingPlantOrder)) {
                            // 同步过来的cargoId是拌合站订单明细的id，去拌合站查，查到有采购单id的就返回采购单id和合同明细id；查不到就直接塞拌合站的明细id和拌合站的订单id
                            log.info("拌合站订单查询结果为{}",simpleMixingPlantOrder);
                            materialData.setPurchaseOrderId(simpleMixingPlantOrder.getPurchaseOrderId());
                            materialData.setContractDetailId(simpleMixingPlantOrder.getPurchaseOrderDetailId());
                            materialData.setMixingType(simpleMixingPlantOrder.getMixingType());
                        }else if (ObjectUtil.isNull(simpleMixingPlantOrder)){
                            // 同步过来的cargoId是采购单明细的id,塞的是采购单id和合同明细id
                            List<PurchaseOrderDetailVO> purchaseOrderDetails = materialPurchaseService.getPurchaseOrderDetails(Collections.singletonList(materialDTO.getCargoId()));
                            log.info("采购单查询结果为{}",purchaseOrderDetails);
                            if (CollUtil.isNotEmpty(purchaseOrderDetails)) {
                                PurchaseOrderDetailVO purchaseOrderDetailVO = purchaseOrderDetails.get(0);
                                materialData.setPurchaseOrderId(purchaseOrderDetailVO.getOrderId());
                                materialData.setContractDetailId(purchaseOrderDetailVO.getContractDetailId());
                                materialSendReceive.setExtNo(purchaseOrderDetailVO.getOrderExtNo());
                            }
                        }else {
                            log.info("没有查询到相关订单...");
                        }
                    }

                    String cargoId = cargoConfirmList.get(0).getCargoId();
                    CargoDetailInfoDTO dto = null;
                    if (ObjectUtil.isNotNull(materialDTO.getSource())) {
                        if (materialDTO.getSource().equals("品茗拌合站")) {
                            dto = materialDataMapper.getCargoFromMixing(cargoId);
                        } else if (materialDTO.getSource().equals("品茗采购单")) {
                            dto = materialDataMapper.getCargoFromPurchase(cargoId);
                        } else if (materialDTO.getSource().equals("品茗合同")) {
                            dto = materialDataMapper.getCargoFromContract(cargoId);
                        }
                    }else {
                        // cargoId -》 桩桩采购单场景:基石订单的外部单号是采购单明细id；拌合站原料进场下是拌合站订单明细id；拌合站成品出场下是拌合站订单明细id，要用关联的采购单订单明细id查数据
                        // 兼容原数据
                        dto = materialDataMapper.getCargo(cargoId);
                    }

                    materialData.setWeightSend(materialDTO.getWaybillCounts());
                    materialData.setWeightUnit(materialDTO.getWaybillUnit());
                    materialData.setRemark(materialDTO.getRemark());
                    materialData.setIsDevice((byte) 2);
                    materialData.setConfirmType(2);
                    materialData.setReceiveTime(materialData.getEnterTime().isBefore(materialData.getLeaveTime()) ? materialData.getEnterTime() : materialData.getLeaveTime());
                    if (ObjectUtil.isNotNull(materialData.getWeightSend()) && materialData.getWeightSend().compareTo(BigDecimal.ZERO) > 0) {
                        materialData.setDeviationRate(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(),materialData.getWeightSend()),materialData.getWeightSend(),4),100));
                    }
                    if (ObjectUtil.isNotNull(dto)) {
                        materialData.setMaterialId(dto.getMaterialId());
                        MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
                        if (ObjectUtil.isNotEmpty(materialDto)) {
                            materialData.setMaterialName(materialDto.getMaterialName());
                            materialData.setCategoryId(materialDto.getMaterialCategoryId());
                            materialData.setCategoryName(materialDto.getMaterialCategoryName());
                        }
                        materialData.setSupplierId(dto.getSupplierId());
                        materialData.setActualReceive(materialData.getActualCount());
                        if (ObjectUtil.isNotNull(dto.getDeviationCeiling()) && ObjectUtil.isNotNull(dto.getDeviationFloor()) && ObjectUtil.isNotNull(dto.getDeviationCalculate()) && dto.getDeviationCalculate() == 1) {
                            // 偏差状态
                            BigDecimal deviation = materialData.getDeviationRate();
                            int flag;
                            int flag1;
                            flag = deviation.compareTo(dto.getDeviationFloor());
                            flag1 = deviation.compareTo(dto.getDeviationCeiling());
                            if (flag < 0) {
                                materialData.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                            }
                            if (flag1 > 0) {
                                materialData.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                            }
                            if (flag >= 0 && flag1 <= 0) {
                                materialData.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                            }
                            boolean flag2 = materialData.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || materialData.getDeviationStatus() == DeviationStatusEnum.NORMAL.value();
                            if (flag2) {
                                materialData.setActualReceive(materialData.getWeightSend());
                            }
                        }
                    }
                }

                sendReceives.add(materialSendReceive);
                datas.add(materialData);
                if (materialSendReceive.getType() == 1) {
                    receives.add(materialData);
                }else {
                    sends.add(materialData);
                }
                confirmResDTO.setLocalId(e.getConfirmId());
                confirmResDTO.setId(e.getId());
                res.add(confirmResDTO);
            });

            if (CollUtil.isNotEmpty(sendReceives)) {
                Map<String, String> receiveMap = sendReceives.stream().collect(Collectors.toMap(MaterialSendReceive::getId, MaterialSendReceive::getReceiveNo));
                List<MaterialWarning> warnings = new ArrayList<>();
                if (CollUtil.isNotEmpty(receives)) {
                    receives.forEach(e -> {
                        List<MaterialWarning> receiveWarning = warningUtil.createWarnings(e,receiveMap.get(e.getReceiveId()),(byte) 1);
                        warnings.addAll(receiveWarning);
                    });
                }
                if (CollUtil.isNotEmpty(sends)) {
                    sends.forEach(e -> {
                        List<MaterialWarning> sendWarning = warningUtil.createWarnings(e,receiveMap.get(e.getReceiveId()), (byte) 2);
                        warnings.addAll(sendWarning);
                    });
                }

                if (CollUtil.isNotEmpty(warnings)) {
                    materialWarningService.saveBatch(warnings);
                }
            }

            materialSendReceiveService.saveBatch(sendReceives);
            materialDataService.saveBatch(datas);

            log.info("返回结果为{}",datas.stream().map(MaterialData::getWeighId).collect(Collectors.toList()));
            log.info("res:{}",res);
            return res;
        }

        return new ArrayList<>();
    }


}
