package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Data
public class PurchaseOrderDetailDTO implements Serializable {

    @ApiModelProperty(value = "采购合同物料明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "采购单ID")
    private String orderId;

    @ApiModelProperty(value = "采购单明细ID")
    private String orderDetailId;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "品牌列表")
    private List<String> brands;

    @ApiModelProperty(value = "下单量")
    private BigDecimal count;

    @ApiModelProperty(value = "备注(计划使用部位)")
    private String remark;

    @ApiModelProperty(value = "分类id")
    private Integer categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "品种/规格")
    private String materialNameSpec;

    @ApiModelProperty(value = "材料ID")
    private String materialId;

    @ApiModelProperty(value = "采购结算单位")
    private String unit;

    @ApiModelProperty(value = "过磅换算公式单位")
    private String transformUnit;

    @ApiModelProperty(value = "累计面单应收量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "实收量")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "含量单位")
    private String contentUnit;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "偏差数量")
    private BigDecimal deviationCount;

    @ApiModelProperty(value = "偏差率")
    private String deviationRate;

    @ApiModelProperty(value = "本次是否下单")
    private Boolean isOrder;

    @ApiModelProperty(value = "是否关联收料")
    private Boolean receiveDataRelation;

    @ApiModelProperty(value = "参数要求")
    private String parameterRequirements;

    @ApiModelProperty(value = "参数要求")
    private List<ParameterRequirementsDTO> parameterRequirementsModel;

    @ApiModelProperty(value = "参数要求说明")
    private String parameterRequirementsDesc;
}
