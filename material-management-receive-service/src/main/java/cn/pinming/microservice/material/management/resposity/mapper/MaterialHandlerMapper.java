package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.dto.HandlerDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialHandler;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 处理人信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 16:38:04
 */
public interface MaterialHandlerMapper extends BaseMapper<MaterialHandler> {

    /**
     * 获取该项目下指定处理类型的所有处理人员
     * @param projectId
     * @return
     */
    List<HandlerDTO> selectListByProjectIdAndHandleType(@Param("projectId") Integer projectId, @Param("handleType") Byte handleType);

    /**
     * 获取该公司下指定处理类型的所有处理人员
     * @param companyId
     * @return
     */
    List<HandlerDTO> selectListByCompanyIdAndHandleType(@Param("companyId") Integer companyId, @Param("handleType") Byte handleType);

    /**
     * 获取企业层处理人列表
     * @param companyId 企业id
     * @param handleType 处理类型
     * @return result
     */
    List<HandlerDTO> selectByCompanyIdAndProjectIdIsNullAndHandleType(@Param("companyId") Integer companyId, @Param("handleType") Byte handleType);
}
