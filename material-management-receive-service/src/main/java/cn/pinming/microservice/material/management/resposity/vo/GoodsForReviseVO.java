package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GoodsForReviseVO {
    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "材料id")
    private String materialId;

    @ApiModelProperty(value = "合同约定换算系数")
    private BigDecimal ratio;

    @ApiModelProperty("二级分类id")
    private String categoryId;

    @ApiModelProperty("二级分类名称")
    private String categoryName;

    @ApiModelProperty("材料名称")
    private String materialName;

    @ApiModelProperty("规格型号")
    private String materialSpec;

    @ApiModelProperty("材料全称")
    private String type;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;

    @ApiModelProperty("计划使用部位")
    private String remark;

}
