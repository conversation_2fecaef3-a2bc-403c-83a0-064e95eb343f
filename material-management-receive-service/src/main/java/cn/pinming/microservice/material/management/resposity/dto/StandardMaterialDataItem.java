package cn.pinming.microservice.material.management.resposity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class StandardMaterialDataItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "本地称重记录id，MaterialDataItem的主键")
    private String weighId;

    @ApiModelProperty(value = "企业材料id")
    private String materialId;

    @ApiModelProperty(value = "外部系统材料id")
    private String clientMaterialId;

    @ApiModelProperty(value = "企业材料编码")
    private String materialCode;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "品种/规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "计量单位")
    private String weightUnit;

    @ApiModelProperty(value = "发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "毛重(吨)")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重(吨)")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重(吨)")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重(吨)")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重(吨)")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceiveCount;

    @ApiModelProperty(value = "单价：元/计量单位")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额：单价 * 实收数量")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "毛重时间")
    private LocalDateTime grossTime;

    @ApiModelProperty(value = "进场图片")
    private List<String> grossPicList;

    @ApiModelProperty(value = "毛重地磅id")
    private String grossPoundId;

    @ApiModelProperty(value = "毛重地磅名称")
    private String grossPoundName;

    @ApiModelProperty(value = "皮重时间")
    private LocalDateTime tareTime;

    @ApiModelProperty(value = "出场图片")
    private List<String> tarePicList;

    @ApiModelProperty(value = "皮重地磅id")
    private String tarePoundId;

    @ApiModelProperty(value = "皮重地磅名称")
    private String tarePoundName;

    @ApiModelProperty(value = "收发货id")
    private String sendReceiveId;

    @ApiModelProperty(value = "偏差")
    private BigDecimal deviation;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差")
    private Byte deviationStatus;

    @ApiModelProperty(value = "是否预警")
    private Boolean isWarning;

    @ApiModelProperty(value = "预警类型 超正差 超负差")
    private String warningType;

    @ApiModelProperty(value = "材料分类id")
    private Integer materialCategoryId;

    @ApiModelProperty(value = "材料分类名称")
    private String materialCategoryName;
}
