package cn.pinming.microservice.material.management.controller;

import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.resposity.query.ReceiveOverviewCardQuery;
import cn.pinming.microservice.material.management.resposity.query.ReceiveOverviewModalQuery;
import cn.pinming.microservice.material.management.resposity.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.DeviationOverviewSecondVO;
import cn.pinming.microservice.material.management.resposity.vo.DeviationSecondSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.DeviationSecondVO;
import cn.pinming.microservice.material.management.resposity.vo.KeyValVO;
import cn.pinming.microservice.material.management.resposity.vo.OverviewTrendVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveOverviewCardVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveOverviewHistogram;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveOverviewSecondVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryDeliverySecondVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryDeliveryVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryWarningVO;
import cn.pinming.microservice.material.management.resposity.vo.WarningOverviewSecondVO;
import cn.pinming.microservice.material.management.resposity.vo.WarningOverviewTipsVO;
import cn.pinming.microservice.material.management.resposity.vo.WarningSecondVO;
import cn.pinming.microservice.material.management.service.ISummaryAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 收料总览/收料偏差总览
 *
 * <AUTHOR>
 * @version 2021/9/6 10:37 上午
 */
@Api(tags = "总览分析", value = "lh")
@RestController
@RequestMapping("/api/summary/analysis")
@AllArgsConstructor
public class SummaryAnalysisController {

    public final ISummaryAnalysisService summaryAnalysisService;

    @Resource
    private AuthUserHolder authUserHolder;

    @ApiOperation(value = "汇总", response = SummaryAnalysisVO.class)
    @PostMapping
    public ResponseEntity<Response> summary(@RequestBody SummaryDeliveryQuery query) {
        SummaryAnalysisVO result = summaryAnalysisService.querySummary(query);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "收料总览 - 地磅收料", response = CategoryReceiveVO.class)
    @PostMapping("/receive")
    public ResponseEntity<Response> receive(@RequestBody SupplierAnalysisQuery query) {
        List<CategoryReceiveVO> list = summaryAnalysisService.listReceiveByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-收料总览 iteration  二级下钻页", response = SummaryDeliverySecondVO.class)
    @PostMapping("/overview/receive/second")
    public ResponseEntity<Response> receiveIterationSecond(@RequestBody SummaryDeliveryQuery query) {
        List<ReceiveOverviewSecondVO> list = summaryAnalysisService.listReceiveOverviewSecondByQuery(query, (byte) 1);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-发料总览 iteration  二级下钻页", response = SummaryDeliverySecondVO.class)
    @PostMapping("/overview/send/second")
    public ResponseEntity<Response> sendIterationSecond(@RequestBody SummaryDeliveryQuery query) {
        List<ReceiveOverviewSecondVO> list = summaryAnalysisService.listReceiveOverviewSecondByQuery(query, (byte) 2);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料总览 - 移动收料", response = CategoryReceiveVO.class)
    @PostMapping("/mobileReceive")
    public ResponseEntity<Response> mobileReceive(@RequestBody SupplierAnalysisQuery query) {
        List<CategoryReceiveVO> list = summaryAnalysisService.countMobileReceiveNum(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览 - 偏差占比趋势", response = ReceiveDeviationVO.class)
    @PostMapping("/deviation")
    public ResponseEntity<Response> deviation(@RequestBody SupplierAnalysisQuery query) {
        List<ReceiveDeviationVO> list = summaryAnalysisService.listDeviationByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览 - 变差占比累计", response = ReceiveDeviationSummaryVO.class)
    @PostMapping("/deviation/summary")
    public ResponseEntity<Response> deviationSummary(@RequestBody SupplierAnalysisQuery query) {
        List<ReceiveDeviationSummaryVO> list = summaryAnalysisService.listDeviationSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "发料总览", response = SummaryDeliveryVO.class)
    @PostMapping("/delivery")
    public ResponseEntity<Response> summaryDelivery(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryDeliveryVO> list = summaryAnalysisService.listSummaryDeliveryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "发料总览-二级下钻页", response = SummaryDeliverySecondVO.class)
    @PostMapping("/delivery/second")
    public ResponseEntity<Response> summaryDeliverySecond(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryDeliverySecondVO> list = summaryAnalysisService.listSummaryDeliverySecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "预警总览", response = SummaryWarningVO.class)
    @PostMapping("/warning")
    public ResponseEntity<Response> summaryWarning(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryWarningVO> list = summaryAnalysisService.listSummaryWarningByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "预警总览-二级下钻页")
    @PostMapping("/warning/second")
    public ResponseEntity<Response> summaryWarningSecond(@RequestBody SummaryDeliveryQuery query) {
        List<WarningSecondVO> list = summaryAnalysisService.listWarningSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }


    @ApiOperation(value = "标题-预警总览-overview-二级下钻页")
    @PostMapping("/overview/warning/second")
    public ResponseEntity<Response> overviewWarningSecond(@RequestBody SummaryDeliveryQuery query) {
        List<WarningOverviewSecondVO> list = summaryAnalysisService.listWarningOverviewSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-收料偏差总览-二级下钻页")
    @PostMapping("/overview/deviation/second")
    public ResponseEntity<Response> deviationOverviewSecond(@RequestBody SummaryDeliveryQuery query) {
        List<DeviationOverviewSecondVO> list = summaryAnalysisService.listDeviationOverviewSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览-二级下钻页")
    @PostMapping("/deviation/second")
    public ResponseEntity<Response> deviationSecond(@RequestBody SupplierAnalysisQuery query) {
        List<DeviationSecondVO> list = summaryAnalysisService.listDeviationSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览-二级下钻页-偏差累计")
    @PostMapping("/deviation/second/count")
    public ResponseEntity<Response> deviationSecondCount(@RequestBody SupplierAnalysisQuery query) {
        List<DeviationSecondSummaryVO> list = summaryAnalysisService.listDeviationSecondSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "总览 - 收料总览 - 卡片", response = ReceiveOverviewCardVO.class)
    @PostMapping("/overview/receive/card")
    public ResponseEntity<Response> receiveOverviewCard(@RequestBody ReceiveOverviewCardQuery query) {
        List<ReceiveOverviewCardVO> list = summaryAnalysisService.receiveOverviewCard(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "总览 - 收料总览 - 其他物料", response = ReceiveOverviewCardVO.class)
    @PostMapping("/overview/receive/other")
    public ResponseEntity<Response> receiveOverviewOther(@RequestBody ReceiveOverviewCardQuery query) {
        Map<String, List<ReceiveOverviewHistogram>> map = summaryAnalysisService.receiveOverviewOther(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "总览 - 收料总览 - 柱状图下钻")
    @PostMapping("/overview/receive/card/second")
    public ResponseEntity<Response> receiveOverviewCardSecond(@RequestBody ReceiveOverviewModalQuery query) {
        Map<String, Object> map = summaryAnalysisService.receiveOverviewModal(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "总览 - 异常提示", response = WarningOverviewTipsVO.class)
    @GetMapping("/overview/tips/{deptId}")
    public ResponseEntity<Response> overviewWarningTips(@PathVariable Integer deptId) {
        WarningOverviewTipsVO warningOverviewTipsVO = summaryAnalysisService.warningOverviewTip(deptId);
        return ResponseEntity.ok(new SuccessResponse(warningOverviewTipsVO));
    }

    // ================== 统计新需求 ==================

    @ApiOperation(value = "总体概览 - 本年主要材料占比", response = KeyValVO.class)
    @GetMapping("/overview/total/{deptId}")
    public ResponseEntity<Response> overviewTotal(@PathVariable Integer deptId) {
        List<KeyValVO> result = summaryAnalysisService.overviewTotal(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "总体概览 - 本月数据来源", response = KeyValVO.class)
    @GetMapping("/overview/month/{deptId}")
    public ResponseEntity<Response> overviewMonth(@PathVariable Integer deptId) {
        List<KeyValVO> result = summaryAnalysisService.overviewMonth(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "总体概览 - 本年数据来源", response = KeyValVO.class)
    @GetMapping("/overview/year/{deptId}")
    public ResponseEntity<Response> overviewYear(@PathVariable Integer deptId) {
        List<KeyValVO> result = summaryAnalysisService.overviewYear(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "总体概览 - 本年负差监控英雄榜", response = KeyValVO.class)
    @GetMapping("/overview/diff/project/{deptId}")
    public ResponseEntity<Response> overviewDiffProject(@PathVariable Integer deptId) {
        List<KeyValVO> result = summaryAnalysisService.overviewDiffProject(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }


    @ApiOperation(value = "总体概览 - 近一年综合超负差情况走势", response = OverviewTrendVO.class)
    @GetMapping("/overview/trend/{deptId}")
    public ResponseEntity<Response> overviewTrend(@PathVariable Integer deptId) {
        OverviewTrendVO result = summaryAnalysisService.overviewTrend(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "总体概览 - 本年供货偏差情况分布", response = KeyValVO.class)
    @GetMapping("/overview/diff/{deptId}")
    public ResponseEntity<Response> overviewDiff(@PathVariable Integer deptId) {
        List<KeyValVO> result = summaryAnalysisService.overviewDiff(deptId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

}
