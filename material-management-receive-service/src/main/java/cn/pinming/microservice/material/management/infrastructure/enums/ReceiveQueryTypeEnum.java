package cn.pinming.microservice.material.management.infrastructure.enums;

public enum ReceiveQueryTypeEnum {
    DEFULT((byte) 0, "默认"),
    ALL((byte) 1, "全部"),
    WEIGHBRIDGE((byte) 2, "地磅收料"),
    MOBILE((byte) 3, "移动收料"),

    RECORD_RECEIVE((byte) 11, "报备收料"),
    TEMP_RECEIVE((byte) 12, "临时收料"),

    CONTRACT((byte) 21, ReceiveTypeEnum.CONTRACT, "有合同-按合同"),
    PURCHASE_ORDER((byte) 22, ReceiveTypeEnum.PURCHASE, "有合同-按采购单"),
    NO_CONTRACT((byte) 23, ReceiveTypeEnum.NONE, "无合同"),
    ;

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String          description;
    private       ReceiveTypeEnum receiveType;

    ReceiveQueryTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    ReceiveQueryTypeEnum(byte value, ReceiveTypeEnum receiveType, String description) {
        this.value = value;
        this.receiveType = receiveType;
        this.description = description;
    }

    public static ReceiveQueryTypeEnum getReceiveQueryTypeByValue(Byte value) {
        if (value == null) {
            return DEFULT;
        }
        for (ReceiveQueryTypeEnum statusEnum : values()) {
            if (statusEnum.value() == value.intValue()) {
                return statusEnum;
            }
        }
        return DEFULT;
    }

    public byte value() {
        return value;
    }

    public ReceiveTypeEnum receiveType() {
        return receiveType;
    }

    public Byte getReceiveType() {
        return value == 0 ? null : receiveType == null ? null : receiveType.value();
    }

    public String description() {
        return description;
    }
}
