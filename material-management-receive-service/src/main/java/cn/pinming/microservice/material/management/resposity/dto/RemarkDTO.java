package cn.pinming.microservice.material.management.resposity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2022/4/18 22:25
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RemarkDTO {

    private String preReportId;

    private Integer companyId;

    private Integer projectId;

    private String weighId;

    private Integer supplierId;

    private String supplierName;

    private BigDecimal actualReceive;

    private String receiverName;

}
