package cn.pinming.microservice.material.management.resposity.mapper;


import cn.pinming.microservice.material.management.resposity.dto.WarningDetailDTO;
import cn.pinming.microservice.material.management.resposity.dto.WarningInfoDTO;
import cn.pinming.microservice.material.management.resposity.dto.WarningOverviewSecondDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarning;
import cn.pinming.microservice.material.management.resposity.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.resposity.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.resposity.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.resposity.vo.KeyValVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryWarningSecondVO;
import cn.pinming.microservice.material.management.resposity.vo.SummaryWarningVO;
import cn.pinming.microservice.material.management.resposity.vo.WarningSummaryAnalysisVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
public interface MaterialWarningMapper extends BaseMapper<MaterialWarning> {

    IPage<WarningInfoDTO> selectPageByQuery(@Param("query") WarningInfoQuery warningInfoQuery);

    List<WarningDetailDTO> listWarningDetail(@Param("query") WarningDetailQuery warningDetailQuery);

    WarningSummaryAnalysisVO queryWarningSummary(SummaryDeliveryQuery query);

    List<SummaryWarningVO> listSummaryWarningByQuery(@Param("query") SummaryDeliveryQuery query);

    List<SummaryWarningSecondVO> listSummaryWarningSecondByQuery(@Param("query") SummaryDeliveryQuery query);

    List<WarningOverviewSecondDTO> queryOverviewWarningSummary(@Param("query") SummaryDeliveryQuery query);

    Integer queryOverviewWarningTips(@Param("companyId") Integer companyId, @Param("projectIds") List<Integer> projectIds, @Param("warningTypeList") List<Byte> warningTypeList);

    List<KeyValVO> selectOverviewDiffProject(@Param("companyId") Integer companyId, @Param("projectIds") List<Integer> projectIdList);
}
