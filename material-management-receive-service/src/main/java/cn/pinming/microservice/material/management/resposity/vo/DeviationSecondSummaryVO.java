package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 收料偏差总览-二级下钻页-偏差累计
 *
 * <AUTHOR>
 * @since 2022/4/14 15:53
 */
@Data
public class DeviationSecondSummaryVO {

    @ApiModelProperty(value = "单位id")
    private Integer projectId;

    @ApiModelProperty(value = "单位名称")
    private String projectName;

    @ApiModelProperty(value = "偏差累计")
    private List<DeviationSecondSummaryDetailVO> deviationSecondSummaryDetailVOList;
}
