package cn.pinming.microservice.material.management.infrastructure.enums;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预警类型枚举
 * <AUTHOR>
 * @Date 2022/1/18 14:56
 */
public enum WarningTypeEnum {

    WEIGH_CONVERT((byte) 1, "称重转换系数异常"),
    TARE_GROSS_WEIGH((byte) 2, "毛/皮重异常"),
    RECEIVE_NUMBER((byte) 3, "面单应收数量异常"),
    CHARGE_UNIT((byte) 4, "结算单位异常"),
    NEGATIVE_DEVIATION((byte) 5, "超负差"),
    INVALID_WEIGHT((byte) 6, "无效称重"),
    MATERIAL_NAME((byte) 7, "物料名称异常"),
    TIMEOUT_NO_OUT((byte) 8, "超时未出场"),
    TIMEOUT_NO_ENTER((byte) 9, "超期未进场"),
    TARE_DEVIATION((byte) 10, "皮重偏差异常"),
    WEIGH_INTEGRITY((byte)11,"不完整"),
    WEIGH_REPETITION((byte)12, "重复过磅异常"),
    MATERIAL_OUT((byte)13, "物料出场异常")
    ;


    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WarningTypeEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public static String getWarningTypeDesc(Byte key) {
        List<WarningTypeEnum> warningTypeEnumList = Arrays.stream(WarningTypeEnum.values())
                .filter(type -> type.value() == key).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(warningTypeEnumList)) {
            return warningTypeEnumList.get(0).description();
        }
        return null;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }


}
