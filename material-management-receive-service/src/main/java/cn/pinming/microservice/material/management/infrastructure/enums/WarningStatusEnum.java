package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 * @Date 2022/1/18 15:29
 */
public enum WarningStatusEnum {

    UNHANDLED((byte) 1, "待处理"),
    HANDLED((byte) 2, "已处理");
    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WarningStatusEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }
}
