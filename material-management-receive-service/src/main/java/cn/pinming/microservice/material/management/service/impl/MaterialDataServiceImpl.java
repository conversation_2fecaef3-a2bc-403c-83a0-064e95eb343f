package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.dto.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.base.common.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.management.dto.SdkConfigDTO;
import cn.pinming.microservice.base.management.service.ISdkConfService;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.ReceiveQueryTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.listener.WeighDataListener;
import cn.pinming.microservice.material.management.infrastructure.util.FileDownLoadUtil;
import cn.pinming.microservice.material.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.management.infrastructure.util.PicEchoUtil;
import cn.pinming.microservice.material.management.infrastructure.util.UUIDUtil;
import cn.pinming.microservice.material.management.infrastructure.util.WeighDuplicationUtil;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataDetailDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataExpandDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.form.MobileMaterialBatchForm;
import cn.pinming.microservice.material.management.resposity.form.RefreshPicForm;
import cn.pinming.microservice.material.management.resposity.form.SDKStandardMaterialForm;
import cn.pinming.microservice.material.management.resposity.form.WeighReceiveFixForm;
import cn.pinming.microservice.material.management.resposity.form.WeighSendFixForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.resposity.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialDatasVO;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.RefreshPicVO;
import cn.pinming.microservice.material.management.resposity.vo.SDKHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.SuccessOrFailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDeviationVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO;
import cn.pinming.microservice.material.management.resposity.vo.WeighReceiveVO;
import cn.pinming.microservice.material.management.service.IMaterialDataExpandService;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.service.save.IMaterialDataSaveService;
import cn.pinming.microservice.material.management.service.save.IMaterialReviseService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.purchase.management.service.IMaterialPurchaseService;
import cn.pinming.microservice.purchase.management.vo.PurchaseOrderVO;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 收货/发货明细 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
@Slf4j
@Service
public class MaterialDataServiceImpl extends ServiceImpl<MaterialDataMapper, MaterialData> implements IMaterialDataService {
    @DubboReference
    private IMaterialContractService    materialContractService;
    @Resource
    private ProjectServiceProxy         projectServiceProxy;
    @Resource
    private MaterialServiceProxy        materialServiceProxy;
    @Resource
    private SupplierServiceProxy        cooperateServiceProxy;
    @Resource
    private UserUtil                    userUtil;
    @Resource
    private EmployeeServiceProxy        employeeServiceProxy;
    @Resource
    private PicEchoUtil                 picEchoUtil;
    @Resource
    private IMaterialDataSaveService    materialDataSaveService;
    @Resource
    private FileDownLoadUtil            fileDownLoadUtil;
    @DubboReference
    private ISdkConfService             sdkConfService;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @DubboReference
    private IMaterialPurchaseService    materialPurchaseService;
    @Resource
    private IMaterialReviseService      materialReviseService;
    @Resource
    private NoUtil                      noUtil;
    @Resource
    private WeighDuplicationUtil        weighDuplicationUtil;
    @Resource
    private WeighDataListener           weighDataListener;
    @Resource
    private IMaterialDataExpandService  materialDataExpandService;
    @Resource
    private MobileReceiveMapper         mobileReceiveMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveSdk(SDKStandardMaterialForm form) {
        // 幂等校验
        weighDuplicationUtil.judge(Arrays.asList(form.getRecordId1(), form.getRecordId2()));
        // sendReceive
        String sendReceiveId = materialDataSaveService.SDKSendReceiveSave(form);
        // data
        String id = materialDataSaveService.SDKDataSave(form, sendReceiveId);
        // 内部推送
        weighDataListener.AfterCompletion(id, null, 1);
        return id;
    }

    @Override
    public SDKHistoryVO sdkHistory(Byte type) {
        AuthUser user = userUtil.getUser();
        SDKHistoryVO vo = this.getBaseMapper().sdkHistory(user.getCurrentProjectId(), type, user.getId());
        if (ObjectUtil.isNotNull(vo)) {
            if (StrUtil.isNotBlank(vo.getContractDetailId())) {
                SimpleContractDetailDTO simpleContractDetailDTO = materialContractService.querySimpleContractDetail(vo.getContractDetailId());
                if (ObjectUtil.isNotNull(simpleContractDetailDTO)) {
                    vo.setContractUnit(simpleContractDetailDTO.getUnit());
                    vo.setContractRatio(simpleContractDetailDTO.getConversionRate());
                    vo.setDeviationFloor(simpleContractDetailDTO.getDeviationFloor());
                    vo.setDeviationCeiling(simpleContractDetailDTO.getDeviationCeiling());
                    vo.setCategoryName(simpleContractDetailDTO.getCategoryName());
                    vo.setMaterialName(simpleContractDetailDTO.getMaterialName());
                    vo.setMaterialSpec(simpleContractDetailDTO.getMaterialSpec());
                    vo.setCategoryId(simpleContractDetailDTO.getCategoryId());
                    vo.setDeviationCalculate(simpleContractDetailDTO.getDeviationCalculate());
                }
            } else if (StrUtil.isBlank(vo.getContractDetailId()) && ObjectUtil.isNotNull(vo.getMaterialId())) {
                MaterialDto materialDto = materialServiceProxy.materialById(vo.getMaterialId());
                if (ObjectUtil.isNotNull(materialDto)) {
                    vo.setMaterialName(materialDto.getMaterialName());
                    vo.setCategoryName(materialDto.getMaterialCategoryName());
                    vo.setMaterialSpec(materialDto.getMaterialSpec());
                    vo.setCategoryId(materialDto.getMaterialCategoryId());
                }
            }
        }
        return vo;
    }

    @Override
    public void refreshPic(String id) {
        MaterialData one = this.lambdaQuery()
                .eq(MaterialData::getId, id)
                .one();
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(userUtil.getCompanyId(), userUtil.getProjectId());

        if (ObjectUtil.isNotNull(one) && ObjectUtil.isNotNull(sdkConfig)) {
            RefreshPicForm form = new RefreshPicForm();
            form.setRecord1(one.getRecordId1());
            form.setRecord2(one.getRecordId2());

            String res = null;
            try {
                // ocr服务
                res = HttpUtil.post(sdkConfig.getHost() + "/api/common/refreshPic", JSON.toJSONString(form));
            } catch (Exception e) {
                throw new BOException("-233", "基石服务调用失败");
            }
            Object resbody = JSONObject.parseObject(res).get("data");
            if (ObjectUtil.isNotNull(resbody)) {
                RefreshPicVO picByTwoRecordId = JSONUtil.toBean(resbody.toString(), RefreshPicVO.class);

                if (ObjectUtil.isNotNull(picByTwoRecordId)) {
                    String enterPic = picByTwoRecordId.getEnterPic();
                    String leavePic = picByTwoRecordId.getLeavePic();
                    String enter = null;
                    String leave = null;

                    if (StrUtil.isNotBlank(enterPic)) {
                        enter = StrUtil.split(enterPic, ",").stream().map(e -> {
                            return fileDownLoadUtil.downloadAndUploadFile(e, UUIDUtil.randomUUIDWithoutConnector(), null);
                        }).collect(Collectors.joining(","));
                    }
                    if (StrUtil.isNotBlank(leavePic)) {
                        leave = StrUtil.split(leavePic, ",").stream().map(e -> {
                            return fileDownLoadUtil.downloadAndUploadFile(e, UUIDUtil.randomUUIDWithoutConnector(), null);
                        }).collect(Collectors.joining(","));
                    }

                    one.setEnterPic(enter);
                    one.setLeavePic(leave);
                    if (StrUtil.isNotBlank(enter) || StrUtil.isNotBlank(leave)) {
                        this.updateById(one);
                    }
                }
            }
        }
    }

    @Override
    public WeighDataConfirmDetailDTO confirmDetail(String id) {
        MaterialData one = this.lambdaQuery()
                .select(MaterialData::getWeighId)
                .eq(MaterialData::getId, id)
                .one();
        if (ObjectUtil.isNull(one)) {
            return null;
        }

        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(userUtil.getCompanyId(), userUtil.getProjectId());
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        return materialClient.confirmDetail(one.getWeighId());
    }

    @Override
    public MaterialReviseDetailVO mobileHistoryByType(Byte type, Byte receiveType) {
        MaterialReviseDetailVO vo = new MaterialReviseDetailVO();
        AuthUser user = userUtil.getUser();
        MaterialDataExpandDTO materialData = this.getBaseMapper().mobileHistoryByType(type, receiveType, user.getCurrentCompanyId(), user.getCurrentProjectId());

        if (ObjectUtil.isNotNull(materialData)) {
            MaterialSendReceive sendReceive = materialSendReceiveService.lambdaQuery()
                    .eq(MaterialSendReceive::getId, materialData.getReceiveId())
                    .one();
            PurchaseOrderVO purchaseOrder = materialPurchaseService.getPurchaseOrder(materialData.getPurchaseOrderId());
            SimpleContractDetailDTO contractDetail = materialContractService.querySimpleContractDetail(materialData.getContractDetailId());
            if (ObjectUtil.isNotEmpty(purchaseOrder)) {
                vo.setOrderNo(purchaseOrder.getOrderNo());
            }
            vo.setMaterialId(String.valueOf(materialData.getMaterialId()));
            if (materialData.getMaterialId() != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
                Optional.ofNullable(materialDto).ifPresent(e -> vo.setMaterialName(e.getMaterialName() + e.getMaterialSpec()));
            }
            vo.setSupplierId(materialData.getSupplierId());
            if (materialData.getSupplierId() != null) {
                SupplierDTO supplierDTO = cooperateServiceProxy.findById(materialData.getSupplierId());
                Optional.ofNullable(supplierDTO).ifPresent(e -> vo.setSupplierName(e.getName()));
            }
            if (ObjectUtil.isNotNull(contractDetail)) {
                vo.setDeviationCeiling(contractDetail.getDeviationCeiling());
                vo.setDeviationFloor(contractDetail.getDeviationFloor());
                vo.setConversionRate(contractDetail.getConversionRate());
            }

            vo.setExtNo(sendReceive.getExtNo());
            vo.setPurchaseOrderId(materialData.getPurchaseOrderId());
            vo.setContractDetailId(materialData.getContractDetailId());
            vo.setRatio(materialData.getRatio());
            vo.setTypeDetail(sendReceive.getTypeDetail());
            vo.setUnit(materialData.getWeightUnit());
            vo.setMoistureContent(materialData.getMoistureContent());
            vo.setPosition(materialData.getPosition());
            vo.setDischargePoint(materialData.getDischargePoint());
        }

        return vo;
    }

    /**
     * 地磅收货明细
     *
     * @param query
     * @return
     */
    @Override
    public WeighReceiveVO showWeightDetail(MaterialWeighQuery query) {
        WeighReceiveVO vo = new WeighReceiveVO();
        AuthUser user = userUtil.getUser();
        query.setCompanyId(user.getCurrentCompanyId());
        MaterialDataDetailDTO dto = this.getBaseMapper().selectWeightDetail(query);
        if (ObjectUtil.isNotEmpty(dto)) {
            BeanUtils.copyProperties(dto, vo);

            // 收料人
            EmployeeSimpleDTO employee = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), dto.getCreateId());
            if (Objects.nonNull(employee)) {
                vo.setCreateName(employee.getMemberName());
            }
            // 收货项目
            ProjectDTO projectDto = projectServiceProxy.getProjectById(dto.getReceiverProject());
            if (ObjectUtil.isNotNull(projectDto)) {
                vo.setProjectName(projectDto.getProjectTitle());
            }
            //  供应商
            if (dto.getSupplierId() != null) {
                List<SupplierDTO> supplierVO = cooperateServiceProxy.findListByIds(query.getCompanyId(), Collections.singletonList(dto.getSupplierId()));
                if (CollUtil.isNotEmpty(supplierVO)) {
                    vo.setSupplierName(supplierVO.get(0).getName());
                }
            }

            List<MaterialDatasVO> materialDataVOList = this.getBaseMapper().selectWeighDetailsWithPurchase(query);
            if (CollUtil.isNotEmpty(materialDataVOList)) {
                materialDataVOList.forEach(e -> {
                    // 材料类目
                    if (e.getMaterialId() != null) {
                        MaterialDto materialDto = materialServiceProxy.materialById(e.getMaterialId());
                        if (ObjectUtil.isNotEmpty(materialDto)) {
                            e.setMaterialCategoryName(materialDto.getMaterialCategoryName());
                            e.setMaterialName(materialDto.getMaterialName());
                            e.setMaterialSpec(materialDto.getMaterialSpec());
                            String str = StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec());
                            e.setType(str);
                        }
                    }
                    if (e.getMaterialId() == null && StrUtil.isNotBlank(e.getMaterialName())) {
                        e.setType(e.getMaterialName());
                    }

                    // 偏差状态
                    e.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(e.getDeviationStatusByte()));
                });
            }
            vo.setList(materialDataVOList);

            // 处理图片
            vo.setEnterPics(picEchoUtil.echo(vo.getEnterPic(), 4));
            vo.setLeavePics(picEchoUtil.echo(vo.getLeavePic(), 4));
            vo.setDocumentPics(picEchoUtil.echo(vo.getDocumentPic(), -1));
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SuccessOrFailVO batch(List<MobileMaterialBatchForm> list) {
        AuthUser user = userUtil.getUser();
        list.forEach(e -> e.setPjId(user.getCurrentProjectId()));
        list.forEach(e -> e.setCoId(user.getCurrentCompanyId()));
        // 去重
        list = new ArrayList<>(list.stream().collect(Collectors.toMap(
                e -> new AbstractMap.SimpleEntry<>(e.getRecordId1(), e.getRecordId2()),
                e -> e,
                (existing, replacement) -> existing
        )).values());
        // 收料
        List<MobileMaterialBatchForm> receive = list.stream().filter(e -> e.getKind() == 1).collect(Collectors.toList());
        // 发料
        List<MobileMaterialBatchForm> send = list.stream().filter(e -> e.getKind() == 2).collect(Collectors.toList());
        log.info("本次批量上传{}条发料数据", send.size());
        log.info("本次批量上传{}条收料数据", receive.size());

        SuccessOrFailVO successOrFailVO = new SuccessOrFailVO();
        Map<String, String> errorMap = new HashMap<>();
        // 处理收料
        if (CollUtil.isNotEmpty(receive)) {
            // 更新
            List<MobileMaterialBatchForm> update = receive.stream().filter(MobileMaterialBatchForm::getExist).collect(Collectors.toList());
            // 新增
            List<MobileMaterialBatchForm> add = receive.stream().filter(e -> !e.getExist()).collect(Collectors.toList());

            // 批量更新
            if (CollUtil.isNotEmpty(update)) {
                update.forEach(e -> {
                    WeighReceiveFixForm form = new WeighReceiveFixForm();
                    receiveFixTrans(e, form);
                    try {
                        this.billFix(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
            // 批量新增
            if (CollUtil.isNotEmpty(add)) {
                add.forEach(e -> {
                    SDKStandardMaterialForm form = new SDKStandardMaterialForm();
                    e.setSNo(noUtil.getReceiveNo(String.valueOf(e.getPjId())));
                    receiveCommitTrans(e, form);
                    try {
                        this.saveSdk(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
        }

        // 处理发料
        if (CollUtil.isNotEmpty(send)) {
            // 更新
            List<MobileMaterialBatchForm> update = send.stream().filter(e -> e.getExist() == true).collect(Collectors.toList());
            // 新增
            List<MobileMaterialBatchForm> add = send.stream().filter(e -> e.getExist() == false).collect(Collectors.toList());

            // 批量更新
            if (CollUtil.isNotEmpty(update)) {
                update.forEach(e -> {
                    WeighSendFixForm form = new WeighSendFixForm();
                    sendFixTrans(e, form);
                    try {
                        materialReviseService.sendFix(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
            // 批量新增
            if (CollUtil.isNotEmpty(add)) {
                add.forEach(e -> {
                    SDKStandardMaterialForm form = new SDKStandardMaterialForm();
                    receiveCommitTrans(e, form);
                    try {
                        this.saveSdk(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
        }
        successOrFailVO.setErrorMap(errorMap);

        return successOrFailVO;
    }

    @Override
    public SupplierAnalysisVO getSupplierAnalysisByQuery(SupplierAnalysisQuery query) {
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            // 报备收料、临时收料
            return this.getBaseMapper().selectSupplierAnalysisByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            // 移动收料 有合同
            return mobileReceiveMapper.selectSupplierAnalysisByMobileQuery(query);
        } else {
            // 全部
            return this.getBaseMapper().selectSupplierAnalysisUnionByQuery(query);
        }
    }

    @Override
    public List<SupplierAnalysisDetailVO> getSupplierAnalysisPageVO(SupplierAnalysisQuery query) {
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            // 报备收料、临时收料
            return this.getBaseMapper().selectSupplierAnalysisPageVO(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            // 移动收料 有合同
            return mobileReceiveMapper.selectSupplierMobileAnalysisPageVO(query);
        } else {
            // 全部
            return this.getBaseMapper().selectSupplierUnionAnalysisPageVO(query);
        }
    }

    /**
     * 对象转换
     */
    private void receiveFixTrans(MobileMaterialBatchForm e, WeighReceiveFixForm form) {
        BigDecimal weightActual = NumberUtil.sub(NumberUtil.sub(e.getGWeight(), e.getTWeight()), e.getBWeight());
        BeanUtils.copyProperties(e, form);
        form.setActualCount(NumberUtil.mul(e.getRatio(), weightActual));
        form.setTruckNo(e.getTNo());
        form.setWeightDeduct(e.getBWeight());
        form.setWeightGross(e.getGWeight());
        form.setWeightTare(e.getTWeight());
        form.setTUuid(e.getTUuid());
        form.setIsAutoVerify((byte) 2);
        form.setIsScanReceive(true);
    }

    /**
     * 对象转换
     */
    private void receiveCommitTrans(MobileMaterialBatchForm e, SDKStandardMaterialForm form) {
        form.setType(e.getKind());
        form.setTypeDetail(e.getTypeDetail());
        form.setSupplierId(e.getSupplierId());
        form.setContractDetailId(e.getContractDetailId());
        form.setPurchaseOrderId(e.getPurchaseOrderId());
        form.setMaterialId(e.getMaterialId());
        form.setCategoryId(e.getCategoryId());
        form.setUnit(e.getWeightUnit());
        form.setDeviationCeiling(e.getDeviationCeiling());
        form.setDeviationFloor(e.getDeviationFloor());
        form.setWeightGross(e.getGWeight());
        form.setWeightTare(e.getTWeight());
        form.setWeightDeduct(e.getBWeight());
        form.setMoistureContent(e.getMoistureContent());
        form.setWeightSend(e.getWeightSend());
        form.setRatio(e.getRatio());
        form.setEnterTime(e.getGTime().isBefore(e.getTTime()) ? e.getGTime() : e.getTTime());
        form.setLeaveTime(e.getGTime().isBefore(e.getTTime()) ? e.getTTime() : e.getGTime());
        form.setTruckNo(e.getTNo());
        form.setEnterPic(e.getEnterPics());
        form.setLeavePic(e.getLeavePics());
        form.setDocumentPicUuid(e.getDocumentPics());
        form.setRecordId1(e.getRecordId1());
        form.setRecordId2(e.getRecordId2());
        form.setWbsId(e.getWbsId());
        form.setPosition(e.getPosition());
        form.setExtNo(e.getExtNo());
        form.setRemark(e.getRemark());
        form.setReceiveUnit(e.getReceiveUnit());
        form.setDeviationCalculate(e.getDeviationCalculate());
        form.setReceiveProject(e.getReceiveProject());
        form.setDischargePoint(e.getDischargePoint());
    }

    /**
     * 对象转换
     */
    private void sendFixTrans(MobileMaterialBatchForm e, WeighSendFixForm form) {
        BigDecimal weightActual = NumberUtil.sub(NumberUtil.sub(e.getGWeight(), e.getTWeight()), e.getBWeight());
        BeanUtils.copyProperties(e, form);
        form.setSendId(e.getReceiveId());
        form.setActualCount(NumberUtil.mul(e.getRatio(), weightActual));
        form.setIsAutoVerify((byte) 2);
    }

    public void billFix(WeighReceiveFixForm form) {
        MaterialData materialData = this.getOne(new LambdaQueryWrapper<MaterialData>().eq(MaterialData::getWeighId, form.getTUuid()), false);
        // 参数组装
        form.setId(materialData.getId());
        form.setReceiveId(materialData.getReceiveId());
        form.setWeightGross(materialData.getWeightGross());
        form.setWeightTare(materialData.getWeightTare());

        // 调用修正接口
        materialReviseService.add(form);
    }

    @Override
    public List<SupplierRankVO> deductRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductRankByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductTotalRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductTotalRankByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductTotalProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductTotalProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyRankListByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyMobileRankListByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyAllRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyAllRankListByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalMobileProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalAllProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalRankByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalMobileRankByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalAllRankByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyMobileProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyAllProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyAllProportionByQuery(query);
    }
}
