package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/4/7 11:12
 */
@Data
public class MaterialTruckInfoVO {

    @ApiModelProperty("dataId")
    private String id;

    @ApiModelProperty("receiveId")
    private String receiveId;

    @ApiModelProperty("过磅单号")
    private String receiveNo;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("供应商id")
    private Integer supplierId;

    @ApiModelProperty("供应商name")
    private String supplierName;

    @ApiModelProperty("材料id")
    private Integer materialId;

    @ApiModelProperty("材料name")
    private String materialName;

    @ApiModelProperty("规格")
    private String materialSpec;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("净重")
    private BigDecimal weightNet;

    @ApiModelProperty("毛重时间")
    private LocalDateTime grossTime;

    @ApiModelProperty("皮重时间")
    private LocalDateTime tareTime;

    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty("出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty("车型")
    private Integer carType;
}
