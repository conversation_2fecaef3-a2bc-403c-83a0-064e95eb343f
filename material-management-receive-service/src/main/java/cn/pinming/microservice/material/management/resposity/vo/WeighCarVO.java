package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WeighCarVO {

    @ApiModelProperty("节点名称")
    private String pointName;

    @ApiModelProperty("车辆累积量")
    private Integer carTotal;

    @ApiModelProperty("车辆本月新增量")
    private Integer carMonthly;

    @ApiModelProperty("车辆统计列表")
    private List<WeighCarDetailVO> list;
}
