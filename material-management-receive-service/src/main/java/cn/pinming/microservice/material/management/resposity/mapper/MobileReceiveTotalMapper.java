package cn.pinming.microservice.material.management.resposity.mapper;

import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料总计表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
public interface MobileReceiveTotalMapper extends BaseMapper<MobileReceiveTotal> {
    MobileReceiveHistoryVO selectHistory(@Param("totalId") String totalId);

    List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationVO> countDeviation(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationSummaryVO> countDeviationStatus(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);
}
