package cn.pinming.microservice.material.management.resposity.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2022/12/29 11:19
 */
@Data
public class MaterialCountQuery {

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    private LocalDate beginTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "结束时间不能为空")
    private LocalDate endTime;

    @ApiModelProperty("类型 1 总量 2 过磅收料数 3 过磅发料数 4 移动收料数")
    @NotNull(message = "查询类型不能为空")
    private Integer type;

    @ApiModelProperty("时间类型 today week month year")
    @NotBlank(message = "时间类型不能为空")
    private String timeType;

}
