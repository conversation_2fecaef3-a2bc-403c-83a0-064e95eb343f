package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveOverviewModal {

    @ApiModelProperty(value = "单位名称")
    private String name;

    @ApiModelProperty(value = "面单量")
    private Double y1;

    @ApiModelProperty(value = "实际量")
    private Double y2;

    @ApiModelProperty(value = "实收量")
    private Double y3;

}
