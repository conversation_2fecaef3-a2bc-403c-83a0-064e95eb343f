package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description
 */
@Data
public class ProjectConfigForm {

    @ApiModelProperty(value = "部门ID，多个用英文逗号分隔")
    @NotNull
    private String departmentIds;

    @ApiModelProperty(value = "项目ID，多个用英文逗号分隔")
    @NotNull
    private String projectIds;

    @ApiModelProperty(value = "项目状态(1在建 2完工 3筹备 4立项 5停工)，多个用英文逗号分隔")
    @NotNull
    private String status;

}
