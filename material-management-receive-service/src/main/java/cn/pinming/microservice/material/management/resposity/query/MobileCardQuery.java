package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MobileCardQuery extends BaseQuery {
    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "收料记录编号")
    private String receiveNo;

    @ApiModelProperty(value = "材料二级分类id")
    private Integer categoryId;

    @ApiModelProperty(value = "收货状态 1，合格进场；2，不合格进场")
    private Byte receiveStatus;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差")
    private Byte deviationStatus;

    @ApiModelProperty(value = "实际收料开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "实际收料结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "收料类型 1 按采购单 2 按合同 3 仅关联供应商 ")
    private Byte receiveType;

    @ApiModelProperty(value = "对账id")
    private String reconciliationId;

    @ApiModelProperty(value = "排序方式 1 按创建时间降序 2 按创建时间升序")
    private Integer sort;
}
