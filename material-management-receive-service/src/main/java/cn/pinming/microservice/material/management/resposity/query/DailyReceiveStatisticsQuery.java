package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * This class is for xxxx.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/1/29 09:57
 */
@Data
public class DailyReceiveStatisticsQuery {

    @ApiModelProperty("企业id")
    private Integer companyId;

    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty("是否合同收料: true 按合同收料 false 无合同收料")
    private boolean isContractReceive;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "1 按材料品种统计 即按物料品种进行数据归集; 2 按材料分类统计 即按物料的二级分类进行数据归集。 默认1 ")
    private Byte materialType = 1;

    @ApiModelProperty(value = "按合同收料: 1.全部 2.地磅收料(只统计“报备收料”、“临时收料”) 3.移动收料 (统计“有合同”收料); 按无合同收料: 2.地磅收料(只统计无归属收料) 3.移动收料 (无合同收料)")
    private Byte receiveType;

    @ApiModelProperty("统计合同范围设置")
    private List<String> contractRangeList;

    @ApiModelProperty("统计物料范围设置")
    private List<Integer> materialRangeList;

    @ApiModelProperty("导出时 - 名称单位关系映射列表")
    private List<MaterialUnitQuery> materialUnitList;

}
