package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/7 16:05
 */
public enum MaterialReviseEnum {
    ALREADY_REVISE((byte)1, "有修订"),
    NOT_REVISE((byte)2, "未修订");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    MaterialReviseEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (MaterialReviseEnum statusEnum : MaterialReviseEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
