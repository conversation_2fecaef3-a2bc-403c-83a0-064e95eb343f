package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 标题预警总览下钻
 *
 * <AUTHOR>
 * @since 2022/4/11 10:43
 */
@Data
public class WarningOverviewSecondVO {

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "累计")
    private Integer total;

    @ApiModelProperty(value = "本月新增")
    private Integer monthRise;

    @ApiModelProperty(value = "未处理")
    private Integer unHandle;

    @ApiModelProperty(value = "各单位每月预警次数")
    private List<WarningOverviewSecondDetailVO> warningOverviewSecondDetailVOS;
}
