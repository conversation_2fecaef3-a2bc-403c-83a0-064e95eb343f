package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MobileReceiveUpdateForm extends BaseForm{
    /**
     * 收货单id
     */
    @NotBlank(message = "收货单id不能为空")
    private String receiveId;

    @ApiModelProperty(value = "收料类型 1有合同收料-按合同，2有合同收料-按采购单，3无合同收料")
    @NotNull(message = "收料类型不能为空")
    private Byte receiveType;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "修订说明")
    private String reviseRemark;

    @ApiModelProperty(value = "验收材料总计")
    @Valid
    private List<MobileReceiveUpdateDetailForm> list;
}
