package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SendReceiveMultiForm {
    @ApiModelProperty(value = "id")
    private String id;

    // 收发料信息
    @NotNull(message = "过磅类型不能为空")
    @ApiModelProperty(value = "过磅类型 1 收料 2 发料")
    private Integer type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采 15 直入直出      发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    @NotNull(message = "收发料子类型不能为空")
    private Integer typeDetail;

    // 重量信息
    @ApiModelProperty(value = "毛重")
    @NotNull(message = "毛重不能为空")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    @NotNull(message = "皮重不能为空")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    // 进出场信息
    @ApiModelProperty(value = "进场时间")
    @NotNull(message = "进场时间不能为空")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    @NotNull(message = "出场时间不能为空")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "进场照片")
    private List<String> enterPic;

    @ApiModelProperty(value = "出场照片")
    private List<String> leavePic;

    @ApiModelProperty(value = "单据照片")
    private List<String> documentPic;

    @ApiModelProperty(value = "收货/发货时间")
    private LocalDateTime receiveTime;

    // 车辆信息
    @ApiModelProperty(value = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String truckNo;

    // SDK
    @NotBlank(message = "基石称重数据id不能为空")
    @ApiModelProperty("基石终端称重数据id一")
    private String recordId1;

    @NotBlank(message = "基石称重数据id不能为空")
    @ApiModelProperty("基石终端称重数据id二")
    private String recordId2;

    // 其他
    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "分配方式 1 按随车面单量 2 按理重")
    @NotBlank(message = "分配方式不能为空")
    private Integer distribution;

    @ApiModelProperty("收料确认方式 1 扫码组装 2 司机自助确认 3 OCR结果落库")
    private Integer confirmType;

    // 发料
    @ApiModelProperty(value = "领用单位")
    private String receiveUnit;

    @ApiModelProperty(value = "收料明细列表")
    private List<SendReceiveMultiDetailForm> list;
}
