package cn.pinming.microservice.material.management.resposity.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购合同物料明细
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Data
public class PurchaseContractDetail {
    @ApiModelProperty(value = "明细ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "合同ID")
    @TableField("contract_id")
    private String contractId;

    @ApiModelProperty(value = "分类名称")
    private Integer categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "材料ID")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "父节点材料id(用于合同组合材料)")
    @TableField("pid")
    private Integer pid;

    @ApiModelProperty(value = "子件数量(用于合同组合材料)")
    private BigDecimal childrenAmount;

    @ApiModelProperty(value = "材料编码")
    @TableField("material_code")
    private String materialCode;

    @ApiModelProperty(value = "材料名称")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty(value = "材料规格")
    private String materialSpec;

    @ApiModelProperty(value = "采购结算单位")
    private String unit;

    @ApiModelProperty(value = "过磅换算公式单位")
    @TableField("transform_unit")
    private String transformUnit;

    @ApiModelProperty(value = "过磅换算公式数量")
    @TableField("conversion_rate")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "约定偏差阈值上限")
    @TableField("deviation_ceiling")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    @TableField("deviation_floor")
    private BigDecimal deviationFloor;

    @ApiModelProperty(value = "品牌要求，多个")
    private String brand;

    @ApiModelProperty(value = "合同基础价")
    private BigDecimal contractBasePrice;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
