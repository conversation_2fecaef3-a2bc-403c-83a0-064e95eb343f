package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version 2021/9/3 3:45 下午
 */
@Data
public class MaterialUnitVO {

    @ApiModelProperty("计量单位-中文名称，如：米、千米、厘米")
    private String sourceUnitName;

    @ApiModelProperty("计量单位-中文符号，如：米、千米、厘米、米²、千米²、厘米²")
    private String sourceUnitChSymbols;

    @ApiModelProperty("计量单位-英文符号，如：m、km、cm")
    private String sourceUnitEnSymbols;

    @ApiModelProperty("计量单位-中文名称，如：米、千米、厘米")
    private String targetUnitName;

    @ApiModelProperty("计量单位-中文符号，如：米、千米、厘米、米²、千米²、厘米²")
    private String targetUnitChSymbols;

    @ApiModelProperty("计量单位-英文符号，如：m、km、cm")
    private String targetUnitEnSymbols;

    @ApiModelProperty("换算率，即1源计量单位，等于多少目标计量单位")
    private BigDecimal conversionRate;

}
