package cn.pinming.microservice.material.management.resposity.vo;

import cn.pinming.microservice.base.common.wrapper.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseSimpleVO extends SimpleSupplierDTO {

    @ApiModelProperty(value = "上次的材料id")
    private Integer materialId;

    @ApiModelProperty(value = "采购单ID")
    private String id;

    @ApiModelProperty(value = "采购单编号")
    private String orderNo;

    @ApiModelProperty(value = "采购单外部系统单号")
    private String orderExtNo;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "合同名称")
    private String name;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "下单人id")
    private String createId;

    @ApiModelProperty(value = "下单人")
    private String createName;

    @ApiModelProperty(value = "计划收货人")
    private String receiver;

    @ApiModelProperty(value = "收货项目id")
    private Integer receiverProject;

    @ApiModelProperty(value = "收货项目")
    private String receiverProjectTitle;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品列表")
    private List<GoodsSimpleVO> list;

}
