package cn.pinming.microservice.material.management.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.infrastructure.util.ExcelUtils;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.resposity.vo.MaterialReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankDiffVO;
import cn.pinming.microservice.material.management.resposity.vo.SupplierRankVO;
import cn.pinming.microservice.material.management.service.ISupplierAnalysisService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 供应商分析
 *
 * <AUTHOR>
 * @version 2021/9/1 4:31 下午
 */
@Api(tags = "供应商分析", value = "lh")
@RestController
@RequestMapping("/api/supplier/analysis")
@AllArgsConstructor
public class SupplierAnalysisController {

    private final ISupplierAnalysisService supplierAnalysisService;

    @ApiOperation(value = "汇总", response = SupplierAnalysisVO.class)
    @PostMapping("/summary")
    public ResponseEntity<Response> summary(@RequestBody SupplierAnalysisQuery query) {
        SupplierAnalysisVO result = supplierAnalysisService.getSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "收料情况列表", response = SupplierAnalysisDetailVO.class)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody SupplierAnalysisQuery query) {
        IPage<?> page = supplierAnalysisService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "收料情况列表 - 详情", response = MaterialReceiveVO.class)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody SupplierAnalysisQuery query) {
        IPage<?> page = supplierAnalysisService.pageListOrderByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "收料情况列表 - 导出", response = MaterialReceiveVO.class)
    @PostMapping("/detail/export")
    public void detailExport(HttpServletResponse response, @RequestBody SupplierAnalysisQuery query) {
        query.setPages(0);
        query.setSize(Integer.MAX_VALUE);
        IPage<?> page = supplierAnalysisService.pageListOrderByQuery(query);
        LocalDate now = LocalDate.now();
        String date = LocalDateTimeUtil.format(now, "yyyyMMdd");
        // Supplier supplier = supplierService.getById(query.getSupplierId());
        ExcelUtils.export(response, page.getRecords(), MaterialReceiveVO.class, query.getSupplier() + "收料记录导出" + date);
    }

    @ApiOperation(value = "超负差次数排行", response = SupplierRankVO.class)
    @PostMapping("/negative/frequency/rank")
    public ResponseEntity<Response> negativeFrequencyRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeFrequencyRankList(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差次数占比", response = SupplierRankVO.class)
    @PostMapping("/negative/frequency/proportion")
    public ResponseEntity<Response> negativeFrequencyProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeFrequencyProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差总量排行", response = SupplierRankVO.class)
    @PostMapping("/negative/total/rank")
    public ResponseEntity<Response> negativeTotalRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeTotalRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差总量占比排行", response = SupplierRankVO.class)
    @PostMapping("/negative/total/proportion")
    public ResponseEntity<Response> negativeTotalProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeTotalProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重次数排行(车次数)", response = SupplierRankVO.class)
    @PostMapping("/deduct/rank")
    public ResponseEntity<Response> deductRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重次数占比排行", response = SupplierRankVO.class)
    @PostMapping("/deduct/proportion")
    public ResponseEntity<Response> deductProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重总量排行", response = SupplierRankVO.class)
    @PostMapping("/deduct/total/rank")
    public ResponseEntity<Response> deductTotalRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductTotalRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重总量占比排行", response = SupplierRankVO.class)
    @PostMapping("/deduct/total/proportion")
    public ResponseEntity<Response> deductTotalProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductTotalProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }


    @ApiOperation(value = "本期/累计 超负差top", response = SupplierRankDiffVO.class)
    @PostMapping("/negative/frequency/top")
    public ResponseEntity<Response> negativeFrequencyTop(@RequestBody SupplierRankQuery query) {
        List<SupplierRankDiffVO> list = supplierAnalysisService.negativeFrequencyTop(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "本期/累计 超负差比top", response = SupplierRankDiffVO.class)
    @PostMapping("/negative/frequency/rate/top")
    public ResponseEntity<Response> negativeFrequencyRateTop(@RequestBody SupplierRankQuery query) {
        List<SupplierRankDiffVO> list = supplierAnalysisService.negativeFrequencyRateTop(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }
}
