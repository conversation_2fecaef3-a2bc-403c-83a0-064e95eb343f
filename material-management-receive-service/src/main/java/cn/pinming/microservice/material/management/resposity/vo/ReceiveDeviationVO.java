package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/9 9:47 下午
 */
@Data
public class ReceiveDeviationVO {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("超负差")
    private int negative;

    @ApiModelProperty("超正差")
    private int positive;

    @ApiModelProperty("正常")
    private int normal;

    @ApiModelProperty("无法确定偏差状态")
    private int unidentified;
}
