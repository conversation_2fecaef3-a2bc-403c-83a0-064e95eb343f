package cn.pinming.microservice.material.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.FileDTO;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.resposity.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveDetailMapper;
import cn.pinming.microservice.material.management.resposity.mapper.MobileReceiveTotalMapper;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveDetailHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO;
import cn.pinming.microservice.material.management.service.IMobileReceiveTotalService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 移动收料总计表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
@Service
public class MobileReceiveTotalServiceImpl extends ServiceImpl<MobileReceiveTotalMapper, MobileReceiveTotal> implements IMobileReceiveTotalService {

    @Resource
    private MaterialServiceProxy      materialServiceProxy;
    @Resource
    private MobileReceiveTotalMapper  totalMapper;
    @Resource
    private MobileReceiveDetailMapper detailMapper;
    @Resource
    private FileServiceProxy          fileServiceProxy;

    /**
     * 各材料收货记录统计录入
     *
     * @param form,receiveId
     */
    @Override
    public void add(MobileReceiveMaterialForm form, SimpleTransformDTO simpleTransformDTO) {
        MobileReceiveTotal total = new MobileReceiveTotal();
        List<Integer> integerList = new ArrayList<>();
        Integer categoryId = null;
        Byte type = simpleTransformDTO.getType();
        String receiveId = simpleTransformDTO.getReceiveId();
        String totalUuid = simpleTransformDTO.getTotalId();
        integerList.add(form.getMaterialId());
        List<MaterialDto> list = materialServiceProxy.listMaterialByIds(integerList);
        if (CollUtil.isNotEmpty(list)) {
            categoryId = list.get(0).getMaterialCategoryId();
        }

        total.setReceiveId(receiveId);
        total.setId(totalUuid);
        if (type != null && type == 1) {
            // 简易版
            total.setRemark(form.getRemark());
            total.setCategoryId(categoryId);
            total.setMaterialId(form.getMaterialId());
            total.setSendNumber(1);
            total.setSendContent(simpleTransformDTO.getSendSettlementTotal());
            total.setSendSettlementTotal(simpleTransformDTO.getSendSettlementTotal());
            total.setActualNumber(1);
            total.setActualContent(simpleTransformDTO.getActualSettlementTotal());
            total.setActualSettlementTotal(simpleTransformDTO.getActualSettlementTotal());
            total.setSettlementUnit(simpleTransformDTO.getSettlementUnit());
            total.setUnit(simpleTransformDTO.getSettlementUnit());
            total.setDeviationRate(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(total.getActualSettlementTotal(), total.getSendSettlementTotal()), total.getSendSettlementTotal()), 100));
            // 偏差状态
            if (simpleTransformDTO.getReceiveType() == 1 || simpleTransformDTO.getReceiveType() == 2) {
                byte deviationStatus = DeviationStatusEnum.NORMAL.value();
                if (NumberUtil.isLess(total.getDeviationRate(), simpleTransformDTO.getDeviationFloor())) {
                    deviationStatus = DeviationStatusEnum.NEGATIVEDIFFERENCE.value();
                } else if (NumberUtil.isGreater(total.getDeviationRate(), simpleTransformDTO.getDeviationCeiling())) {
                    deviationStatus = DeviationStatusEnum.POSITIVEDIFFERENCE.value();
                }
                total.setDeviationStatus(deviationStatus);
            }
        } else {
            BeanUtils.copyProperties(form, total);
            total.setReceiveId(receiveId);
            total.setId(totalUuid);
            total.setCategoryId(categoryId);
            if (form.getKind() != null) {
                if (form.getKind() == 1) {
                    total.setActualSettlementTotal(form.getActualWeightTotal());
                } else if (form.getKind() == 2) {
                    total.setActualSettlementTotal(form.getTheoreticalWeightTotal());
                }
            }
        }

        this.save(total);
    }

    @Override
    public MobileReceiveHistoryVO history(String totalId) {
        MobileReceiveHistoryVO vo = totalMapper.selectHistory(totalId);
        if (ObjectUtil.isNotEmpty(vo)) {
            List<MobileReceiveDetailHistoryVO> list = detailMapper.selectHistory(totalId);
            list.forEach(e -> {
                if (StrUtil.isNotBlank(e.getPointsPic())) {
                    e.setPointsPic(fileServiceProxy.getFileList(e.getPointsPic()).stream().map(FileDTO::getUrl).collect(Collectors.joining(",")));
                }
            });
            vo.setList(list);
        }
        return vo;
    }

    @Override
    public List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query) {
        return totalMapper.countMobileReceiveNum(query);
    }

    @Override
    public List<ReceiveDeviationVO> countDeviation(SupplierAnalysisQuery query, List<Byte> receiveMode) {
        return totalMapper.countDeviation(query, receiveMode);
    }

    @Override
    public List<ReceiveDeviationSummaryVO> countDeviationStatus(SupplierAnalysisQuery query, List<Byte> receiveMode) {
        return totalMapper.countDeviationStatus(query, receiveMode);
    }
}
