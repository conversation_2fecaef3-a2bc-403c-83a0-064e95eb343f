package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version 2021/9/9 9:47 下午
 */
@Data
public class ReceiveDeviationSummaryVO {

    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("类型名称")
    private Byte deviationStatus;

    @ApiModelProperty("次数")
    private Integer count;

    @ApiModelProperty("比率")
    private BigDecimal rate;

}
