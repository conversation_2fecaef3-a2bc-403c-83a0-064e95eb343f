package cn.pinming.microservice.material.management.infrastructure.enums;

/**
 * <AUTHOR>
 * @Date 2022/1/24 13:11
 */
public enum WarningSourceEnum {

    WEIGHT_BRIDGE_RECEIVE((byte) 1, "地磅收料"),
    MOBilE_RECEIVE((byte) 2, "移动收料"),
//    FIELD_RECEIVE((byte) 3, "场内领料")
    WEIGHT_BRIDGE_DELIVERY((byte)3,"发料")


    ;



    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    WarningSourceEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }
    public String description() {
        return description;
    }
}
