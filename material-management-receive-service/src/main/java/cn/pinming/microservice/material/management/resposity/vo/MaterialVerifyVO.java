package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:58
 */
@Data
public class MaterialVerifyVO {
    @ApiModelProperty(value = "对账id")
    private String id;

    @ApiModelProperty(value = "对账编号")
    private String verifyNo;

    @ApiModelProperty(value = "报账项目")
    private String verifyProject;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "对账供应商")
    private String verifySupplier;

    @ApiModelProperty(value = "对账人")
    private String verifyPerson;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "收料方式 1 地磅收料 2 移动收料")
    private Byte verifyType;

    @ApiModelProperty(value = "对账状态 0 已归档 1 对账中")
    private Byte status;

    @ApiModelProperty(value = "归档时间")
    private LocalDateTime fileTime;

    @ApiModelProperty(value = "是否是创建者")
    private Boolean isCreate;

    @ApiModelProperty(value = "手工添加收料记录")
    private Boolean addReceive;

    @ApiModelProperty(value = "使用扫码枪添加收料记录")
    private Boolean scanAddReceive;

    @ApiModelProperty(value = "归档")
    private Boolean verifyFile;

    @ApiModelProperty(value = "统计列表")
    private List<MaterialVerifyStatisticsVO> statisticsVOList;

    @ApiModelProperty(value = "地磅收料-收料列表")
    private List<MaterialWeighBaseVO> receiveDataVOList;

    @ApiModelProperty(value = "移动收料-收料列表")
    private List<MobileReceiveVerifyVO> mobileDataVOList;
}
