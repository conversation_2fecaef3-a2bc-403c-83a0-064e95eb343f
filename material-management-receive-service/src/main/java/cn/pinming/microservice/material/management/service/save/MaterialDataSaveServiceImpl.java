package cn.pinming.microservice.material.management.service.save;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.microservice.base.common.proxy.FileServiceProxy;
import cn.pinming.microservice.base.common.proxy.MaterialServiceProxy;
import cn.pinming.microservice.base.common.proxy.ProjectServiceProxy;
import cn.pinming.microservice.base.common.proxy.SupplierServiceProxy;
import cn.pinming.microservice.base.common.proxy.dto.ProjectDTO;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.base.management.dto.SdkConfigDTO;
import cn.pinming.microservice.base.management.service.ISdkConfService;
import cn.pinming.microservice.contract.management.dto.SimpleContractDTO;
import cn.pinming.microservice.contract.management.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.management.infrastructure.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.OcrMappingEnum;
import cn.pinming.microservice.material.management.infrastructure.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.infrastructure.exception.BOException;
import cn.pinming.microservice.material.management.infrastructure.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.infrastructure.util.*;
import cn.pinming.microservice.material.management.resposity.dto.MaterialDataExpandDTO;
import cn.pinming.microservice.material.management.resposity.dto.MaterialMatchingScore;
import cn.pinming.microservice.material.management.resposity.dto.PicUrlDTO;
import cn.pinming.microservice.material.management.resposity.dto.ReceiptRecyclePushDTO;
import cn.pinming.microservice.material.management.resposity.entity.MaterialData;
import cn.pinming.microservice.material.management.resposity.entity.MaterialDataExpand;
import cn.pinming.microservice.material.management.resposity.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.resposity.entity.MaterialWarning;
import cn.pinming.microservice.material.management.resposity.form.PushRangeForm;
import cn.pinming.microservice.material.management.resposity.form.SDKStandardMaterialForm;
import cn.pinming.microservice.material.management.resposity.form.WordsMatchForm;
import cn.pinming.microservice.material.management.resposity.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.service.IMaterialDataExpandService;
import cn.pinming.microservice.material.management.service.IMaterialDataService;
import cn.pinming.microservice.material.management.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.service.IMaterialWarningService;
import cn.pinming.microservice.material.resource.dto.MaterialDataResourceDTO;
import cn.pinming.microservice.material.resource.service.IWeighDataSyncService;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.supplier.management.dto.SupplierDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MaterialDataSaveServiceImpl implements IMaterialDataSaveService {
    @Resource
    private NoUtil noUtil;
    @Resource
    private UserUtil userUtil;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private PicUtil picUtil;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private WarningUtil warningUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @DubboReference
    private IMaterialContractService materialContractService;
    @Resource
    private SupplierServiceProxy supplierServiceProxy;
    @Value("${imatchocr.url}")
    private String imatchocrUlr;
    @DubboReference
    private ISdkConfService sdkConfService;
    @Resource
    private WeighDuplicationUtil weighDuplicationUtil;
    @Resource
    private FileServiceProxy fileServiceV2Proxy;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @DubboReference
    private IWeighDataSyncService weighDataSyncService;
    @Resource
    private IMaterialDataExpandService materialDataExpandService;
    @Resource
    private IMaterialWarningService materialWarningService;

    @Override
    public String SDKSendReceiveSave(SDKStandardMaterialForm form) {
        AuthUser user = userUtil.getUser();

        MaterialSendReceive sendReceive = new MaterialSendReceive();
        BeanUtils.copyProperties(form, sendReceive);
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            sendReceive.setReceiveNo(noUtil.getReceiveNo(user.getCurrentProjectId()));
        } else {
            sendReceive.setReceiveNo(noUtil.getSendNo(user.getCurrentProjectId()));
        }

        materialSendReceiveService.save(sendReceive);
        return sendReceive.getId();
    }

    @Override
    public String SDKDataSave(SDKStandardMaterialForm form, String sendReceiveId) {
        MaterialData data = new MaterialData();
        MaterialDataExpand materialDataExpand = new MaterialDataExpand();
        SDKCalculate(form);
        BeanUtils.copyProperties(form, data);
        data.setReceiveId(sendReceiveId);
        data.setSupplierName(form.getReceiveProject());
        data.setWeightUnit(form.getUnit());
        data.setIsDevice((byte) 2);
        data.setWeighId(UUIDUtil.randomUUIDWithoutConnector());
        data.setReceiveTime(ObjectUtil.isNull(form.getReceiveTime()) ? (form.getType() == 1 ? form.getEnterTime() : form.getLeaveTime()) : form.getReceiveTime());
        // 图片
        if (CollUtil.isNotEmpty(form.getDocumentPic())) {
            // 单据照片
            String documentPic = form.getDocumentPic().stream().map(e -> {
                try {
                    return picUtil.getUrlByBase64(e);
                } catch (FileNotFoundException ex) {
                    throw new BOException(BOExceptionEnum.PIC_SIZE_ERROR);
                }
            }).collect(Collectors.joining(","));
            data.setDocumentPic(documentPic);
        }
        if (CollUtil.isNotEmpty(form.getDocumentPicUuid())) {
            String documentPic = String.join(",", form.getDocumentPicUuid());
            fileServiceV2Proxy.confirmFiles(form.getDocumentPicUuid());
            data.setDocumentPic(documentPic);
        }
        if (CollUtil.isNotEmpty(form.getEnterPic())) {
            // 进场照片
            String enterPic = form.getEnterPic().stream().map(e -> {
                try {
                    return picUtil.downloadAndUploadFile(e, UUIDUtil.randomUUID(), null);
                } catch (IOException ex) {
                    throw new BOException(BOExceptionEnum.PIC_SIZE_ERROR);
                }
            }).collect(Collectors.joining(","));
            data.setEnterPic(enterPic);
        }
        if (CollUtil.isNotEmpty(form.getLeavePic())) {
            // 出场照片
            String leavePic = form.getLeavePic().stream().map(e -> {
                try {
                    return picUtil.downloadAndUploadFile(e, UUIDUtil.randomUUID(), null);
                } catch (IOException ex) {
                    throw new BOException(BOExceptionEnum.PIC_SIZE_ERROR);
                }
            }).collect(Collectors.joining(","));
            data.setLeavePic(leavePic);
        }
        // 材料
        if (ObjectUtil.isNotNull(form.getMaterialId())) {
            MaterialDto materialDto = materialServiceProxy.materialById(form.getMaterialId());
            if (ObjectUtil.isNotNull(materialDto)) {
                data.setCategoryId(materialDto.getMaterialCategoryId());
                data.setCategoryName(materialDto.getMaterialCategoryName());
                data.setMaterialName(materialDto.getMaterialName() + '/' + materialDto.getMaterialSpec());
            }
        }

        MaterialSendReceive one = materialSendReceiveService.lambdaQuery()
                .eq(MaterialSendReceive::getId, sendReceiveId)
                .one();
        materialDataService.save(data);
        BeanUtils.copyProperties(form, materialDataExpand);
        materialDataExpand.setDataId(data.getId());
        materialDataExpandService.save(materialDataExpand);
        warningUtil.createWarning(data,one.getReceiveNo(), form.getType());
        return data.getId();
    }

    @Override
    public void OCRSync(ReceiptRecyclePushDTO dto) {
        String primaryCode = dto.getPrimaryCode();
        ProjectDTO projectDTO = projectServiceProxy.getProjectById(Integer.valueOf(primaryCode));
        if (ObjectUtil.isNull(projectDTO)) {
            throw new BOException(BOExceptionEnum.PROJECT_ERROR);
        }
        Integer projectId = projectDTO.getProjectId();
        Integer companyId = projectDTO.getCompanyId();

        List<ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO> weighList = dto.getWeighList();
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO record1 = weighList.get(0);
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO record2 = weighList.get(1);
        weighDuplicationUtil.judge(Arrays.asList(record1.getRecordId(),record2.getRecordId()));

        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO gross = null;
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO tare = null;
        Byte type = 1;
        if (record1.getWeight().compareTo(record2.getWeight()) > 0) {
            gross = record1;
            tare = record2;
        } else {
            gross = record2;
            tare = record1;
        }
        if (gross.getWeighTime().isAfter(tare.getWeighTime())) {
            type = 2;
        }

        MaterialSendReceive materialSendReceive = new MaterialSendReceive();
        materialSendReceive.setType(type);
        if (materialSendReceive.getType() == 1) {
            materialSendReceive.setReceiveNo(noUtil.getOCRReceiveNo(projectId));
        } else {
            materialSendReceive.setReceiveNo(noUtil.getOCRSendNo(projectId));
        }
        materialSendReceive.setId(UUIDUtil.randomUUIDWithoutConnector());
        materialSendReceive.setTruckNo(dto.getTruckNo());
        materialSendReceive.setProjectId(projectId);
        materialSendReceive.setCompanyId(companyId);
        // 冗余ReceiptRecycleDO的id
        materialSendReceive.setConfirmExtNo(String.valueOf(dto.getId()));

        MaterialData materialData = new MaterialData();
        // OCR识别结果
        String ocrMaterialName = null;
        String ocrMaterialSpec = null;
        String ocrSupplierName = null;
        if (CollUtil.isNotEmpty(dto.getModuleList())) {
            for (ReceiptRecyclePushDTO.ReceiptRecycleModuleDTO e : dto.getModuleList()) {
                if (e.getKeyName().equals(OcrMappingEnum.POSITION.getMapping())) {
                    materialData.setPosition(e.getKeyValue());
                }
                if (e.getKeyName().equals(OcrMappingEnum.WEIGHT_SEND.getMapping())) {
                    materialData.setWeightSend(new BigDecimal(e.getKeyValue()));
                }
                if (e.getKeyName().equals(OcrMappingEnum.GOODS.getMapping())) {
                    ocrMaterialName = e.getKeyValue();
                }
                if (e.getKeyName().equals(OcrMappingEnum.SPEC.getMapping())) {
                    ocrMaterialSpec = e.getKeyValue();
                }
                if (e.getKeyName().equals(OcrMappingEnum.SUPPLIER_NAME.getMapping())) {
                    ocrSupplierName = e.getKeyValue();
                    materialData.setSupplierName(ocrSupplierName);
                }
            }
        }
        SimpleContractDetailDTO detailDTO = null;
        if (StrUtil.isNotBlank(dto.getContractId())) {
            // 有合同id
            SimpleContractDTO purchaseContract = materialContractService.querySimpleContract(dto.getContractId());
            if (ObjectUtil.isNull(purchaseContract) || ObjectUtil.isNull(purchaseContract.getSupplierId())) {
                throw new BOException(BOExceptionEnum.DOCUMENT_ERROR);
            }
            materialData.setSupplierId(purchaseContract.getSupplierId());
            SupplierDTO supplierDTO = supplierServiceProxy.findById(purchaseContract.getSupplierId());
            if (ObjectUtil.isNotNull(supplierDTO)) {
                materialData.setSupplierName(supplierDTO.getName());
            }
            List<SimpleContractDetailDTO> purchaseContractDetailList = materialContractService.querySimpleContractDetailsByContractId(dto.getContractId());
            List<String> contractMaterial = new ArrayList<>();
            if (CollUtil.isNotEmpty(purchaseContractDetailList)) {
                contractMaterial = purchaseContractDetailList.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).collect(Collectors.toList());
            }

            // 语义相似度过滤
            try {
                List<MaterialMatchingScore> materialMatchingScores = materialMatching(ocrMaterialName + ocrMaterialSpec, contractMaterial);
                log.info(materialMatchingScores.toString());
                if (CollUtil.isNotEmpty(materialMatchingScores)) {
                    List<MaterialMatchingScore> scores = materialMatchingScores.stream().filter(e -> new BigDecimal(e.getScore()).compareTo(new BigDecimal("0.95")) > 0).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(scores) && scores.size() == 1) {
                        MaterialMatchingScore materialMatchingScore = scores.get(0);
                        detailDTO = purchaseContractDetailList.stream().filter(e -> StrUtil.format("{}{}", e.getMaterialName(), e.getMaterialSpec()).equals(materialMatchingScore.getMaterialName())).collect(Collectors.toList()).get(0);
                        materialData.setMaterialId(detailDTO.getMaterialId());
                        materialData.setCategoryId(detailDTO.getCategoryId());
                        materialData.setContractDetailId(detailDTO.getContractDetailId());
                        materialData.setWeightUnit(detailDTO.getUnit());
                        materialData.setRatio(detailDTO.getConversionRate());
                    }
                    if (CollUtil.isEmpty(scores) || (CollUtil.isNotEmpty(scores) && scores.size() != 1)) {
                        materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
                    }
                }else {
                    materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
                }
            } catch (Exception ignored) {
                log.info("语义匹配失败");
            }
        } else {
            // 无other_id
            materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
//            ProjectConfig one = projectConfigService.lambdaQuery()
//                    .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
//                    .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
//                    .eq(ProjectConfig::getType, ProjectConfigEnum.FOUR.value())
//                    .eq(ProjectConfig::getOcrModuleId, ocrConvertDTO.getId())
//                    .isNotNull(ProjectConfig::getOcrMaterialType)
//                    .one();
//            if (ObjectUtil.isNotNull(one)) {
//                // 项目设置品种列表
//                List<String> split = StrUtil.split(one.getOcrMaterialType(), ",");
//                List<String> materialNameConfigs = new ArrayList<>();
//                split.forEach(e -> {
//                    String s = StrUtil.split(e, "/").get(2);
//                    materialNameConfigs.add(s);
//                });
//                // 根据品种名称找规格列表
//                List<MaterialDto> materialDtos = materialService.listMaterialByMaterials(user.getCurrentCompanyId(), materialNameConfigs);
//                if (CollUtil.isNotEmpty(materialDtos)) {
//                    List<String> collect = materialDtos.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).distinct().collect(Collectors.toList());
//                    try {
//                        List<MaterialMatchingScore> materialMatchingScores = materialMatching(ocrMaterialName + ocrMaterialSpec, collect);
//                        if (CollUtil.isNotEmpty(materialMatchingScores)) {
//                            log.info(materialMatchingScores.toString());
//                            List<String> material = materialMatchingScores.stream().map(MaterialMatchingScore::getMaterialName).distinct().collect(Collectors.toList());
//                            List<SingelOcrMaterialDTO> result = materialDtos.stream().filter(e -> material.contains(e.getMaterialName() + e.getMaterialSpec())).map(e -> {
//                                SingelOcrMaterialDTO singelOcrMaterialDTO = new SingelOcrMaterialDTO();
//                                BeanUtils.copyProperties(e, singelOcrMaterialDTO);
//                                return singelOcrMaterialDTO;
//                            }).collect(Collectors.toList());
//                            form.setMaterialChooseList(result);
//                        }
//                    } catch (Exception ignored) {
//                    }
//                }
//            }
        }
        materialData.setReceiveId(materialSendReceive.getId());
        materialData.setWeightGross(gross.getWeight());
        materialData.setWeightTare(tare.getWeight());
        materialData.setWeightDeduct(BigDecimal.ZERO);
        materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
        materialData.setMoistureContent(BigDecimal.ZERO);
        materialData.setWeightActual(materialData.getWeightNet());
        // 实际数量
        if (materialData.getRatio() != null) {
            materialData.setActualCount(NumberUtil.div(materialData.getWeightActual(), NumberUtil.div(materialData.getRatio(),1000)));
            // 偏差率 + 偏差状态
            boolean flag2 = materialData.getWeightSend() != null && NumberUtil.isGreater(materialData.getWeightSend(), BigDecimal.ZERO);
            if (flag2) {
                BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(), materialData.getWeightSend()), materialData.getWeightSend(), 4), 100);
                materialData.setDeviationRate(deviation);
                if (ObjectUtil.isNotNull(detailDTO)) {
                    BigDecimal deviationCeiling = detailDTO.getDeviationCeiling();
                    BigDecimal deviationFloor = detailDTO.getDeviationFloor();

                    if (ObjectUtil.isNotNull(deviationCeiling) && ObjectUtil.isNotNull(deviationFloor) && ObjectUtil.isNotNull(detailDTO.getDeviationCalculate()) && detailDTO.getDeviationCalculate() == 1) {
                        int flag;
                        int flag1;
                        flag = deviation.compareTo(deviationFloor);
                        flag1 = deviation.compareTo(deviationCeiling);
                        if (flag < 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                        }
                        if (flag1 > 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                        }
                        if (flag >= 0 && flag1 <= 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                        }
                    }
                }
            }
            // 实收数量
            if (materialData.getActualCount() != null && materialData.getActualReceive() == null) {
                materialData.setActualReceive(materialData.getActualCount());
                boolean flag = materialData.getDeviationStatus() != null && (materialData.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || materialData.getDeviationStatus() == DeviationStatusEnum.NORMAL.value());
                if (flag) {
                    materialData.setActualReceive(materialData.getWeightSend());
                }
            }
        }
        materialData.setRecordId1(record1.getRecordId());
        materialData.setRecordId2(record2.getRecordId());
        materialData.setIsDevice((byte) 2);
        materialData.setWeighId(UUIDUtil.randomUUIDWithoutConnector());
        materialData.setConfirmType(3);
        List<PicUrlDTO> recyclePicUrls = dto.getRecyclePicUrls();
        if (CollUtil.isNotEmpty(recyclePicUrls)) {
            String collect = recyclePicUrls.stream().map(e -> {
                try {
                    return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                } catch (IOException ex) {
                    log.info("下载单据照片异常");
                    return null;
                }
            }).collect(Collectors.joining(","));
            materialData.setDocumentPic(collect);
        }
        materialData.setEnterTime(gross.getWeighTime().
                isBefore(tare.getWeighTime()) ? gross.getWeighTime() : tare.getWeighTime());
        materialData.setLeaveTime(gross.getWeighTime().
                isAfter(tare.getWeighTime()) ? gross.getWeighTime() : tare.getWeighTime());
        if (gross.getWeighTime().
                isBefore(tare.getWeighTime())) {
            if (CollUtil.isNotEmpty(gross.getFileIdUrls())) {
                String enterPic = gross.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载进场照片异常");
                        return null;
                    }
                }).collect(Collectors.joining(","));
                materialData.setEnterPic(enterPic);
            }
            if (CollUtil.isNotEmpty(tare.getFileIdUrls())) {
                String leavePic = tare.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载出场照片异常");
                        return null;
                    }
                }).collect(Collectors.joining(","));
                materialData.setLeavePic(leavePic);
            }
        } else {
            if (CollUtil.isNotEmpty(tare.getFileIdUrls())) {
                String enterPic = tare.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载进场照片异常");
                        return null;
                    }
                }).collect(Collectors.joining(","));
                materialData.setEnterPic(enterPic);
            }
            if (CollUtil.isNotEmpty(gross.getFileIdUrls())) {
                String leavePic = gross.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载出场照片异常");
                        return null;
                    }
                }).collect(Collectors.joining(","));
                materialData.setLeavePic(leavePic);
            }
        }
        materialData.setReceiveTime(dto.getRecycleTime());
        materialData.setCompanyId(companyId);
        materialData.setProjectId(projectId);

        materialSendReceiveService.save(materialSendReceive);
        materialDataService.save(materialData);

        // 回调确认
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        materialClient.receiptRecyclePushConfirm(dto.getId());
    }

    @Override
    public void innerSync(List<PushRangeForm> range) {
        if (CollUtil.isEmpty(range)) {return;}
        List<String> companyIdList = range.stream().filter(e -> StrUtil.isBlank(e.getProjectIds())).map(PushRangeForm::getCompanyId).collect(Collectors.toList());
        List<String> projectIdList = range.stream().filter(e -> StrUtil.isNotBlank(e.getProjectIds())).map(e -> StrUtil.split(e.getProjectIds(), ",")).flatMap(Collection::stream).collect(Collectors.toList());

        List<MaterialDataResourceDTO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(companyIdList)) {
            List<MaterialDataResourceDTO> company = materialDataMapper.innerSync(companyIdList,null);
            list.addAll(company);
        }
        if (CollUtil.isNotEmpty(projectIdList)) {
            List<MaterialDataResourceDTO> project = materialDataMapper.innerSync(null,projectIdList);
            list.addAll(project);
        }

        if (CollUtil.isNotEmpty(list)) {
            List<List<MaterialDataResourceDTO>> batch = splitList(list,200);
            batch.parallelStream().forEach(e -> {
                weighDataSyncService.syncWeighData(e);
            });
        }
    }

    @Override
    public void warning(List<String> ids) {
        List<MaterialDataExpandDTO> list = materialDataMapper.warning(ids);
        List<MaterialWarning> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(e -> {
                List<MaterialWarning> warnings = warningUtil.createWarnings(e, e.getReceiveNo(), e.getReceiveType());
                result.addAll(warnings);
            });
        }
        if (CollUtil.isNotEmpty(result)) {
            materialWarningService.saveBatch(result);
        }
    }

    private <T> List<List<T>> splitList(List<T> list, int batchSize) {
        return IntStream.range(0, (list.size() + batchSize - 1) / batchSize)  // 计算分割的批次数
                .mapToObj(i -> list.subList(i * batchSize, Math.min((i + 1) * batchSize, list.size())))
                .collect(Collectors.toList());
    }

    /**
     * 语义相似度匹配
     *
     * @param material,materialList
     * @return
     */
    private List<MaterialMatchingScore> materialMatching(String material, List<String> materialList) {
        WordsMatchForm form = new WordsMatchForm();
        form.setWord(material);
        form.setWords(materialList);
        try {
            String res = HttpUtil.post(imatchocrUlr, JSON.toJSONString(form));
            return JSONObject.parseArray(res, MaterialMatchingScore.class);
        } catch (Exception e) {
            throw new BOException(BOExceptionEnum.WORDS_MATCH_ERROR);
        }
    }

    private void SDKCalculate(SDKStandardMaterialForm form) {
        // 净重
        form.setWeightNet(NumberUtil.sub(form.getWeightGross(), form.getWeightTare()));
        // 实重
        form.setWeightActual(NumberUtil.sub(form.getWeightNet(), form.getWeightDeduct()));
        if (ObjectUtil.isNotNull(form.getMoistureContent())) {
            // 含水率
            form.setWeightActual(NumberUtil.mul(NumberUtil.sub(1, NumberUtil.div(form.getMoistureContent(), 100)), form.getWeightActual()));
        }
        // 实际数量
        form.setActualCount(NumberUtil.div(form.getWeightActual(), form.getRatio()));
        form.setDeviationStatus(null);
        BigDecimal actualReceive = form.getActualReceive();
        if (ObjectUtil.isNotNull(actualReceive)) {
            form.setActualReceive(actualReceive);
        }else {
            form.setActualReceive(form.getActualCount());
        }
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            // 偏差率
            if (ObjectUtil.isNotNull(form.getWeightSend())) {
                form.setDeviationRate(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(form.getActualCount(), form.getWeightSend()), form.getWeightSend(), 4), 100));
                if (ObjectUtil.isNotNull(form.getDeviationCeiling()) && ObjectUtil.isNotNull(form.getDeviationFloor()) && ObjectUtil.isNotNull(form.getDeviationCalculate()) && form.getDeviationCalculate() == 1) {
                    // 偏差状态
                    BigDecimal deviation = form.getDeviationRate();
                    int flag;
                    int flag1;
                    flag = deviation.compareTo(form.getDeviationFloor());
                    flag1 = deviation.compareTo(form.getDeviationCeiling());
                    if (flag < 0) {
                        form.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                    }
                    if (flag1 > 0) {
                        form.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                        if (ObjectUtil.isNull(actualReceive)) {
                            form.setActualReceive(form.getWeightSend());
                        }
                    }
                    if (flag >= 0 && flag1 <= 0) {
                        form.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                        if (ObjectUtil.isNull(actualReceive)) {
                            form.setActualReceive(form.getWeightSend());
                        }
                    }
                }
            }
        }
    }
}
