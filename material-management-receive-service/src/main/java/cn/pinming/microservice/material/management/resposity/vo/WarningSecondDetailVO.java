package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预警下钻详情vo
 *
 * <AUTHOR>
 * @since 2022/4/6 14:09
 */
@Data
public class WarningSecondDetailVO {

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "已处理数量")
    private Integer handleCount;

    @ApiModelProperty(value = "未处理数量")
    private Integer unHandleCount;

    @ApiModelProperty(value = "总量")
    private Integer count;
}
