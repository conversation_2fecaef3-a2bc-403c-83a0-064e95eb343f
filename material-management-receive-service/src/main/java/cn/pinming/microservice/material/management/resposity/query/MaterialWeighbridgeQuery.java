package cn.pinming.microservice.material.management.resposity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MaterialWeighbridgeQuery extends BaseQuery {

    @ApiModelProperty("企业id")
    @NotNull
    private Integer companyId;

    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty("项目状态")
    private Byte status;

    @ApiModelProperty("企业id")
    private List<Integer> projectIds;
}
