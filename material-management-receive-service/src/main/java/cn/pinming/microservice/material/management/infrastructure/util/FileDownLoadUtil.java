package cn.pinming.microservice.material.management.infrastructure.util;

import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.AliyunOssUploadComponent;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

@Component
@Slf4j
public class FileDownLoadUtil {
    @DubboReference
    private FileCenterService fileCenterService;
    public String downloadAndUploadFile(String downloadUrl, String memberId, String fileName) {
        // 下载图片字节流
        try {
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = connection.getInputStream();
                File file = File.createTempFile("image", ".jpg");
                FileOutputStream outputStream = new FileOutputStream(file);
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.close();
                inputStream.close();
                fileName = StrUtil.isBlank(fileName) ? memberId + ".jpg" : fileName + ".jpg";
                OssFile ossFile = new OssFile(file, URLConnection.guessContentTypeFromName(fileName), FileTypeEnums.IMAGE);
                return uploadFile(memberId, ossFile);
            }
        } catch (Exception e) {
            log.error("下载图片并上传失败");
        }
        return null;
    }

    public String uploadFile(String memberId, OssFile ossFile) {
        AliyunOssUploadComponent aliyunOssUploadComponent = new AliyunOssUploadComponent(fileCenterService,false);
        return aliyunOssUploadComponent.uploadFile(ossFile, new UploadConfig(memberId));
    }
}
