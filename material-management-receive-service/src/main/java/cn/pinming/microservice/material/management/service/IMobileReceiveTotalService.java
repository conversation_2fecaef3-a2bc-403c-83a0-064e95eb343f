package cn.pinming.microservice.material.management.service;


import cn.pinming.microservice.material.management.resposity.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.resposity.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.resposity.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.resposity.vo.ReceiveDeviationVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料总计表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
public interface IMobileReceiveTotalService extends IService<MobileReceiveTotal> {
    void add(MobileReceiveMaterialForm form, SimpleTransformDTO simpleTransformDTO);

    MobileReceiveHistoryVO history(String totalId);

    List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query);

    List<ReceiveDeviationVO> countDeviation(SupplierAnalysisQuery query, List<Byte> receiveMode);

    List<ReceiveDeviationSummaryVO> countDeviationStatus(SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);
}
