package cn.pinming.microservice.material.management.resposity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/4/7 11:06
 */
@Data
public class MaterialTruckVO {

    @ApiModelProperty("最后一次明细id")
    private String lastDataId;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("供应商id")
    private Integer supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("进场次数")
    private Long enterCount;

    @ApiModelProperty("载货总量")
    private BigDecimal total;

    @ApiModelProperty("最后一次毛重")
    private BigDecimal lastGross;

    @ApiModelProperty("最后一次皮重")
    private BigDecimal lastTare;

    @ApiModelProperty("最后一次毛重时间")
    private LocalDateTime lastGrossTime;

    @ApiModelProperty("最后一次皮重时间")
    private LocalDateTime lastTareTime;
}
