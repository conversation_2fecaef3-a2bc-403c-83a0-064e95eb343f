package cn.pinming.microservice.material.management.resposity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MobileGoodsCardVO {
        @ApiModelProperty(value = "receiveId")
        @ExcelIgnore
        private String id;

        @ApiModelProperty(value = "材料id")
        @ExcelIgnore
        private Integer materialId;

        @ApiModelProperty(value = "材料全称")
        @ExcelProperty(value = "材料名称")
        private String materialName;

        @ApiModelProperty(value = "材料结算合计")
        @ExcelProperty(value = "材料结算合计")
        private BigDecimal settlementTotal;

        @ApiModelProperty(value = "结算单位")
        @ExcelProperty(value = "结算单位")
        private String settlementUnit;

        @ApiModelProperty(value = "偏差状态")
        @ExcelIgnore
        private Byte deviationStatus;

        @ApiModelProperty(value = "偏差状态")
        @ExcelProperty(value = "偏差状态")
        private String deviationStatusStr;

        @ApiModelProperty(value = "收货单编号")
        @ExcelProperty(value = "收货单编号")
        private String receiveNo;

        @ApiModelProperty(value = "车牌号")
        @ExcelProperty(value = "车牌号")
        private String truckNo;

        @ApiModelProperty(value = "面单应收量")
        @ExcelProperty(value = "面单应收量")
        private BigDecimal sendSettlementTotal;

        @ApiModelProperty(value = "偏差量")
        @ExcelProperty(value = "偏差量")
        private BigDecimal deviationAmount;

        @ApiModelProperty(value = "偏差率")
        @ExcelProperty(value = "偏差率")
        private BigDecimal deviationRate;

        @ApiModelProperty(value = "进场时间")
        @ExcelProperty(value = "进场时间")
        private LocalDateTime truckTime;
}
