package cn.pinming.microservice.material.management.resposity.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/12
 * @description
 */
@Data
public class MaterialGroupUpdateForm {

    @ApiModelProperty(value = "原分组名称")
    @NotNull
    private String oldName;

    @ApiModelProperty(value = "新分组名称")
    @NotNull
    private String name;

}
