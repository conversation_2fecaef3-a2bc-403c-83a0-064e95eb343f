package cn.pinming.microservice.material.management.resposity.query;


import cn.hutool.core.lang.Pair;
import cn.pinming.microservice.material.management.infrastructure.enums.StatisticsReceiveWayEnum;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/13
 * @description
 */
public class StatisticsReceiveWayQuery {

    /**
     * 收料方式查询条件
     *
     * @param query
     * @return 地磅收料  移动收料
     */
    public static Pair<List<Byte>, List<Byte>> formatReceiveWay(String query) {
        List<Byte> poundbill = Lists.newArrayList();
        List<Byte> mobile = Lists.newArrayList();
        if (StringUtils.isNotBlank(query)) {
            String[] split = query.split(",");
            for (String s : split) {
                StatisticsReceiveWayEnum receiveWayEnum = StatisticsReceiveWayEnum.valueOf(s);
                switch (receiveWayEnum) {
                    case POUNDBILL_TEMPORARY:
                    case POUNDBILL_REPORT:
                    case POUNDBILL_UNBELOGN:
                        poundbill.add(receiveWayEnum.getCode());
                        break;
                    case MOBILE_CONTRACT:
                    case MOBILE_PURCHASE:
                    case MOBILE_NONE:
                        mobile.add(receiveWayEnum.getCode());
                        break;
                }
            }
        }
        return Pair.of(poundbill, mobile);
    }

}
