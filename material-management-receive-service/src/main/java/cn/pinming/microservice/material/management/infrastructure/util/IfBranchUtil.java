package cn.pinming.microservice.material.management.infrastructure.util;

/**
 * <AUTHOR>
 * @date 2022/3/16
 * @description 改写if分支
 */
public class IfBranchUtil {

    /**
     * 改写if无返回值
     * @param b
     * @return
     */
    public static IfBranchHandle isTrue(boolean b) {
        return (trueHandle) -> {
            if (b) {
                trueHandle.run();
            }
        };
    }

    /**
     * 改写if-else无返回值
     * @param b
     * @return
     */
    public static IfElseBranchHandle isTureOrFalse(boolean b) {
        return (trueHandle, falseHandle) -> {
            if (b) {
                trueHandle.run();
            } else {
                falseHandle.run();
            }
        };
    }

    /**
     * 改写if有返回值
     * @param b
     * @param t
     * @param <T>
     * @param <R>
     * @return
     */
    public static<T, R> IfReturnBranchHandle<T, R> isTrueReturn(boolean b, T t) {
        return (trueHandle) -> {
            if (b) {
                return trueHandle.apply(t);
            }
            return null;
        };
    }

    /**
     * 改写if-else有返回值
     * @param b
     * @param t
     * @param <T>
     * @param <R>
     * @return
     */
    public static<T, R> IfElseReturnBranchHandle<T, R> isTrueOrFalseReturn(boolean b, T t) {
        return (trueHandle, falseHandle) -> {
            if (b) {
                return trueHandle.apply(t);
            } else {
                return falseHandle.apply(t);
            }
        };
    }

}
