package cn.pinming.microservice.material.management.service;


import cn.pinming.microservice.material.management.resposity.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.resposity.entity.MobileReceiveDetail;
import cn.pinming.microservice.material.management.resposity.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.resposity.query.MobileCardQuery;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveCardVO;
import cn.pinming.microservice.material.management.resposity.vo.MobileReceiveVerifyVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 移动收料细节表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:23
 */
public interface IMobileReceiveDetailService extends IService<MobileReceiveDetail> {

    /**
     * 各材料收货记录细节录入
     *
     * @param list,receiveId
     */
    void add(List<MobileReceiveMaterialForm> list, String receiveId, SimpleTransformDTO simpleTransformDTO);

    /**
     * 移动收料单卡片列表页
     *
     * @param query
     * @return
     */
    IPage<MobileReceiveCardVO> selectPage(MobileCardQuery query);

    List<MobileReceiveVerifyVO> selectPageForVerify(MobileCardQuery query);
}
