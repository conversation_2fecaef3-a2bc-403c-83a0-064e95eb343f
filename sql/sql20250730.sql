CREATE TABLE `s_statistics_material_config`
(
    `id`            varchar(32) CHARACTER SET utf8 NOT NULL COMMENT 'id',
    `name`          varchar(128) CHARACTER SET utf8         DEFAULT NULL COMMENT '名称',
    `category_id`   text CHARACTER SET utf8 COMMENT '二级分类id列表,以逗号分割,定义排序',
    `company_id`    int                                     DEFAULT NULL COMMENT '公司id',
    `project_id`    int                                     DEFAULT NULL COMMENT '项目id',
    `department_id` int                                     DEFAULT NULL COMMENT '组织id',
    `create_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '创建者id',
    `update_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '修改者id',
    `create_time`   datetime                                DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                DEFAULT NULL COMMENT '修改时间',
    `is_deleted`    tinyint unsigned               NOT NULL DEFAULT '0' COMMENT '逻辑删除标记 0 正常 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='统计配置表-物料';

CREATE TABLE `s_statistics_material_group`
(
    `id`            varchar(32) CHARACTER SET utf8 NOT NULL COMMENT 'id',
    `name`          varchar(128) CHARACTER SET utf8         DEFAULT NULL COMMENT '默认分类名称',
    `company_id`    int                                     DEFAULT NULL COMMENT '公司id',
    `project_id`    int                                     DEFAULT NULL COMMENT '项目id',
    `department_id` int                                     DEFAULT NULL COMMENT '组织id',
    `create_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '创建者id',
    `update_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '修改者id',
    `create_time`   datetime                                DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                DEFAULT NULL COMMENT '修改时间',
    `is_deleted`    tinyint unsigned               NOT NULL DEFAULT '0' COMMENT '逻辑删除标记 0 正常 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='统计配置表-物料默认分类删除标记';

CREATE TABLE `s_statistics_project_config`
(
    `id`                   varchar(32) CHARACTER SET utf8 NOT NULL COMMENT 'id',
    `belong_project_id`    text CHARACTER SET utf8 COMMENT '统计项目id列表',
    `belong_department_id` text CHARACTER SET utf8 COMMENT '统计企业id列表',
    `status`               varchar(64) CHARACTER SET utf8          DEFAULT NULL COMMENT '项目状态: 1-在建,2-完工,5-筹备,6-立项,7-停工 ',
    `company_id`           int                                     DEFAULT NULL COMMENT '公司id',
    `project_id`           int                                     DEFAULT NULL COMMENT '项目id',
    `department_id`        int                                     DEFAULT NULL COMMENT '组织id',
    `create_id`            varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '创建者id',
    `update_id`            varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '修改者id',
    `create_time`          datetime                                DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime                                DEFAULT NULL COMMENT '修改时间',
    `is_deleted`           tinyint unsigned               NOT NULL DEFAULT '0' COMMENT '逻辑删除标记 0 正常； 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='统计配置表-项目';

CREATE TABLE `s_statistics_use_config`
(
    `id`            varchar(32) CHARACTER SET utf8 NOT NULL COMMENT 'id',
    `day`           int                                     DEFAULT NULL COMMENT '使用率: 天数',
    `company_id`    int                                     DEFAULT NULL COMMENT '公司id',
    `project_id`    int                                     DEFAULT NULL COMMENT '项目id',
    `department_id` int                                     DEFAULT NULL COMMENT '组织id',
    `create_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '创建者id',
    `update_id`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '修改者id',
    `create_time`   datetime                                DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                DEFAULT NULL COMMENT '修改时间',
    `is_deleted`    tinyint unsigned               NOT NULL DEFAULT '0' COMMENT '逻辑删除标记 0 正常； 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='统计配置表-使用率';