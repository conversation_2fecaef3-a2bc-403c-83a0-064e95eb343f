CREATE TABLE "s_statistics_material_config"
(
    "id"            VARCHAR(32) NOT NULL,
    "name"          VARCHAR(128) DEFAULT NULL,
    "category_id"   TEXT DEFAULT NULL,
    "company_id"    INTEGER DEFAULT NULL,
    "project_id"    INTEGER DEFAULT NULL,
    "department_id" INTEGER DEFAULT NULL,
    "create_id"     VARCHAR(32) DEFAULT NULL,
    "update_id"     VARCHAR(32) DEFAULT NULL,
    "create_time"   TIMESTAMP DEFAULT NULL,
    "update_time"   TIMESTAMP DEFAULT NULL,
    "is_deleted"    SMALLINT NOT NULL DEFAULT 0,
    PRIMARY KEY ("id")
);

COMMENT ON TABLE "s_statistics_material_config" IS '统计配置表-物料';
COMMENT ON COLUMN "s_statistics_material_config"."id" IS 'id';
COMMENT ON COLUMN "s_statistics_material_config"."name" IS '名称';
COMMENT ON COLUMN "s_statistics_material_config"."category_id" IS '二级分类id列表,以逗号分割,定义排序';
COMMENT ON COLUMN "s_statistics_material_config"."company_id" IS '公司id';
COMMENT ON COLUMN "s_statistics_material_config"."project_id" IS '项目id';
COMMENT ON COLUMN "s_statistics_material_config"."department_id" IS '组织id';
COMMENT ON COLUMN "s_statistics_material_config"."create_id" IS '创建者id';
COMMENT ON COLUMN "s_statistics_material_config"."update_id" IS '修改者id';
COMMENT ON COLUMN "s_statistics_material_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "s_statistics_material_config"."update_time" IS '修改时间';
COMMENT ON COLUMN "s_statistics_material_config"."is_deleted" IS '逻辑删除标记 0 正常 1删除';


CREATE TABLE "s_statistics_material_group"
(
    "id"            VARCHAR(32) NOT NULL,
    "name"          VARCHAR(128) DEFAULT NULL,
    "company_id"    INTEGER DEFAULT NULL,
    "project_id"    INTEGER DEFAULT NULL,
    "department_id" INTEGER DEFAULT NULL,
    "create_id"     VARCHAR(32) DEFAULT NULL,
    "update_id"     VARCHAR(32) DEFAULT NULL,
    "create_time"   TIMESTAMP DEFAULT NULL,
    "update_time"   TIMESTAMP DEFAULT NULL,
    "is_deleted"    SMALLINT NOT NULL DEFAULT 0,
    PRIMARY KEY ("id")
);

COMMENT ON TABLE "s_statistics_material_group" IS '统计配置表-物料默认分类删除标记';
COMMENT ON COLUMN "s_statistics_material_group"."id" IS 'id';
COMMENT ON COLUMN "s_statistics_material_group"."name" IS '默认分类名称';
COMMENT ON COLUMN "s_statistics_material_group"."company_id" IS '公司id';
COMMENT ON COLUMN "s_statistics_material_group"."project_id" IS '项目id';
COMMENT ON COLUMN "s_statistics_material_group"."department_id" IS '组织id';
COMMENT ON COLUMN "s_statistics_material_group"."create_id" IS '创建者id';
COMMENT ON COLUMN "s_statistics_material_group"."update_id" IS '修改者id';
COMMENT ON COLUMN "s_statistics_material_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "s_statistics_material_group"."update_time" IS '修改时间';
COMMENT ON COLUMN "s_statistics_material_group"."is_deleted" IS '逻辑删除标记 0 正常 1删除';


CREATE TABLE "s_statistics_project_config"
(
    "id"                   VARCHAR(32) NOT NULL,
    "belong_project_id"    TEXT DEFAULT NULL,
    "belong_department_id" TEXT DEFAULT NULL,
    "status"               VARCHAR(64) DEFAULT NULL,
    "company_id"           INTEGER DEFAULT NULL,
    "project_id"           INTEGER DEFAULT NULL,
    "department_id"        INTEGER DEFAULT NULL,
    "create_id"            VARCHAR(32) DEFAULT NULL,
    "update_id"            VARCHAR(32) DEFAULT NULL,
    "create_time"          TIMESTAMP DEFAULT NULL,
    "update_time"          TIMESTAMP DEFAULT NULL,
    "is_deleted"           SMALLINT NOT NULL DEFAULT 0,
    PRIMARY KEY ("id")
);

COMMENT ON TABLE "s_statistics_project_config" IS '统计配置表-项目';
COMMENT ON COLUMN "s_statistics_project_config"."id" IS 'id';
COMMENT ON COLUMN "s_statistics_project_config"."belong_project_id" IS '统计项目id列表';
COMMENT ON COLUMN "s_statistics_project_config"."belong_department_id" IS '统计企业id列表';
COMMENT ON COLUMN "s_statistics_project_config"."status" IS '项目状态: 1-在建,2-完工,5-筹备,6-立项,7-停工';
COMMENT ON COLUMN "s_statistics_project_config"."company_id" IS '公司id';
COMMENT ON COLUMN "s_statistics_project_config"."project_id" IS '项目id';
COMMENT ON COLUMN "s_statistics_project_config"."department_id" IS '组织id';
COMMENT ON COLUMN "s_statistics_project_config"."create_id" IS '创建者id';
COMMENT ON COLUMN "s_statistics_project_config"."update_id" IS '修改者id';
COMMENT ON COLUMN "s_statistics_project_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "s_statistics_project_config"."update_time" IS '修改时间';
COMMENT ON COLUMN "s_statistics_project_config"."is_deleted" IS '逻辑删除标记 0 正常； 1删除';


CREATE TABLE "s_statistics_use_config"
(
    "id"            VARCHAR(32) NOT NULL,
    "day"           INTEGER DEFAULT NULL,
    "company_id"    INTEGER DEFAULT NULL,
    "project_id"    INTEGER DEFAULT NULL,
    "department_id" INTEGER DEFAULT NULL,
    "create_id"     VARCHAR(32) DEFAULT NULL,
    "update_id"     VARCHAR(32) DEFAULT NULL,
    "create_time"   TIMESTAMP DEFAULT NULL,
    "update_time"   TIMESTAMP DEFAULT NULL,
    "is_deleted"    SMALLINT NOT NULL DEFAULT 0,
    PRIMARY KEY ("id")
);

COMMENT ON TABLE "s_statistics_use_config" IS '统计配置表-使用率';
COMMENT ON COLUMN "s_statistics_use_config"."id" IS 'id';
COMMENT ON COLUMN "s_statistics_use_config"."day" IS '使用率: 天数';
COMMENT ON COLUMN "s_statistics_use_config"."company_id" IS '公司id';
COMMENT ON COLUMN "s_statistics_use_config"."project_id" IS '项目id';
COMMENT ON COLUMN "s_statistics_use_config"."department_id" IS '组织id';
COMMENT ON COLUMN "s_statistics_use_config"."create_id" IS '创建者id';
COMMENT ON COLUMN "s_statistics_use_config"."update_id" IS '修改者id';
COMMENT ON COLUMN "s_statistics_use_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "s_statistics_use_config"."update_time" IS '修改时间';
COMMENT ON COLUMN "s_statistics_use_config"."is_deleted" IS '逻辑删除标记 0 正常； 1删除';
