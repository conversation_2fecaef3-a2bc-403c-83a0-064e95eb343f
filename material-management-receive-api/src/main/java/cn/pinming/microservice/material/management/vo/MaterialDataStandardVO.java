package cn.pinming.microservice.material.management.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MaterialDataStandardVO implements Serializable {
    @ApiModelProperty(value = "称重明细id")
    private String id;

    @ApiModelProperty(value = "过磅类型 1 收料 2 发料")
    private Byte type;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供  8 自采 9 集采       发料:4 发料  5 废旧物料  6 调出  7 售出  10 调拨出库 11 领用出库 12 废品处置 13 余料处理 14 其他  ")
    private Byte typeDetail;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "合同明细id")
    private String contractDetailId;

    @ApiModelProperty(value = "采购单ID")
    private String purchaseOrderId;

    @ApiModelProperty(value = "采购单明细id")
    private String purchaseOrderDetailId;

    @ApiModelProperty(value = "材料ID")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "二级分类id")
    private Integer categoryId;

    @ApiModelProperty(value = "结算单位")
    private String unit;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "随车面单数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "偏差状态(0 正常 1 负偏差 2 正偏差)")
    private Byte deviationStatus;

    @ApiModelProperty(value = "出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "收货/发货时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "备注")
    private String remark;
}
