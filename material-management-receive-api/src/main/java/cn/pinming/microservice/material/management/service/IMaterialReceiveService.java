package cn.pinming.microservice.material.management.service;

import cn.pinming.microservice.material.management.vo.MaterialDataStandardVO;

import java.util.List;

public interface IMaterialReceiveService {
    List<MaterialDataStandardVO> getDataByExtNo(List<String> extNoList);

    List<MaterialDataStandardVO> getDataByIds(List<String> ids);

    List<MaterialDataStandardVO> getDataByPurchaseId(String purchaseOrderId);

    List<String> getReceiveIdByExtNo(Integer projectId, String extNo);

}
